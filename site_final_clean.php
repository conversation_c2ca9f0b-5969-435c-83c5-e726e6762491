<?php
defined('IN_IA') or exit('Access Denied');

function dayu_form_field_district($name, $values = array()) {$html .= ' {

    if (!empty($values['city'])) {}

    if (!(empty($values) || !is_array($values))) {}
    require([\"jquery\", \"district\"], function($, dis){
        <div class="tpl-district-container" style="block;">
        <div class="col-lg-4">
        <select name="' . $name . '[province]" data-value="' . $values['province'] . '" class="tpl-province">
        </select><i></i>
        </div>
        <div class="col-lg-4">
        <select name="' . $name . '[city]" data-value="' . $values['city'] . '" class="tpl-city">
        </select><i></i>
        </div>
        <div class="col-lg-4">
        <select name="' . $name . '[district]" data-value="' . $values['district'] . '" class="tpl-district">
        </select><i></i>
        </div>

        </div>'; $values = array("province" => "", "city" => "", "district" => ""); $values['city'] = ''; if (!empty($values['province'])) {}
        $html .= '
        <script type=\"text/javascript\">
        $(\".tpl-district-container\").each(function(){
            var elms = {};
            elms.province = $(this).find(\".tpl-province\")[0];
            elms.city = $(this).find(\".tpl-city\")[0];
            elms.district = $(this).find(\".tpl-district\")[0];
            var vals = {};
            vals.province = $(elms.province).attr(\"data-value\");
            vals.city = $(elms.city).attr(\"data-value\");
            vals.district = $(elms.district).attr(\"data-value\");
            dis.render(elms, vals, {withTitle: true});
        });
    });

    </script>'; define('TPL_INIT_DISTRICT', true); if (defined('TPL_INIT_DISTRICT')) {}

    $values['province'] = ''; $values['district'] = ''; if (!empty($values['district'])) {}
    return $html; $html = ''; }
}

function tpl_form_field_images2($name, $value, $title) {$thumb = empty($value) ? 'images/global/nopic.jpg' : $value; return $html; $thumb = tomedia($thumb); $html = "	<li class=\"mui-table-view-cell mui-media mui-col-xs-6\"> {
    <a href=\";\" class=\"js-image-{$name}\">
    <span class=\"js-image-{$name}s\"><img class=\"mui-media-object\" src=\"{$thumb}\"></span>
    <div class=\"mui-media-body\">
    <input type=\"hidden\" id=\"{$name}\">
    <input class=\"weui_uploader_input\" type=\"file\" name=\"{$name}\" accept=\"image/*\" capture=\"camera\" value=\"{$title}\"></div>
    </a>
    </li>
    <script>
    util.image(\$('.js-image-{$name}'), function(url){
        \$('.js-image-{$name}').prev().val(url.attachment);
        \$('.js-image-{$name}s').find('img').attr('src',url.url);
    }, {
        crop : false,
        multiple : false
    });
    </script>"; }
}

define('TEMPLATE_WEUI', '../addons/dayu_form/template/weui/'); define('TEMPLATE_PATH', '../addons/dayu_form/template/style/'); define('MODULE_NAME', 'dayu_form'); require IA_ROOT . '/addons/dayu_form/inc/func/core.php'; function notice_init() {return error(-1, '创建公众号操作对象失败'); if (!is_null($acc)) {}
$acc = WeAccount::create(); global $_W; return $acc; }

function dayu_fans_form($field, $value = "") {switch ($field) {
    case 'reside': case 'resideprovince': case 'residecity': case 'residedist': $html = dayu_form_field_district('reside', $value);}
    return $html; }

    class dayu_formModuleSite extends Core { function __construct() {$this->_openid = $_COOKIE[$this->_auth2_openid]; $this->_appid = $this->_account['key']; $this->_appid = $_W['account']['key']; $oauth = $settings['oauth']; $this->_appsecret = $_W['account']['secret']; if (!(!empty($oauth) && !empty($oauth['account']))) {}

    $this->_appid = $_W['account']['key']; $this->_accountlevel = $account['level']; $this->_auth2_headimgurl = 'auth2_headimgurl_' . $_W['uniacid']; $this->_appsecret = $_W['account']['secret']; $this->_auth2_openid = 'auth2_openid_' . $_W['uniacid']; load()->model('mc'); $this->_appsecret = $this->_account['secret']; $this->_auth2_nickname = 'auth2_nickname_' . $_W['uniacid']; $account = $_W['account']; global $_W, $_GPC; $this->_weid = $_W['uniacid']; $this->_account = account_fetch($oauth['account']); if (!isset($_COOKIE[$this->_auth2_openid])) {}
    $this->_openid = $_W['openid'];
    $settings = uni_setting($this->_weid); }

    public function oauth2($url) {header("Location: " . $oauth2_code); global $_GPC, $_W; $userinfo = $this->get_User_Info($from_user); $code = $_GPC['code']; if (!(empty($userinfo) || !is_array($userinfo) || empty($userinfo['openid']) || empty($userinfo['nickname']))) {} {

        $userinfo = $this->get_User_Info($from_user, $token['access_token']); setcookie($this->_auth2_openid, $from_user, time() + 3600 * 24); if (!empty($code)) {}
        load()->func('communication'); $authkey = intval($_GPC['authkey']);

        return $userinfo; $this->showMessage('code获取失败.', '', '', '', ''); setcookie($this->_auth2_nickname, $userinfo['nickname'], time() + 3600 * 24); $state = 0; $from_user = $token['openid']; $state = 1; $token = $this->get_Authorization_Code($code, $url); echo '<h1>获取微信公众号授权失败[无法取得粉丝信息], 请稍后重试！ 公众平台返回原始数据: <br />' . $state . $userinfo['meta'] . '<h1>'; setcookie($this->_auth2_headimgurl, $userinfo['headimgurl'], time() + 3600 * 24); setcookie($this->_auth2_sex, $userinfo['sex'], time() + 3600 * 24); $oauth2_code = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' . $this->_appid . '&redirect_uri=' . urlencode($url) . '&response_type=code&scope=snsapi_userinfo&state=0#wechat_redirect'; exit; if ($authkey == 0) {
        }

        public function get_Access_Token() {load()->class('weixin.account'); return $access_token; $account = $_W['account']; $account = $this->_account; global $_W; $access_token = $accObj->fetch_token(); if ($this->_accountlevel < 4) { {
            $acc = WeAccount::create($account['acid']); }

            if (empty($this->_account)) {}
        }

        public function get_Authorization_Code($code, $url) {echo '微信授权失败! 公众平台返回原始数据: <br>' . $error['meta']; $oauth2_code = "https://api.weixin.qq.com/sns/oauth2/access_token?appid={$this->_appid}&secret={$this->_appsecret}&code={$code}&grant_type=authorization_code"; header("Location: " . $oauth2_code); $error = ihttp_get($oauth2_code); $token = @json_decode($error['content'], true); exit; if (!(empty($token) || !is_array($token) || empty($token['access_token']) || empty($token['openid']))) {}
        return $token; $oauth2_code = $url; }

        public function get_User_Info($from_user, $ACCESS_TOKEN = "") {$json = ihttp_get($url); $url = "https://api.weixin.qq.com/sns/userinfo?access_token={$ACCESS_TOKEN}&openid={$from_user}&lang=zh_CN";
        return $userinfo; $userinfo = @json_decode($json['content'], true); $url = "https://api.weixin.qq.com/cgi-bin/user/info?access_token={$ACCESS_TOKEN}&openid={$from_user}&lang=zh_CN"; $ACCESS_TOKEN = $this->get_Access_Token(); }

        public function get_Code($url) {global $_W; header("Location: " . $oauth2_code); $url = urlencode($url); $oauth2_code = "https://open.weixin.qq.com/connect/oauth2/authorize?appid={$this->_appid}&redirect_uri={$url}&response_type=code&scope=snsapi_base&state=0#wechat_redirect"; }

        global $_W; return $urls; $list = pdo_fetchall('SELECT title, reid FROM ' . tablename($this->tb_form) . " WHERE weid = '{$_W['uniacid']}'"); $urls = array(); foreach ($list as $row) { $urls[] = array("title" => $row['title'], "url" => $_W['siteroot'] . 'app/' . $this->createMobileUrl('dayu_form', array("id" => $row['reid']))); }}

        public function __call($name, $arguments) {$fromurl = urldecode($_GPC['fromurl']); $avatar = $_W['attachurl'] . 'images/global/noavatar_middle.gif'; $isMobile = stripos($name, 'doMobile') === 0; if (!empty($profile['avatar'])) {} {

            $fun = strtolower(substr($name, 5)); $id = $_GPC['id']; $dir .= 'web/'; $op = $operation = trim($_GPC['op']) ? trim($_GPC['op']) : 'display'; require MODULE_ROOT . '/fans.web.php'; $dayuset = $this->module['config']; $avatar = $fans['tag']['avatar']; if (tomedia('headimg_' . $_W['acid'] . '.jpg')) {}
            $file = $dir . $fun . '.php'; return null;
            $fun = strtolower(substr($name, 8)); $dir = MODULE_ROOT . '/inc/'; require MODULE_ROOT . '/fans.mobile.php';

            if (!empty($fans['tag']['avatar'])) {}

            if (!file_exists($file)) {}

            exit; $avatar = $profile['avatar']; $avatar = tomedia('headimg_' . $_W['acid'] . '.jpg'); if ($isWeb || $isMobile) {
        $dir .= 'mobile/'; load()->func('tpl'); require $file; trigger_error("访问的方法 {$name} 不存在.", E_USER_WARNING); $weid = $_W['uniacid']; $isWeb = stripos($name, 'doWeb') === 0; $returnUrl = urlencode($_W['siteurl']); global $_W, $_GPC; }
            }

            public function doWebFansSearch() {$params[':nickname'] = "%{$kwd}%"; include $this->template('fanssearch'); $sql = 'SELECT * FROM ' . tablename('mc_mapping_fans') . " WHERE {$where}ORDER BY fanid DESC LIMIT 20"; $pindex = max(1, intval($_GPC['page'])); $psize = 20; $params[':uniacid'] = $_W['uniacid']; $where = 'uniacid = :uniacid AND `nickname` LIKE :nickname'; $boss = pdo_fetchall($sql, $params); load()->model('mc'); global $_W, $_GPC; foreach ($boss as &$row) {$row['fans'] = mc_fansinfo($row['openid'], $_W['uniacid']); $r['openid'] = $row['openid']; $row['entry'] = $r; $r['follow'] = $row['follow']; $r['fanid'] = $row['fanid']; $r = array(); $r['nickname'] = $row['nickname']; }
            $kwd = $_GPC['keyword']; }

            public function doWebQuery() {$ds = pdo_fetchall($sql, $params); $kwd = $_GPC['keyword']; $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `title` LIKE :title ORDER BY reid DESC LIMIT 0,8'; global $_W, $_GPC; foreach ($ds as &$row) {$r['thumb'] = $row['thumb']; $r = array(); $row['entry'] = $r; $r['title'] = $row['title']; $r['description'] = cutstr(strip_tags($row['description']), 50); $r['reid'] = $row['reid']; }
            $params[':weid'] = $_W['uniacid']; $params[':title'] = "%{$kwd}%"; $params = array(); include $this->template('query'); }

            public function doWebLinkage() {$linkage = pdo_fetch('SELECT * FROM ' . tablename($this->tb_linkage) . " WHERE id = '{$id}'"); message('更新联动成功！', $this->createWebUrl('linkage', array("op" => "display", "reid" => $reid)), 'success'); if (!empty($parent)) {} {

        $id = intval($_GPC['id']); $children = array(); $data = array("reid" => $reid, "title" => $_GPC['title'], "parentid" => intval($parentid), "displayorder" => intval($_GPC['displayorder'])); $record['linkage'] = iserializer($data); message('您没有权限进行该操作.'); foreach ($_GPC['displayorder'] as $id => $displayorder) { pdo_update($this->tb_linkage, array("displayorder" => $displayorder), array("id" => $id)); }

        $linkage = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_linkage) . " WHERE reid = '{$reid}' ORDER BY parentid ASC, displayorder desc"); message('保存成功', $this->createWebUrl('linkage', array("op" => "display", "reid" => $reid)), 'success'); pdo_update($this->tb_linkage, $data, array("id" => $id)); itoast('联动删除成功！', referer(), 'success'); message('抱歉，请输入联动标题！'); $linkage = array("displayorder" => 0); message('联动排序更新成功！', $this->createWebUrl('linkage', array("op" => "display", "reid" => $reid)), 'success'); $reid = intval($_GPC['reid']); $parent = pdo_fetch('SELECT id, title FROM ' . tablename($this->tb_linkage) . " WHERE id = '{$parentid}'"); if (!empty($linkage)) {}

        global $_GPC, $_W; $id = intval($_GPC['id']); if (!empty($id)) {}
        $id = pdo_insertid(); pdo_delete($this->tb_linkage, array("id" => $id));

        $record = array(); pdo_update($this->tb_form, $record, array("reid" => $reid)); if ($setting['role'] == 1 && $_W['role'] == 'operator' && !$role) {

        $operation = !empty($_GPC['op']) ? $_GPC['op'] : 'display'; if (!checksubmit('submit')) {}
        $activity = pdo_get($this->tb_form, array("weid" => $_W['uniacid'], "reid" => $reid), array("title", "linkage")); pdo_insert($this->tb_linkage, $data); }

        if (!checksubmit('paixu')) {}

        foreach ($linkage as $index => $item) {$children[$item['parentid']][] = $item; if (empty($item['parentid'])) {}

        if (!checksubmit('submit')) {}

        if (empty($parentid)) {}
        unset($linkage[$index]); }

        unset($data['parentid']); if (!empty($id)) {}

        include $this->template('linkage'); $data = array("l1" => $_GPC['la1'], "l2" => $_GPC['la2']); itoast('抱歉，联动不存在或是已经被删除！', referer(), 'error'); $parentid = intval($_GPC['parentid']); $linkage = pdo_fetch('SELECT * FROM ' . tablename($this->tb_linkage) . " WHERE id = '{$id}'"); $role = $this->get_isrole($reid, $_W['user']['uid']); include $this->template('linkage'); $la = iunserializer($activity['linkage']); require MODULE_ROOT . '/fans.web.php'; load()->func('tpl'); message('抱歉，上级联动不存在或是已经被删除！', $this->createWebUrl('linkage', array("op" => "post", "reid" => $reid)), 'error'); if (!empty($_GPC['title'])) {}
        }

        public function doMobileGetLinkage() {message($result, '', 'ajax'); message($result, '', 'ajax'); $result['status'] = 1; $result['status'] = 0; $result['jss'] = '没有下级内容'; if (!empty($jss)) {}
        $result['jss'] = $jss; $jss = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_linkage) . ' WHERE parentid = :parentid ORDER BY displayorder desc, id DESC', array(":parentid" => $_GPC['linkage1'])); global $_GPC, $_W; }

        public function doWebStaff() {$total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_staff) . ' WHERE ' . $where, $params);  {

        global $_W, $_GPC; if (!checksubmit('submit')) {}

        require MODULE_ROOT . '/fans.web.php'; if (empty($id)) {}

        $lists = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_staff) . ' WHERE ' . $where . ' ORDER BY createtime DESC,id ASC LIMIT ' . ($pindex - 1) * $psize . ',' . $psize, $params, 'id'); $psize = 20; $data['weid'] = $_GPC['weid']; pdo_insert('dayu_form_staff', $data); $data['createtime'] = time(); $where = ' reid = :reid'; $reid = intval($_GPC['reid']); if (!empty($activity)) {}

        if ($setting['role'] == 1 && $_W['role'] == 'operator' && !$role) {

        foreach ($_GPC['ids'] as $k => $v) {$data = array("nickname" => trim($_GPC['nickname'][$k]), "openid" => trim($_GPC['openid'][$k]), "weid" => trim($_GPC['weid'][$k])); pdo_update('dayu_form_staff', $data, array("reid" => $reid, "id" => intval($v))); }
        itoast('添加客服成功', $this->createWebUrl('staff', array("reid" => $reid, "op" => "list")), 'success'); pdo_delete('dayu_form_staff', array("id" => $id)); $activity = $this->get_form($reid); $role = $this->get_isrole($reid, $_W['user']['uid']); $pindex = max(1, intval($_GPC['page'])); $id = intval($_GPC['id']); }

        include $this->template('staff'); $data['nickname'] = $_GPC['nickname']; $params[':reid'] = $reid; message('表单不存在或已删除', $this->createWebUrl('display'), 'error'); $pager = pagination($total, $pindex, $psize); if (!checksubmit('submit')) {}
        include $this->template('staff');

        $data['openid'] = $_GPC['openid']; itoast('编辑成功', $this->createWebUrl('staff', array("op" => "list", "reid" => $reid)), 'success'); message('您没有权限进行该操作.'); $data['reid'] = $reid; itoast('删除成功.', referer()); if (empty($_GPC['keyword'])) {}

        $where .= " AND nickname LIKE '%{$_GPC['keyword']}%'"; $weid = $_W['uniacid']; $op = trim($_GPC['op']) ? trim($_GPC['op']) : 'list'; if (empty($_GPC['ids'])) {}
        }

        public function doWebchangecheckedAjax() {exit('0'); message('您没有权限进行该操作.'); if ($setting['role'] == 1 && $_W['role'] == 'operator' && !$role) { {

        $change = $_GPC['change']; if (false !== pdo_update($this->tb_form, array($field => $change), array("reid" => intval($id), "weid" => $_W['uniacid']))) {}
        require MODULE_ROOT . '/fans.web.php'; $id = $_GPC['id']; exit('1'); global $_W, $_GPC; $role = $this->get_isrole($id, $_W['user']['uid']); $field = $_GPC['field']; }
        }

        public function doWebEditkf() {exit; global $_W, $_GPC; $openid = $_GPC['openid']; $nickname = $_GPC['nickname']; $fff = pdo_fetchall('SELECT reid,title FROM ' . tablename($this->tb_form)); include $this->template('kf_edit'); if (!is_array($reid)) {} {
        $a = pdo_update('dayu_form_staff', array("reid" => $actid, "nickname" => $nickname, "openid" => $openid), array("id" => $_GPC['id'])); }

        if ($setting['role'] == 1 && $_W['role'] == 'operator' && !$role) {

        message('更改成功!', referer()); $role = $this->get_isrole($reid, $_W['user']['uid']); $fun = explode(',', $config['reid']); require MODULE_ROOT . '/fans.web.php'; $config = pdo_fetch('SELECT * from ' . tablename($this->tb_staff) . ' where id=' . $_GPC['id']); $actid = substr($actid, 0, strlen($actid) - 1); $reid = $_GPC['reid']; if ($_GPC['dopost'] == 'update') {

        message('您没有权限进行该操作.'); foreach ($reid as $k => $v) { $actid = $v . ','; }
        }

        public function doWebDetail() {mc_group_update(mc_openid2uid($row['openid'])); $sms_data = array("mobile" => $row['mobile'], "title" => $activity['title'], "mname" => $row['member'], "mmobile" => $row['mobile'], "openid" => $row['openid'], "status" => $state['name'], "ptname" => $_W['account']['name'], "addons" => $_W['current_module']['name'], "content" => $alldata); $row = pdo_fetch($sql, $params); $linkage['l1'] = $this->get_linkage($linkage['l1'], ''); $kami = pdo_get('dayu_sendkami', array("weid" => $_W['uniacid'], "id" => $row['kid']), array()); message('访问非法.'); $huifu = $state['name'] . $kfinfo . $revoice; $acc = WeAccount::create($_W['acid']); $kami = pdo_get('dayu_kami', array("weid" => $_W['uniacid'], "id" => $row['kid']), array()); $info .= "<a href='{$url}'>现在去评价</a>"; $record['icredit'] = 1; $linkage = iunserializer($row['linkage']); if (!is_array($_GPC['rethumb'])) {} {
        $acc = notice_init(); $kfinfo = !empty($_GPC['kfinfo']) ? '

        $thumb1 = unserialize($row['rethumb']); $_W['page']['title'] = $activity['title'] . ' 表单详情'; if ($row['icredit'] != '1' && $par['icredit'] == '1' && $activity['credit'] != '0.00' && $_GPC['status'] == '3') {
        $testfile = $_FILES['upfile']; $info = '【您好，受理结果通知】

        $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `reid`=:reid'; if ($_GPC['status'] == '3' && $par['icredit'] == '1') {

        $status = $this->get_status($reid, $_GPC['status']); $settings = $this->module['config']; $params[':weid'] = $_W['uniacid']; $row['member'] = !empty($row['member']) ? $row['member'] : $row['user']['nickname']; if ($activity['custom_status'] == 1) {

        $msg = ''; if (!is_array($linkage)) {}

        $params = array(); ihttp_post(murl('entry', array("do" => "Notice", "id" => $activity['smstype'], "m" => "dayu_sms"), true, true), $sms_data); $CustomNotice = $acc->sendCustomNotice($custom); $sql = 'SELECT * FROM ' . tablename($this->tb_info) . ' WHERE `rerid`=:rerid'; if (!($activity['custom_status'] == '0' && !empty($activity['m_templateid']))) {}

        $state = array(); if (!(!empty($par['comment']) && pdo_tableexists('dayu_comment') && !empty($row['commentid']))) {}
        $info = $activity['title'] . ' 处理结果 ' . $status['name'] . '

        load()->func('tpl'); if (empty($row['thumb'])) {}

        $url = $outurl; if (!empty($row)) {}

        $arr2 = array("0", "1", "2", "3", "8", "7"); require MODULE_ROOT . '/fans.web.php'; $record['kfinfo'] = $_GPC['kfinfo']; $params[':reid'] = $row['reid']; if (!($par['sms'] != '0' && $par['paixu'] != '2' && !empty($activity['smstype']))) {}
        $ytime = date('Y-m-d s', TIMESTAMP); $rerid = intval($_GPC['id']); $row['revoices'] = $row['revoice']; }

        if (!(pdo_tableexists('dayu_comment') && !empty($par['comment']) && empty($row['commentid']) && $_GPC['status'] == '3')) {}

        if (!checksubmit('submit')) {}

        if (!is_error($status)) {}

        if (empty($_GPC['file'])) {}

        if (!is_array($wxcard)) {}

        itoast('修改成功', referer(), 'success'); if (!empty($activity)) {}

        message('发送失败，原因为' . $status['message']); $activity = pdo_fetch($sql, $params); $CustomNotice = $acc->sendCustomNotice($custom); $custom = array("msgtype" => "text", "text" => array("content" => urlencode($info))); $acc = WeAccount::create($_W['acid']); foreach ($thumb1 as $p) { $rethumb[] = is_array($p) ? $p['attachment'] : $p; }

        mc_notice_credit1($row['openid'], mc_openid2uid($row['openid']), $activity['credit'], $log); pdo_update('dayu_form_info', $record, array("rerid" => $rerid)); $comment = pdo_get('dayu_comment', array("weid" => $_W['uniacid'], "id" => $row['commentid']), array()); $par = iunserializer($activity['par']); foreach ($arr2 as $index => $v) { $state[$v][] = $this->get_status($row['reid'], $v); }

        客服回复：' . $_GPC['kfinfo'] : ''; foreach ($wxcard as &$val) { $val['coupon'] = pdo_get('coupon', array("uniacid" => $_W['uniacid'], "card_id" => $val['cardid']), array("id", "title")); }
        '; $row['user'] = mc_fansinfo($row['openid'], $acid, $weid); $status = $this->get_status($row['reid'], $row['status']); $record['yuyuetime'] = strtotime($_GPC['yuyuetime']); $data = array("first" => array("value" => $activity['mfirst'] . '
        ', "color" => "#743A3A"), "keyword1" => array("value" => $row['member']), "keyword2" => array("value" => $row['mobile']), "keyword3" => array("value" => $_GPC['yuyuetime']), "keyword4" => array("value" => $huifu), "remark" => array("value" => '

        ' . $activity['mfoot'], "color" => "#008000")); if (!(pdo_tableexists('dayu_sendkami') && !empty($par['sendkami']) && $row['kid'])) {}

        $record['status'] = intval($_GPC['status']); load()->func('communication'); foreach ($_GPC['rethumb'] as $thumb) { $th[] = tomedia($thumb); }

        $info .= "<a href='{$url}'>点击查看详情</a>"; $log = $activity['title'] . '-' . $activity['credit'] . '积分'; $settings = uni_setting($_W['uniacid'], array("creditnames", "creditbehaviors")); $state = $this->get_status($row['reid'], $_GPC['status']); $row['file'] = iunserializer($row['file']); $role = $this->get_isrole($row['reid'], $_W['user']['uid']); global $_W, $_GPC; $acc->sendTplNotice($row['openid'], $activity['m_templateid'], $data, $outurl, '#FF0000'); $custom = array("msgtype" => "text", "text" => array("content" => urlencode($info)), "touser" => $row['openid']); $outurl = !empty($par['noticeurl']) ? $par['noticeurl'] : $_W['siteroot'] . 'app/' . $this->createMobileUrl('mydayu_form', array("op" => "detail", "rerid" => $rerid, "id" => $row['reid'])); if ($setting['role'] == 1 && $_W['role'] == 'operator' && !$role) {
        $info .= "{$par['commenttitle']}

        "; $row['yuyuetime'] && ($row['yuyuetime'] = date('Y-m-d s', $row['yuyuetime'])); foreach ($_GPC['file'] as $file) { $th[] = $file; }
        $info .= "姓名：{$row['member']}
        手机：{$row['mobile']}

        受理结果：{$huifu}"; $la = iunserializer($activity['linkage']); if (!(!empty($par['wxcard']) && !empty($_GPC['wxcard']) && pdo_tableexists('dayu_wxcard'))) {}

        '; message('非法访问.'); $msg .= $wxcard_post['msg']; message('您没有权限进行该操作.'); load()->func('file'); $wxcard_post = ihttp_post(murl('entry', array("do" => "recorded", "couponid" => $_GPC['wxcard'], "m" => "dayu_wxcard"), true, true), array("addons" => $_W['current_module']['name'], "openid" => $row['openid'], "infoid" => $rerid)); $rethumb = array(); $record['rethumb'] = iserializer($th); $alldata = array(); if (!is_array($thumb1)) {}

        $record['file'] = iserializer($th); foreach ($formdata as $index => $v) {$alldata[] = $v['title'] . ':' . $v['data'] . ','; $formdata[$index]['data'] .= $fdata['data'];

        $row['voices'] = $row['voice']; if (!(!empty($par['wxcard']) && pdo_tableexists('dayu_wxcard_activity'))) {}

        $custom['touser'] = trim($row['openid']); $custom = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_custom) . ' WHERE weid = :weid ORDER BY displayorder DESC', array(":weid" => $_W['uniacid'])); $behavior = $settings['creditbehaviors']; $params[':rerid'] = $rerid; include $this->template('detail'); if (!is_error($acc)) {}

        return error(-1, $acc['message']); $linkage['l2'] = $this->get_linkage($linkage['l2'], ''); $params = array(); $wxcard = pdo_fetchall('SELECT * FROM ' . tablename('dayu_wxcard') . ' WHERE weid = :weid AND cid=:cid AND status=1', array(":weid" => $_W['uniacid'], ":cid" => $par['wxcard'])); $row['thumb'] = iunserializer($row['thumb']); $acc = WeAccount::create($_W['acid']); mc_credit_update(mc_openid2uid($row['openid']), $behavior['activity'], $activity['credit'], array(0, $activity['title'])); $formdata = $this->order_foreach ($row['reid'], $rerid); $url = $outurl; $record = array(); if (!(pdo_tableexists('dayu_kami') && !empty($par['kami']) && $row['kid'])) {}
        }

        public function doWebupfile() {$max_file_size = 2000000; global $_W, $_GPC; if ($max_file_size < $file['size']) { {

        $destination = $destination_folder . time() . '.' . $ftype; $pinfo = pathinfo($file['name']); exit; if (is_uploaded_file($_FILES['upfile'][tmp_name])) {}

        $ftype = $pinfo['extension']; $fname = $pinfo[basename]; exit; $image_size = getimagesize($filename); echo 'size'; if (!(file_exists($destination) && $overwrite != true)) {}

        mkdir($destination_folder); exit; if (file_exists($destination_folder)) {}

        echo 'name'; $destination_folder = ATTACHMENT_ROOT . 'dayu_form/' . $_W['uniacid'] . '/file/'; if (move_uploaded_file($filename, $destination)) {}
        echo $dest . $fname; $file = $_FILES['upfile']; $pinfo = pathinfo($destination); $filename = $file['tmp_name']; exit; echo 'move'; echo 'nothing'; $dest = '/attachment/dayu_form/' . $_W['uniacid'] . '/file/'; }
        }

        public function doWebManage() {$where = 'reid = :reid'; if (empty($list)) {} {
        $fields = pdo_fetchall($sql, $params, 'refid'); $params = array(); $par = iunserializer($activity['par']); $childlist = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_data) . ' WHERE rerid IN (\'' . implode('\',\'', is_array($rerid) ? $rerid : array($rerid)) . '\') AND `reid`=:reid', array(":reid" => $reid));
        $linkage = iunserializer($v['linkage']);
        $staff = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_staff) . ' WHERE reid = :reid ORDER BY `id` DESC', array(":reid" => $reid));
        $total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_info) . " WHERE {$where}", $params);
        $params = array(":reid" => $reid);
        $activity = pdo_get($this->tb_form, array("weid" => $_W['uniacid'], "reid" => $reid));
        $endtime = empty($_GPC['time']['end']) ? TIMESTAMP : strtotime($_GPC['time']['end']);
        $zuhe = array_merge($htmlheader, $ds, $htmlfoot);
        $ds = array();
        $etime = date('Ymd', $endtime);
        $rerid = array_keys($list);
        $reid = intval($_GPC['id']);
        require MODULE_ROOT . '/fans.web.php';
        global $_W, $_GPC;
        $params2 = array(":reid" => $reid);
        $where2 = 'a.reid = :reid';
        load()->model('mc');
        $htmlfoot = array("status" => "状态", "kfinfo" => "回复", "createtime" => "提交时间");
        $sql = 'SELECT * FROM ' . tablename($this->tb_info) . " WHERE {$where}ORDER BY createtime DESC LIMIT " . ($pindex - 1) * $psize . ',' . $psize;
        $allTotal = pdo_fetchall('SELECT `reid` FROM ' . tablename($this->tb_info) . ' WHERE reid = :reid GROUP BY openid', array(":reid" => $reid));
        $childrens = array();
        $stime = date('Ymd', $starttime);
        $html = $this->from_export_parse($zuhe, $childrens, $reid);
        $list = pdo_fetchall($sql, $params, 'rerid');
        $pindex = max(1, intval($_GPC['page']));
        $psize = 15;
        $la = iunserializer($activity['linkage']);
        $pager = pagination($total, $pindex, $psize);
        $v = $img . tomedia($v) . '" style="50px; 50px;"/>';
        $img = '<img src="';
        $children = array();
        $status = $_GPC['status'];
        $starttime = empty($_GPC['time']['start']) ? strtotime('-1 month') : strtotime($_GPC['time']['start']);
        $listall = pdo_fetchall('SELECT a.reid,a.rerid,a.member,a.mobile,a.openid,a.linkage,a.status,a.kfinfo,a.createtime,(SELECT GROUP_CONCAT(b.data ORDER BY b.displayorder desc) FROM ' . tablename($this->tb_data) . ' AS b WHERE b.rerid = a.rerid) data FROM ' . tablename($this->tb_info) . " AS a WHERE {$where2}ORDER BY a.rerid DESC", $params2);
        $sql = 'SELECT * FROM ' . tablename($this->tb_field) . ' WHERE `reid`=:reid ORDER BY displayorder desc';
        $role = $this->get_isrole($reid, $_W['user']['uid']);

        if (!empty($activity)) {}

        if (empty($_GPC['time'])) {}

        foreach ($listall as $index => $v) {foreach (explode(',', $v['data']) as $val) { $v[] = $val;

        if (!is_array($linkage)) {}}

        foreach ($list as &$r) {$r['kf'] = mc_fansinfo($r['kf'], $acid, $_W['uniacid']);

        if (empty($r['consult']['id'])) {}

        if (!pdo_tableexists('dayu_consult')) {}

        if (!checksubmit('export', true)) {}

        if (!empty($fields)) {}

        if (empty($_GPC['time'])) {}

        if ($status != '') {

        if (empty($_GPC['kf'])) {}

        foreach ($fields as $f) { $ds[$f['refid']] = $f['title'];

        if ($setting['role'] == 1 && $_W['role'] == 'operator' && !$role) {

        if ($status != '') {

        if (empty($_GPC['keywords'])) {}

        foreach ($list as $key => &$value) {unset($v);

        if (!is_array($value['fields'])) {}

        foreach ($value['fields'] as &$v) {if (!(strstr($field, 'images') || strstr($field, 'dayu_form'))) {}

        if (empty($_GPC['keywords'])) {}

        foreach ($childlist as $reply => $d) {unset($children[$reply]);

        if (empty($d['rerid'])) {}}}
        unset($v['linkage']);
        unset($v['data']);
        unset($v['link']);
        message('非法访问.');
        message('您没有权限进行该操作.');
        message('非法访问.');
        header('Content-text/csv');

        $where2 .= " and a.status='{$status}'"; header("Content-attachment; filename={$activity['title']}=={$stime}-{$etime}.csv"); $htmlheader = array("openid" => "粉丝编号", "member" => $activity['member'], "mobile" => $activity['phone']); if (empty($_GPC['kf'])) {}
        $where2 .= ' and (a.member like :member or a.mobile like :mobile)';
        $params2[':starttime'] = $starttime;
        $v['link']['l1'] = $this->get_linkage($linkage['l1'], '');
        $v['l2'] = $v['link']['l2']['title'];
        $v['link']['l2'] = $this->get_linkage($linkage['l2'], '');
        $v['l1'] = $v['link']['l1']['title'];
        $childrens[] = $v;
        $where .= ' AND createtime >= :starttime AND createtime <= :endtime ';
        $where .= " and status='{$status}'";
        $r['groupid'] = mc_fetch($r['user']['uid'], array("groupid"));
        $r['voices'] = strstr($r['voice'], '//') ? $r['voice'] : $setting['qiniu']['host'] . '/' . $r['voice'];
        $r['user'] = mc_fansinfo($r['openid']);
        $r['consult'] = pdo_fetch('SELECT id FROM ' . tablename('dayu_consult') . ' WHERE infoid = :infoid ORDER BY createtime DESC', array(":infoid" => $r['rerid']));
        $r['revoices'] = strstr($r['revoice'], '//') ? $r['revoice'] : $setting['qiniu']['host'] . '/' . $r['revoice'];
        $r['consultid'] = '1';
        $r['state'] = $this->get_status($r['reid'], $r['status']);
        $r['comment'] = !empty($par['comment']) && pdo_tableexists('dayu_comment') && !empty($r['commentid']) ? $this->get_comment($r['commentid']) : '';
        $where .= " and kf LIKE '%{$_GPC['kf']}%'";
        $params[':member'] = "%{$_GPC['keywords']}%";
        echo $html;
        $where2 .= ' AND a.createtime >= :starttime AND a.createtime <= :endtime ';
        $params[':starttime'] = $starttime;
        $params2[':mobile'] = "%{$_GPC['keywords']}%";
        $params2[':member'] = "%{$_GPC['keywords']}%";
        include $this->template('manage');
        $_W['page']['title'] = $activity['title'] . ' 记录管理';
        $params2[':endtime'] = $endtime;
        $where .= ' and (member like :member or mobile like :mobile)';
        exit;
        $params[':reid'] = $reid;
        $where2 .= " and a.kf LIKE '%{$_GPC['kf']}%'";
        $params[':mobile'] = "%{$_GPC['keywords']}%";
        }}
        $params[':endtime'] = $endtime;
        $children[$d['rerid']][] = $d;
        }

        public function doWebbatchrecord() {if (!empty($reply)) {} {
        $reid = intval($_GPC['reid']);
        require MODULE_ROOT . '/fans.web.php';
        $reply = pdo_fetch('select reid from ' . tablename($this->tb_form) . ' where reid = :reid', array(":reid" => $reid));
        global $_GPC, $_W;
        $rerid = intval($rerid);
        $role = $this->get_isrole($reid, $_W['user']['uid']);

        if ($setting['role'] == 1 && $_W['role'] == 'operator' && !$role) {

        foreach ($_GPC['idArr'] as $k => $rerid) {pdo_delete('dayu_form_info', array("rerid" => $rerid, "reid" => $reid));
        message('抱歉，表单主题不存在或是已经被删除！');
        message('记录删除成功！', '', 0);
        message('您没有权限进行该操作.');
        pdo_delete('dayu_form_data', array("rerid" => $rerid));
        }

        public function doWebupdategroup() {exit(json_encode(array("status" => "error", "mess" => $data['message']))); {
        $groupid = intval($_GPC['groupid']);
        global $_GPC, $_W;
        $data = $acc->updateFansGroupid($openid, $groupid);
        $openid = trim($_GPC['openid']);
        $acc = WeAccount::create($_W['acid']);

        if (is_error($data)) {}

        if (!empty($openid)) {}
        exit(json_encode(array("status" => "success")));
        pdo_update('mc_mapping_fans', array("groupid" => $groupid), array("uniacid" => $_W['uniacid'], "acid" => $_W['acid'], "openid" => $openid));
        exit(json_encode(array("status" => "error", "mess" => "粉丝openid错误")));
        }

        public function doWebDisplay() {message('复制表单出错', '', 'error'); {
        $op = trim($_GPC['op']) ? trim($_GPC['op']) : '';
        $params = array();
        $roleid = array_keys($role);
        $pager = pagination($total, $pindex, $psize);
        $id = intval($_GPC['id']);
        global $_W, $_GPC;
        $ds = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_form) . ' WHERE ' . $where . ' ORDER BY status DESC,reid DESC LIMIT ' . ($pindex - 1) * $psize . ',' . $psize, $params);
        $form = pdo_fetch('SELECT * FROM ' . tablename($this->tb_form) . ' WHERE weid = :weid AND reid = :reid', array(":weid" => $_W['uniacid'], ":reid" => $id));
        $reid = intval($_GPC['reid']);
        require MODULE_ROOT . '/fans.web.php';
        $role = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_role) . ' WHERE weid = :weid and roleid = :roleid  ORDER BY id DESC', array(":roleid" => $_W['user']['uid'], ":weid" => $weid), 'reid');
        $where = 'weid = :weid';
        $form_id = pdo_insertid();
        $var3 = '&' . $item['var3'] . '=变量3';
        $var2 = '&' . $item['var2'] . '=变量2';
        $total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_form) . ' WHERE ' . $where, $params);
        $psize = 10;
        $fields = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_field) . ' WHERE reid = :reid', array(":reid" => $id));
        $switch = intval($_GPC['switch']);
        $pindex = max(1, intval($_GPC['page']));
        $status = $_GPC['status'];
        $params = array(":weid" => $weid);
        $sql = 'UPDATE ' . tablename($this->tb_form) . ' SET `status`=:status WHERE `reid`=:reid';
        $cateid = intval($_GPC['formid']);
        $category = pdo_fetchall('SELECT id,title FROM ' . tablename($this->tb_category) . ' WHERE weid = :weid ORDER BY `id` DESC', array(":weid" => $_W['uniacid']));

        if (empty($_GPC['keyword'])) {}

        if (!empty($form)) {}

        if ($op == 'copy') {

        foreach ($fields as &$val) {unset($val['refid']);

        foreach ($ds as &$item) {$var1 = '&' . $item['var1'] . '=变量1';

        if (empty($item['var3'])) {}

        if (empty($item['var1'])) {}

        if (empty($item['var2'])) {}

        if ($status != '') {

        if (empty($fields)) {}

        if ($setting['role'] == 1 && $_W['role'] == 'operator') {
        unset($form['reid']);
        message('复制表单成功', $this->createWebUrl('display'), 'success');
        pdo_query($sql, $params);
        pdo_insert($this->tb_field, $val);
        message('表单不存在或已删除', referer(), 'error');
        pdo_insert($this->tb_form, $form);
        $where .= ' AND reid IN (\'' . implode('\',\'', is_array($roleid) ? $roleid : array($roleid)) . '\')';
        $params[':status'] = $switch;
        exit;
        $val['reid'] = $form_id;
        $_W['page']['title'] = '表单列表';
        $form['createtime'] = TIMESTAMP;
        $params[':reid'] = $reid;
        $item['cate'] = $this->get_category($item['cid']);
        $item['la'] = $this->get_linkage($item['reid'], 1);
        $item['record'] = $item['isget'] == 1 ? murl('entry', array("do" => "record", "id" => $item['reid'], "m" => "dayu_form"), true, true) . $var1 . $var2 . $var3 : murl('entry', array("do" => "record", "id" => $item['reid'], "m" => "dayu_form"), true, true);
        $item['isstart'] = $item['starttime'] > 0;
        $item['mylink'] = murl('entry', array("do" => "Mydayu_form", "id" => $item['reid'], "weid" => $item[weid], "m" => "dayu_form"), true, true);
        $item['par'] = iunserializer($item['par']);
        $item['isvar'] = $item['isget'] == 1 ? '<span class="btn btn-success btn-sm">启用</span>' : '<span class="btn btn-default btn-sm">关闭</span>';
        $item['link'] = $item['isget'] == 1 ? murl('entry', array("do" => "dayu_form", "id" => $item['reid'], "m" => "dayu_form"), true, true) . $var1 . $var2 . $var3 : murl('entry', array("do" => "dayu_form", "id" => $item['reid'], "m" => "dayu_form"), true, true);
        $item['switch'] = $item['status'];
        $item['role'] = $this->get_isrole($item['reid'], $_W['user']['uid']);
        $item['count'] = $this->get_count($item['reid']);
        $form['title'] = $form['title'] . '_' . random(6);
        include $this->template('display');
        $where .= " AND title LIKE '%{$_GPC['keyword']}%'";
        $where .= ' and status=' . intval($status);
        $where .= ' and cid=' . intval($cateid);
        }

        public function doWebDelete() {$params[':reid'] = $reid; {
        $sql = 'DELETE FROM ' . tablename($this->tb_field) . ' WHERE `reid`=:reid';
        $sql = 'DELETE FROM ' . tablename($this->tb_form) . ' WHERE `reid`=:reid';
        $params = array();
        $sql = 'DELETE FROM ' . tablename($this->tb_staff) . ' WHERE `reid`=:reid';
        $sql = 'DELETE FROM ' . tablename($this->tb_info) . ' WHERE `reid`=:reid';
        $role = $this->get_isrole($reid, $_W['user']['uid']);
        require MODULE_ROOT . '/fans.web.php';
        global $_W, $_GPC;
        $reid = intval($_GPC['id']);
        $sql = 'DELETE FROM ' . tablename($this->tb_data) . ' WHERE `reid`=:reid';

        if ($_W['role'] == 'operator' && !$role) {

        if ($reid > 0) {
        pdo_query($sql, $params);
        message('非法访问.');
        message('操作成功.', referer());
        message('您没有权限进行该操作.');
        pdo_query($sql, $params);
        pdo_query($sql, $params);
        pdo_query($sql, $params);
        pdo_query($sql, $params);
        }

        public function doWebdayu_formDelete() {if (empty($id)) {} {
        $id = intval($_GPC['id']);
        require MODULE_ROOT . '/fans.web.php';
        global $_W, $_GPC;
        $role = $this->get_isrole($id, $_W['user']['uid']);
        pdo_delete('dayu_form_info', array("rerid" => $id));
        message('操作成功.', referer());
        message('您没有权限进行该操作.');
        pdo_delete('dayu_form_data', array("rerid" => $id));
        }

        public function doMobiledayu_formDelete() {if (!empty($id) && $openid == $form['openid']) {} {
        $id = intval($_GPC['id']);
        $form = pdo_fetch('SELECT rerid, openid FROM ' . tablename($this->tb_info) . " WHERE rerid = '{$id}'");
        $reid = intval($_GPC['reid']);
        global $_W, $_GPC;
        $openid = intval($_GPC['openid']);
        pdo_delete('dayu_form_data', array("rerid" => $id));
        pdo_delete('dayu_form_info', array("rerid" => $id));
        $this->showMessage('删除成功.', $this->createMobileUrl('mydayu_form', array("weid" => $_W['uniacid'], "id" => $reid)), '', '', '');
        $this->showMessage('删除失败，原因：该记录不在您的名下.', $this->createMobileUrl('mydayu_form', array("weid" => $_W['uniacid'], "id" => $reid)), '', '', '');
        }

        public function doWebPost() {$sql = 'SELECT * FROM ' . tablename($this->tb_field) . ' WHERE `reid`=:reid ORDER BY `displayorder` DESC,`refid`'; {
        $sql = 'SELECT openid,nickname FROM ' . tablename('mc_mapping_fans') . ' WHERE acid =:acid AND openid = :openid';
        $title = !empty($_GPC['id']) ? '编辑表单' : '新建表单';
        $sql = 'SELECT openid,nickname FROM ' . tablename('mc_mapping_fans') . ' WHERE acid =:acid AND nickname = :nickname';
        $types = array();
        $var1 = !empty($par['var1']) ? '&' . $par['var1'] . '=自定义变量1' : '';
        $activity = pdo_fetch($sql, $params);
        $links = '保存表单后生成链接';
        $print = pdo_fetchall('SELECT * FROM ' . tablename('dayu_print') . ' WHERE weid = :weid and status=1', array(":weid" => $_W['uniacid']));
        $dayu_check = pdo_fetchall('SELECT * FROM ' . tablename('dayu_check_category') . ' WHERE weid = :weid', array(":weid" => $_W['uniacid']));
        $ds = pdo_fetchall($sql, $params);
        $par = iunserializer($activity['par']);
        $field = array();
        $activity = array("kfirst" => "有客户提交新的表单，请及时跟进", "kfoot" => "点击处理客户提交的表单。", "mfirst" => "受理结果通知", "mfoot" => "如有疑问，请致电联系我们。", "information" => "您提交申请我们已经收到, 请等待客服跟进.", "adds" => "联系地址", "voice" => "录音", "voicedec" => "录音说明", "pluraltit" => "上传图片", "status" => 1, "credit" => 0, "member" => "姓名", "phone" => "手机", "endtime" => date('Y-m-d s', strtotime('+365 day')));
        $params = array();
        $sql = 'DELETE FROM ' . tablename($this->tb_field) . ' WHERE `reid`=:reid';
        $data = array("edit" => intval($_GPC['edit']), "isdel" => intval($_GPC['isdel']), "follow" => intval($_GPC['follow']), "replace" => intval($_GPC['replace']), "card" => intval($_GPC['card']), "pretotal" => intval($_GPC['pretotal']), "daynum" => intval($_GPC['daynum']), "allnum" => intval($_GPC['allnum']), "header" => intval($_GPC['header']), "state1" => trim($_GPC['state1']), "state2" => trim($_GPC['state2']), "state3" => trim($_GPC['state3']), "state4" => trim($_GPC['state4']), "state5" => trim($_GPC['state5']), "state8" => trim($_GPC['state8']), "var1t" => trim($_GPC['var1t']), "var1" => trim($_GPC['var1']), "var2t" => trim($_GPC['var2t']), "var2" => trim($_GPC['var2']), "var3t" => trim($_GPC['var3t']), "var3" => trim($_GPC['var3']), "title" => trim($_GPC['titles']), "ismname" => intval($_GPC['ismname']), "mname" => trim($_GPC['mname']), "submitname" => trim($_GPC['submitname']), "btncolor" => trim($_GPC['btncolor']), "business" => trim($_GPC['business']), "address" => trim($_GPC['address']), "tel" => trim($_GPC['tel']), "lat" => $_GPC['baidumap']['lat'], "lng" => $_GPC['baidumap']['lng'], "noticeurl" => trim($_GPC['noticeurl']), "kami" => intval($_GPC['kami']), "print" => intval($_GPC['print']), "sendkami" => intval($_GPC['sendkami']), "comment" => intval($_GPC['comment']), "commenttitle" => trim($_GPC['commenttitle']), "consult" => intval($_GPC['consult']), "wxcard" => intval($_GPC['wxcard']), "getadd" => intval($_GPC['getadd']), "icredit" => intval($_GPC['icredit']), "onlytit" => trim($_GPC['onlytit']), "sms" => $_GPC['sms'], "smstype" => $_GPC['yztype'], "subtitle" => trim($_GPC['subtitle']), "icon" => $_GPC['icon'], "isrand" => $_GPC['isrand'], "randnum" => $_GPC['randnum'], "dayu_check" => intval($_GPC['dayu_check']));
        $insert = array("weid" => $_W['uniacid'], "reid" => $reid, "roleid" => $rid);
        $behavior = $settings['creditbehaviors'];
        $openid = trim($_GPC['openid']);
        $settings = uni_setting($_W['uniacid'], array("creditnames", "creditbehaviors", "uc", "payment", "passport"));
        $params = array();
        $params = array();
        $sendkami = pdo_fetchall('SELECT * FROM ' . tablename('dayu_sendkami_category') . ' WHERE weid = :weid', array(":weid" => $_W['uniacid']));
        $groups = mc_groups();
        $creditnames = $settings['creditnames'];
        $reid = intval($_GPC['id']);
        load()->model('mc');
        $hasData = true;
        $links = murl('entry', array("do" => "dayu_form", "id" => $reid, "m" => "dayu_form"), true, true) . $var1 . $var2 . $var3;
        $consult = pdo_fetchall('SELECT * FROM ' . tablename('dayu_consult_category') . ' WHERE weid = :weid', array(":weid" => $_W['uniacid']));
        $comment = pdo_fetchall('SELECT * FROM ' . tablename('dayu_comment_category') . ' WHERE weid = :weid', array(":weid" => $_W['uniacid']));
        $par = array("state1" => "待受理", "state2" => "受理中", "state3" => "已完成", "state4" => "拒绝受理", "state5" => "已取消", "state8" => "退回修改", "mname" => "往期记录", "submitname" => "立 即 提 交", "header" => "1");
        $var3 = !empty($par['var3']) ? '&' . $par['var3'] . '=自定义变量3' : '';
        $op = trim($_GPC['op']) ? trim($_GPC['op']) : 'post';
        $permission = pdo_fetchall('SELECT id, uid, role FROM ' . tablename('uni_account_users') . ' WHERE uniacid = :weid and role != :role  ORDER BY uid ASC, role DESC', array(":role" => "clerk", ":weid" => $weid));
        $category = pdo_fetchall('select * from ' . tablename($this->tb_category) . ' where weid = :weid ORDER BY id DESC', array(":weid" => $weid));
        $params = array();
        $hasData = false;
        $kami = pdo_fetchall('SELECT * FROM ' . tablename('dayu_kami_category') . ' WHERE weid = :weid', array(":weid" => $_W['uniacid']));
        $exist = pdo_fetch($sql, array(":nickname" => $nickname, ":acid" => $_W['acid']));
        $reply = pdo_fetch($sql, $params);
        $reid = pdo_insertid();
        $record = array();
        $var2 = !empty($par['var2']) ? '&' . $par['var2'] . '=自定义变量2' : '';
        $role = pdo_fetchall('SELECT roleid FROM ' . tablename($this->tb_role) . " WHERE weid = '{$_W['uniacid']}' AND reid = '{$reid}'");
        load()->func('file');
        $sql = 'SELECT COUNT(*) FROM ' . tablename($this->tb_info) . ' WHERE `reid`=' . $reid;
        $exist = pdo_fetch($sql, array(":openid" => $openid, ":acid" => $_W['acid']));
        require MODULE_ROOT . '/fans.web.php';
        $modules = uni_modules();
        $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `reid`=:reid';
        $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `reid`=:reid';
        $wxcard = pdo_fetchall('SELECT * FROM ' . tablename('dayu_wxcard_activity') . ' WHERE weid = :weid and status=1', array(":weid" => $_W['uniacid']));
        $fields = mc_fields();
        $nickname = trim($_GPC['nickname']);
        $sms = pdo_fetchall('SELECT * FROM ' . tablename('dayu_sms') . ' WHERE weid = :weid', array(":weid" => $_W['uniacid']));
        global $_W, $_GPC;
        $skins = pdo_fetchall('SELECT * FROM ' . tablename('dayu_form_skins') . ' WHERE status = 1 ORDER BY id', array());

        foreach ($skins as &$s) { $s['weid'] = !empty($s['ids']) ? explode(',', $s['ids']) : '';

        if (!empty($openid)) {}

        if (empty($_GPC['thumb'])) {}

        if (!pdo_tableexists('dayu_wxcard_activity')) {}

        if (is_array($_GPC['slide'])) {}

        foreach ($_GPC['title'] as $k => $v) {$field['loc'] = $_GPC['loc'][$k];

        foreach ($permission as &$p) {$p['user'] = $this->get_role($p['uid']);

        if (!(!empty($role) && in_array($p['uid'], $rolearr))) {}}

        if (!pdo_tableexists('dayu_consult_category')) {}

        if ($_GPC['role'] && $reid) {

        foreach ($_GPC['role'] as $rid) {$rid = intval($rid);

        if (!pdo_tableexists('dayu_kami_category')) {}

        if (empty($skins)) {}

        if (!checksubmit()) {}

        if (empty($permission)) {}

        if (!empty($exist)) {}

        if (!pdo_tableexists('dayu_comment_category')) {}

        if (!pdo_tableexists('dayu_form_skins')) {}

        foreach ($_GPC['slide'] as $slide) { $se[] = tomedia($slide);

        if (!pdo_tableexists('dayu_check_category')) {}

        if (empty($role)) {}

        if (!($setting['role'] == 1 && ($_W['role'] == 'founder' || $_W['role'] == 'manager'))) {}

        if (!pdo_tableexists('dayu_print')) {}

        if (!pdo_tableexists('dayu_sms')) {}

        if (!(pdo_fetchcolumn($sql) > 0)) {}

        foreach ($role as $r) { $rolearr[] = $r['roleid'];

        if (empty($reid)) {}

        if (!pdo_tableexists('dayu_sendkami_category')) {}

        if (!(pdo_update('dayu_form', $record, array("reid" => $reid)) === false)) {}
        itoast('保存成功.', 'refresh');
        pdo_insert('dayu_form_fields', $field);
        itoast('保存表单失败2, 请稍后重试.', '', 'error');
        itoast('保存表单失败1, 请稍后重试.', '', 'error');
        unset($insert);
        pdo_insert($this->tb_role, $insert) ? '' : itoast('抱歉，更新失败！', referer(), 'error');
        file_delete($_GPC['thumb-old']);
        pdo_query($sql, $params);
        message(error(-1, '未找到对应的粉丝编号，请检查昵称或openid是否有效'), '', 'ajax');
        pdo_tableexists('dayu_photograph_fields') && ($types['photograph'] = '证件照(photo)');
        message(error(0, $exist), '', 'ajax');
        pdo_insert('dayu_form', $record);
        pdo_delete($this->tb_role, array("weid" => $_W['uniacid'], "reid" => $reid));
        $record['cid'] = intval($_GPC['cate']);
        $activity['starttime'] && ($activity['starttime'] = date('Y-m-d s', $activity['starttime']));
        $record['information'] = trim($_GPC['information']);
        $record['mbgroup'] = $_GPC['mbgroup'];
        $record['agreement'] = trim($_GPC['agreement']);
        $record['kfirst'] = trim($_GPC['kfirst']);
        $record['title'] = trim($_GPC['activity']);
        $params[':reid'] = $reid;
        $record['list'] = intval($_GPC['list']);
        $record['paixu'] = intval($_GPC['paixu']);
        $types['checkbox'] = '多选(checkbox)';
        $record['thumb'] = $_GPC['thumb'];
        $record['inhome'] = intval($_GPC['inhome']);
        $params[':reid'] = $reid;
        $record['mfoot'] = trim($_GPC['mfoot']);
        $field['displayorder'] = range_limit($_GPC['displayorder'][$k], 0, 254);
        $field['essential'] = $_GPC['essentialvalue'][$k] == 'true' ? 1 : 0;
        $field['title'] = trim($v);
        $field['description'] = $_GPC['desc'][$k];
        $field['image'] = $_GPC['image'][$k];
        $field['type'] = $_GPC['type'][$k];
        $field['only'] = $_GPC['only'][$k];
        $field['bind'] = $_GPC['bind'][$k];
        $field['value'] = $_GPC['value'][$k];
        $field['reid'] = $reid;
        $field['value'] = urldecode($field['value']);
        $types['email'] = '电子邮件(email)';
        $record['isget'] = intval($_GPC['isget']);
        $record['isinfo'] = intval($_GPC['isinfo']);
        $record['pluraltit'] = trim($_GPC['pluraltit']);
        $record['isrevoice'] = intval($_GPC['isrevoice']);
        $params[':weid'] = $_W['uniacid'];
        $record['isloc'] = intval($_GPC['isloc']);
        $record['slide'] = '';
        $p['select'] = 1;
        $types['text'] = '字符串(text)';
        $record['m_templateid'] = trim($_GPC['m_templateid']);
        $params[':weid'] = $_W['uniacid'];
        $record['noticeemail'] = trim($_GPC['noticeemail']);
        $types['tel'] = '电话(tel)';
        $record['plural'] = intval($_GPC['plural']);
        $record['member'] = trim($_GPC['member']);
        $par['map'] = array("lat" => $par['lat'], "lng" => $par['lng']);
        $record['smstype'] = $_GPC['smstype'];
        $types['radio'] = '单选(radio)';
        $record['kfoot'] = trim($_GPC['kfoot']);
        $record['status'] = intval($_GPC['status']);
        $params[':reid'] = $reid;
        $params[':reid'] = $reid;
        $record['ivoice'] = intval($_GPC['ivoice']); !empty($activity['slide']) && ($slide = iunserializer($activity['slide']));
        $record['skins'] = trim($_GPC['skins']);
        $record['smsid'] = $_GPC['smsid'];
        $record['slide'] = iserializer($se);
        $record['par'] = iserializer($data);
        $record['voicedec'] = trim($_GPC['voicedec']);
        $record['content'] = trim($_GPC['content']);
        $types['phone'] = '手机(phone)';
        $types['image'] = '上传图片(image)';
        $types['select'] = '下拉框(select)';
        $types['calendar'] = '日历(calendar)';
        $record['starttime'] = strtotime($_GPC['starttime']);
        $record['mobile'] = trim($_GPC['mobile']);
        $record['isrethumb'] = intval($_GPC['isrethumb']);
        $record['isvoice'] = intval($_GPC['isvoice']);
        $record['voice'] = trim($_GPC['voice']);
        $record['outlink'] = trim($_GPC['outlink']);
        $activity['endtime'] && ($activity['endtime'] = date('Y-m-d s', $activity['endtime']));
        $types['textarea'] = '文本(textarea)';
        $record['phone'] = trim($_GPC['phone']);
        $record['createtime'] = TIMESTAMP;
        $types['idcard'] = '身份证(idcard)';
        $record['status'] = 1;
        $types['range'] = '日期时间(range)';
        $record['custom_status'] = intval($_GPC['custom_status']);
        include $this->template('post');
        $record['credit'] = $_GPC['credit'];
        $record['endtime'] = strtotime($_GPC['endtime']);
        $types['number'] = '数字(number)';
        $record['description'] = trim($_GPC['description']);
        $record['mfirst'] = trim($_GPC['mfirst']);
        $record['k_templateid'] = trim($_GPC['k_templateid']);
        $record['weid'] = $_W['uniacid'];
        $record['smsnotice'] = $_GPC['smsnotice'];
        $types['reside'] = '省市区(reside)';
        }

        public function doWebBatchrRcord() {if (!empty($reply)) {} {
        $role = $this->get_isrole($reid, $_W['user']['uid']);
        $reply = pdo_fetch('select reid from ' . tablename($this->tb_form) . ' where reid = :reid', array(":reid" => $reid));
        require MODULE_ROOT . '/fans.web.php';
        global $_GPC, $_W;
        $reid = intval($_GPC['reid']);

        if ($setting['role'] == 1 && $_W['role'] == 'operator' && !$role) {

        foreach ($_GPC['idArr'] as $k => $rerid) {pdo_delete($this->tb_info, array("rerid" => $rerid, "reid" => $reid));
        message('您没有权限进行该操作.');
        message($result, '', 'ajax');
        pdo_delete($this->tb_data, array("rerid" => $rerid, "reid" => $reid));
        $result['msg'] = '记录批量删除成功！';
        $result['status'] = 1;
        $result['status'] = 0;
        $result['msg'] = '抱歉，表单主题不存在或是已经被删除！';
        }}
        }

        public function doWebRecordSet() {message('保存成功.', 'refresh'); {
        $reid = intval($_GPC['reid']);
        $params = array();
        require MODULE_ROOT . '/fans.web.php';
        global $_W, $_GPC;
        $role = $this->get_isrole($reid, $_W['user']['uid']);
        $sql = 'SELECT * FROM ' . tablename($this->tb_field) . ' WHERE `reid`=:reid ORDER BY `displayorder` DESC,`refid`';
        $params = array();
        $record = array();
        $activity = pdo_fetch($sql, $params);
        $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `reid`=:reid';
        $record = iunserializer($activity['fields']);
        $ds = pdo_fetchall($sql, $params);

        foreach ($_GPC['fields'] as $fields) { $th[] = $fields;

        if (!(pdo_update('dayu_form', $record, array("reid" => $reid)) === false)) {}

        if (empty($_GPC['fields'])) {}

        if (!checksubmit()) {}

        if ($setting['role'] == 1 && $_W['role'] == 'operator' && !$role) {
        message('保存表单失败, 请稍后重试.');
        message('您没有权限进行该操作.');
        $record['fields'] = iserializer($th);
        $record['avatar'] = intval($_GPC['avatar']);
        $params[':weid'] = $_W['uniacid'];
        $params[':reid'] = $reid;
        include $this->template('recordset');
        $record['field'] = intval($_GPC['field']);
        $record['bcolor'] = $_GPC['bcolor'];
        $params[':reid'] = $reid;
        }

        public function doWebCustom() {include $this->template('custom'); {
        $op = $operation = $_GPC['op'] ? $_GPC['op'] : 'display';
        global $_W, $_GPC;
        $custom = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_custom) . ' WHERE weid = :weid ORDER BY displayorder DESC', array(":weid" => $weid));
        $data = array("weid" => $weid, "displayorder" => intval($_GPC['displayorder']), "raply" => $_GPC['raply']);
        $role = $this->get_isrole($id, $_W['user']['uid']);
        $id = intval($_GPC['id']);
        $city = array("displayorder" => 0);
        $custom = pdo_fetch('SELECT * FROM ' . tablename($this->tb_custom) . " WHERE id = '{$id}'");
        $id = $_GPC['id'];
        require MODULE_ROOT . '/fans.web.php';
        $id = pdo_insertid();
        load()->func('tpl');
        $id = intval($_GPC['id']);
        $custom = pdo_fetch('SELECT * FROM ' . tablename($this->tb_custom) . " WHERE id = '{$id}'");

        if ($setting['role'] == 1 && $_W['role'] == 'operator' && !$role) {

        if (!empty($_GPC['raply'])) {}

        foreach ($_GPC['displayorder'] as $id => $displayorder) { pdo_update($this->tb_custom, array("displayorder" => $displayorder), array("id" => $id));

        if (!checksubmit('submit')) {}

        if (!empty($id)) {}

        if (empty($_GPC['displayorder'])) {}

        if (!empty($custom)) {}

        if (!empty($id)) {}
        itoast('抱歉，快捷回复内容不存在或是已经被删除！', $this->createWebUrl('custom', array("op" => "display")), 'error');
        pdo_insert($this->tb_custom, $data);
        message('您没有权限进行该操作.');
        itoast('快捷回复内容排序更新成功！', $this->createWebUrl('custom', array("op" => "display")), 'success');
        pdo_delete($this->tb_custom, array("id" => $id));
        itoast('快捷回复内容删除成功！', $this->createWebUrl('custom', array("op" => "display")), 'success');
        itoast('更新快捷回复内容成功！', $this->createWebUrl('custom', array("op" => "display")), 'success');
        pdo_update($this->tb_custom, $data, array("id" => $id));
        message('抱歉，请输入快捷回复内容！');
        include $this->template('custom');
        }

        public function doMobilerecord() {$rows = pdo_fetchall($sql, $params, 'rerid'); {
        $record = iunserializer($activity['fields']);
        $reid = intval($_GPC['id']);
        $list = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_form) . ' WHERE weid = :weid and kfid = :openid and status = 1 ORDER BY reid DESC', array(":weid" => $weid, ":openid" => $openid), 'reid');
        $psize = 10;
        $sql = 'SELECT * FROM ' . tablename($this->tb_info) . " WHERE reid=:reid {$where}ORDER BY createtime DESC,rerid DESC LIMIT " . ($pindex - 1) * $psize . ',' . $psize;
        $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `reid`=:reid';
        $pindex = max(1, intval($_GPC['page']));
        $activity = pdo_fetch($sql, $params);
        $pager = $this->pagination($total, $pindex, $psize);
        require MODULE_ROOT . '/fans.mobile.php';
        $state = !empty($activity['state3']) ? $activity['state3'] : '已完成';
        $childlist = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_data) . ' WHERE rerid IN (\'' . implode('\',\'', is_array($rerid) ? $rerid : array($rerid)) . "') AND `reid`=:reid AND `refid` IN ({$fids}) ORDER BY displayorder DESC,rerid DESC", array(":reid" => $reid));
        $fids = implode(',', $record);
        $maps = $piclist;
        global $_W, $_GPC;
        $rerid = array_keys($rows);
        $total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_info) . " WHERE reid = :reid {$where} ", $params);
        $params = array();
        $children = array();
        $params = array();

        foreach ($childlist as $reply => $r) {$children[$r['rerid']][] = $r;

        if (empty($r['rerid'])) {}}

        foreach ($rows as $index => $row) {if (empty($row['rethumb'])) {}

        foreach ($rows[$index]['thumbs'] as $p) { $piclist .= is_array($p) ? $p['attachment'] : tomedia($p) . ',';

        foreach ($rows[$index]['rethumb'] as $p) { $piclist .= is_array($p) ? $p['attachment'] : tomedia($p) . ',';

        if (empty($row['rethumb'])) {}
        unset($children[$reply]);
        $params[':weid'] = $_W['uniacid'];
        $params[':reid'] = $reid;
        $this->showMessage('非法访问，主题不存在', '', 'error', '', '');
        $rows[$index]['thumbs'] = !empty($row['rethumb']) ? iunserializer($row['thumb']) : '';
        $rows[$index]['thumb'] = iunserializer($row['thumb']);
        $rows[$index]['rethumb'] = !empty($row['rethumb']) ? iunserializer($row['rethumb']) : '';
        $rows[$index]['user'] = mc_fansinfo($row['openid'], $acid, $weid);
        $where .= ' and status=3';
        $params[':reid'] = $reid;
        include $this->template('record');
        }

        public function doMobileCheckOnly() {$result['msg'] = $_GPC['title'] . $msg; {
        $par = iunserializer($activity['par']);
        global $_W, $_GPC;
        $activity = pdo_get($this->tb_form, array("weid" => $_W['uniacid'], "reid" => $_GPC['reid']), array("par"));
        $msg = !empty($par['onlytit']) ? $par['onlytit'] : '存在相同内容，请重新填写';
        $data = pdo_fetch('SELECT * FROM ' . tablename($this->tb_data) . ' WHERE reid = :reid AND refid = :refid', array(":reid" => $_GPC['reid'], ":refid" => $_GPC['refid']));
        message($result, '', 'ajax');
        $result['status'] = '1';
        $result['status'] = '0';
        $result['msg'] = '可使用';
        }

        public function doMobileEdit() {$_share['imgUrl'] = tomedia($activity['thumb']); {
        $reside = $f;
        $info = pdo_get($this->tb_info, array("rerid" => $rerid), array());
        $acc = WeAccount::create($_W['acid']);
        $activity = pdo_get($this->tb_form, array("weid" => $weid, "reid" => $reid), array());
        $rerid = intval($_GPC['rerid']);
        $custom = array("msgtype" => "text", "text" => array("content" => urlencode($info)), "touser" => $s['openid']);
        $info = '【您好，有新的消息】

        $datas = array(); ihttp_email($activity['noticeemail'], $activity['title'] . '的表单提醒', '<h4>姓名：' . $info['member'] . '</h4><h4>手机：' . $info['mobile'] . '</h4>' . $body); $row = array(); if (!is_array($staff)) {}

        $reid = intval($_GPC['reid']); $title = $par['header'] == 1 ? $activity['title'] : $activity['titles']; load()->func('tpl'); foreach ($datas as $row) {if (!strstr($row['content']['data'], 'images')) {}

        global $_W, $_GPC; if (!is_array($_GPC['thumb'])) {}

        $profile = mc_fetch($uid, $binds); if (!empty($datas)) {}

        $binds = array(); $info['fields'] = $info['redid'] = array(); load()->func('communication'); exit; foreach ($staff as $s) {$this->send_template_message(urldecode(json_encode($template))); $template = array("touser" => $s['openid'], "template_id" => $activity['k_templateid'], "url" => $_W['siteroot'] . 'app/' . $this->createMobileUrl('manageform', array("name" => "dayu_form", "id" => $reid)), "topcolor" => "#FF0000", "data" => array("first" => array("value" => urlencode($activity['kfirst'] . '\\n'), "color" => "#743A3A"), "keyword1" => array("value" => urlencode($info['member']), "color" => "#000000"), "keyword2" => array("value" => urlencode($info['mobile']), "color" => "#000000"), "keyword3" => array("value" => urlencode($ytime), "color" => "#000000"), "keyword4" => array("value" => urlencode($status['name'] . '\\n' . $bodnew . '\\n'), "color" => "#FF0000"), "remark" => array("value" => urlencode($activity['kfoot']), "color" => "#008000"))); }

        $outlink = !empty($activity['outlink']) ? $activity['outlink'] : $this->createMobileUrl('mydayu_form', array("op" => "detail", "rerid" => $rerid, "id" => $reid)); require MODULE_ROOT . '/fans.mobile.php'; $this->showMessage('非法访问.', '', '', '', ''); $picker = 1; $status = $this->get_status($reid, '0'); $ytime = date('Y-m-d s', TIMESTAMP); $row['status'] = '0'; $_share['title'] = $activity['title']; $this->showMessage('记录不存在或是已经被删除！', '', '', '', ''); $par = iunserializer($activity['par']); foreach ($_POST as $key => $value) {$entry['data'] = strval($value); pdo_update($this->tb_data, $entry, array("redid" => $key)); $entry = array(); $datas[] = array("content" => $entry, "refid" => $key); }

        $ds = $fids = array(); $staff = pdo_fetchall('SELECT `openid` FROM ' . tablename($this->tb_staff) . ' WHERE reid=:reid AND weid=:weid', array(":weid" => $_W['uniacid'], ":reid" => $reid)); $returnUrl = urlencode($_W['siteurl']); pdo_update($this->tb_info, $row, array("rerid" => $rerid)); $row['thumb'] = iserializer($th); $this->showMessage('非法访问，提交数据不能为空', '', 'error'); if (empty($activity['noticeemail'])) {}

        $field = pdo_getall($this->tb_field, array("reid" => $reid), array(), '', 'displayorder DESC,refid DESC', ''); include $this->template('edit'); $fdatas = pdo_fetchall($sql, $params); $submitname = !empty($par['submitname']) ? $par['submitname'] : '立 即 提 交'; $sql = 'SELECT * FROM ' . tablename($this->tb_data) . " WHERE `reid`='{$reid}' AND `rerid`='{$rerid}' AND `refid` IN ({$fids})"; foreach ($fdatas as $fd) {$info['redid'][$fd['refid']] = $fd['redid']; $info['fields'][$fd['refid']] = $fd['data']; $info['fields'][$fd['refid']] = tomedia($fd['data']); if (strstr($fd['data'], 'images')) {}}

        foreach ($field as $f) {$ds[$f['refid']]['tixing'] = !empty($f['description']) ? urldecode($f['description']) : '请输入' . $f['title'];

        if ($f['type'] == 'image') {

        if (!(!empty($f['loc']) && pdo_tableexists('dayu_form_plugin_radio') && ($f['type'] == 'radio' || $f['type'] == 'checkbox'))) {}

        if ($f['type'] == 'reside') {

        if (in_array($f['type'], array("text", "number", "email"))) {}

        foreach ($staff as $s) {$url = $_W['siteroot'] . 'app/' . $this->createMobileUrl('manageform', array("name" => "dayu_form", "id" => $reid));

        if (!is_array($staff)) {}

        if (!($_W['ispost'] || checksubmit('submit'))) {}

        if (!empty($field)) {}
        $ds[$f['refid']]['default'] = $f['default'];
        $ds[$f['refid']]['loc'] = $f['loc'];
        $ds[$f['refid']]['tixing'] = !empty($f['description']) ? urldecode($f['description']) : '请填写' . $f['title'];
        $ds[$f['refid']]['type'] = $f['type'];
        $ds[$f['refid']]['options'] = explode(',', $f['value']);
        $fids[] = $f['refid'];
        $ds[$f['refid']]['refid'] = $f['refid'];
        $ds[$f['refid']]['tixing'] = !empty($f['description']) ? urldecode($f['description']) : '请选择' . $f['title'];
        $f['default'] = $profile[$f['bind']];
        $ds[$f['refid']]['photograph_url'] = $f['photograph_url'];
        $ds[$f['refid']]['fid'] = $f['title'];
        $ds[$f['refid']]['dayu_radio'] = pdo_fetchall('SELECT * FROM ' . tablename('dayu_form_plugin_radio') . ' WHERE weid = :weid and cid = :cid and status=1 ORDER BY id DESC', array(":weid" => $weid, ":cid" => $f['loc']));
        $ds[$f['refid']]['essential'] = $f['essential'];
        $ds[$f['refid']]['image'] = !empty($f['image']) ? $f['image'] : TEMPLATE_WEUI . 'images/nopic.jpg';
        exit;
        '; $CustomNotice = $acc->sendCustomNotice($custom); $info .= "<a href='{$url}'>点击查看详情</a>"; $info .= "姓名：{$_GPC['member']}
        手机：{$_GPC['mobile']}
        内容：{$bodym}"; }

        $this->showMessage($activity['information'], $outlink); foreach ($_GPC['thumb'] as $thumb) { $th[] = tomedia($thumb); }
        $body .= '<h4>' . $row['content']['data'] . '</h4>'; $smsbody .= $row['content']['data'] . '，'; $bodym .= '\\n　' . $row['content']['data']; $bodnew .= !empty($row['content']['data']) ? '\\n' . $row['fid']['title'] . '：' . $row['content']['data'] : ''; $row['fid'] = $this->get_fields($field['refid']); $field = pdo_get($this->tb_data, array("redid" => $row['refid']), array()); $row['data'] = '有'; }

        $_share['content'] = $activity['description']; if (empty($datas)) {}

        $this->showMessage('不能修改内容', 'error'); $fids = implode(',', $fids); if ($par['edit'] != '1' && $info['status'] != '8') {
        }

        public function doMobiledayu_form() {$row['mobile'] = $_GPC['mobile']; $sms_data = array("mobile" => $activity['mobile'], "title" => $activity['title'], "mname" => $member, "mmobile" => $_GPC['mobile'], "openid" => $row['openid'], "status" => $state['name'], "ptname" => $_W['account']['name'], "addons" => $_W['current_module']['name'], "content" => $alldata); $btncolor = $btncolor['css']; $ds = pdo_fetchall($sql, $params); load()->func('communication'); if (empty($datas)) {} {

        $member = !empty($row['member']) ? $row['member'] : $fans['user']['nickname']; if (!($allnum >= intval($par['allnum']))) {}

        load()->model('mc'); $row['openid'] = $openid; $update['realname'] = $_GPC['member']; $this->showMessage('名额已满', '', 'info'); if (empty($activity['noticeemail'])) {}

        $ds = pdo_fetchall($sql, $params); $params[':reid'] = $reid; $this->showMessage('保存失败.'); $sql = 'SELECT * FROM ' . tablename($this->tb_field) . ' WHERE `reid` = :reid ORDER BY `displayorder` DESC'; $params[':reid'] = $reid; $log = $activity['title'] . '-' . $activity['credit'] . '积分'; if (!empty($repeat)) {}

        $kami = pdo_get('dayu_kami', array("weid" => $weid, "number" => $_GPC['kami'], "cid" => $par['kami']), array("id", "status", "number", "password")); $repeat = $_COOKIE['r_submit']; $par = iunserializer($activity['par']); $sendkamidata = array("reid" => $reid, "infoid" => $rerid, "addons" => "dayu_form", "openid" => $openid, "name" => $row['member'], "mobile" => $row['mobile'], "status" => 1, "updatetime" => TIMESTAMP); $activity['smsid'] && empty($member['mobile']) && $par['smstype'] == '1' && $this->showMessage('请完善手机号', murl('entry', array("do" => "index", "id" => $activity['smsid'], "m" => "dayu_sms", "form" => $_W['current_module']['name'], "returnurl" => $returnUrl), true, true), 'info'); if (!($par['replace'] && !empty($update))) {}

        $behavior = $settings['creditbehaviors']; $lg = array("l1" => intval($_GPC['linkage1']), "l2" => intval($_GPC['linkage2'])); foreach ($binds as $key => $value) {$binds[] = 'residedist'; $binds[] = 'resideprovince'; $binds[] = 'birthmonth'; $binds[] = 'residecity'; if ($value == 'birth') {

        load()->func('file'); $initRange = $initCalendar = false; $row['lat'] = $_GPC['getlat']; $row['getip'] = $_GPC['getip']; $sql = 'SELECT * FROM ' . tablename($this->tb_field) . ' WHERE `reid` = :reid ORDER BY rand() limit ' . $par['randnum']; global $_W, $_GPC; !empty($activity['slide']) && ($slide = iunserializer($activity['slide'])); $update['residedist'] = $_GPC['reside']['district']; $time = date('Y-m-d', time()); $submitname = !empty($par['submitname']) ? $par['submitname'] : '立 即 提 交'; $profile = mc_fetch($uid, $binds); $linkage = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_linkage) . ' WHERE reid = :reid and parentid = 0 ORDER BY displayorder desc, id DESC', array(":reid" => $reid)); $params[':weid'] = $weid; ihttp_post(murl('entry', array("do" => "Notice", "id" => $activity['smsnotice'], "m" => "dayu_sms"), true, true), $sms_data); $this->showMessage($_GPC['kami'] . ' 已使用', '', 'error'); $_share['content'] = $activity['description']; foreach ($staff as $s) {$this->send_template_message(urldecode(json_encode($template))); $template = array("touser" => $s['openid'], "template_id" => $activity['k_templateid'], "url" => $_W['siteroot'] . 'app/' . $this->createMobileUrl('manageform', array("name" => "dayu_form", "weid" => $row['weid'], "id" => $row['reid'])), "topcolor" => "#FF0000", "data" => array("first" => array("value" => urlencode($activity['kfirst'] . '\\n'), "color" => "#743A3A"), "keyword1" => array("value" => urlencode($member), "color" => "#000000"), "keyword2" => array("value" => urlencode($_GPC['mobile']), "color" => "#000000"), "keyword3" => array("value" => urlencode($ytime), "color" => "#000000"), "keyword4" => array("value" => urlencode($bodym . $voice . $kamiinfo . '\\n'), "color" => "#FF0000"), "remark" => array("value" => urlencode($activity['kfoot']), "color" => "#008000"))); }

        $template = array("touser" => $row['openid'], "template_id" => $activity['k_templateid'], "url" => murl('entry', array("do" => "mydayu_form", "op" => "detail", "id" => $row['reid'], "rerid" => $rerid, "m" => "dayu_form"), true, true), "topcolor" => "#FF0000", "data" => array("first" => array("value" => urlencode($activity['mfirst']), "color" => "#743A3A"), "keyword1" => array("value" => urlencode($member), "color" => "#000000"), "keyword2" => array("value" => urlencode($row['mobile']), "color" => "#000000"), "keyword3" => array("value" => urlencode($ytime), "color" => "#000000"), "keyword4" => array("value" => urlencode($kamiinfo), "color" => "#FF0000"), "remark" => array("value" => urlencode($activity['mfoot']), "color" => "#008000"))); if (!(pdo_insert('dayu_form_info', $row) != 1)) {}

        $mname = !empty($par['mname']) ? $par['mname'] : '往期记录'; if (!empty($datas)) {}

        $tmp_name = $upfile['tmp_name']; message('移动文件失败，请检查服务器权限', referer(), 'error'); mkdir($upload_path); $upload_path = ATTACHMENT_ROOT . '/dayu_form/' . $weid . '/'; $target_filename = 'form' . $reid . '_' . date('YmdHis') . mt_rand(10, 99) . '.thumb.jpg'; if ($refid && $field && $file['name'] && $field['type'] == 'image') {

        $content = date('Y-m-d s', TIMESTAMP); $entry = array(); $uptypes = array("image/jpg", "image/png", "image/jpeg"); unlink($srcfile); $entry['reid'] = $reid; $imginfo = getimagesize($srcfile); if ($size > $maxfilesize * 1024 * 1024) {

        $maxfilesize = $upfilesize; $source_filename = 'form' . $reid . '_' . date('YmdHis') . mt_rand(10, 99) . '.jpg'; $refid = intval(str_replace('field_', '', $key)); $datas[] = $entry; message('上传文件类型不符：' . $type, referer(), 'error'); $srcfile = $upload_path . $source_filename; $ret = file_image_thumb($srcfile, $desfile, $avatarsize); if (move_uploaded_file($tmp_name, $upload_path . $source_filename)) {}

        $name = $upfile['name']; $upfile = $file; $entry['refid'] = $refid; message('上传文件过大' . $_FILES['file']['error'], referer(), 'error'); if (in_array($type, $uptypes)) {}

        $color = imagecolorallocatealpha($image, 255, 0, 0, 50); $field = $fields[$refid]; load()->func('file'); $size = $upfile['size']; $image = $fun($srcfile); $imgtype = image_type_to_extension($imginfo[2], false); if (!strexists($key, 'field_')) {}

        $avatarsize = !empty($activity['upsize']) ? $activity['upsize'] : 640; $fun = 'imagecreatefrom' . $imgtype; imagestring($image, 5, 10, 10, $content, $color); message('上传错误：错误代码：' . $error, referer(), 'error'); if (file_exists($upload_path)) {}}
        $alldata = array();

        $tomorrow = strtotime('tomorrow'); $fans['user'] = mc_fansinfo($openid, $acid, $weid); $credits = mc_credit_fetch($_W['member']['uid'], '*'); $state = $this->get_status($row['reid'], 1); $row['createtime'] = TIMESTAMP; $today = strtotime('today'); $this->showMessage('抱歉,每人只能提交' . intval($par['pretotal']) . '次！', '', 'info'); if (empty($kami)) {}

        $to_card = $par['card'] == 1 ? murl('entry', array("do" => "card", "m" => "we7_coupon", "returnurl" => $returnUrl), true, true) : murl('entry', array("do" => "index", "m" => "dayu_card", "returnurl" => $returnUrl), true, true); $this->showMessage('卡号不存在', '', 'error'); $row['lng'] = $_GPC['getlng']; foreach ($_GPC as $key => $value) {if (!in_array($field['type'], array("checkbox"))) {}

        $bindFiled = substr(strrchr($key, '_'), 1); $entry['data'] = trim($value); if (!in_array($field['type'], array("number", "text", "calendar", "email", "radio", "textarea", "range", "select", "image", "reside", "photograph", "tingli", "phone", "tel", "idcard"))) {}

        $params = array(); if (!($lognum >= intval($par['daynum']))) {}

        $dayu_check = pdo_get('dayu_check', array("weid" => $weid, "openid" => $openid, "cid" => $dayu_check_category['id'], "status" => 1), array("id")); $_share['imgUrl'] = tomedia($activity['thumb']); mc_group_update($uid); $group = pdo_fetch('SELECT * FROM ' . tablename('mc_members') . " WHERE uniacid = '{$weid}' AND uid = '{$uid}'"); if ($ishy == false) {

        $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `reid`=:reid'; if (!(!empty($activity['mobile']) && !empty($activity['smsnotice']))) {}

        $lognum = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_info) . " WHERE openid = '{$openid}' AND reid = '{$reid}' AND createtime > " . $today . ' AND createtime < ' . $tomorrow); $activity = pdo_fetch($sql, $params); foreach ($ds as $value) { $fields[$value['refid']] = $value; }

        $yuyuetime = date('Y-m-d i', time() + 3600); $row['kid'] = $sendkami['id']; if (!($pretotal >= intval($par['pretotal']))) {}

        $record = array(); $title = $par['title'] ? $par['title'] : $activity['title']; $this->showMessage($kami['password'] . '卡号与密码不匹配', '', 'error'); ihttp_post(murl('entry', array("do" => "print", "printid" => $par['print'], "m" => "dayu_print"), true, true), array("title" => $activity['title'], "realname" => $member, "mobile" => $_GPC['mobile'], "info" => $bodyp, "createtime" => $row['createtime'])); $allnum = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_info) . ' WHERE reid = :reid and (status=0 or status=1 or status=3)', array(":reid" => $reid)); $_share['title'] = $title; $row['voice'] = !empty($_GPC['voice']) ? $setting['qiniu']['host'] . '/' . $_GPC['voice'] : ''; if (!(pdo_tableexists('dayu_sendkami') && !empty($par['sendkami']))) {}
        $kamiinfo = !empty($row['kid']) ? '\\n　卡号：' . $sendkami['number'] . '\\n　密码：' . $sendkami['password'] : ''; $params = array(); $dayu_check_category = pdo_get('dayu_check_category', array("weid" => $weid, "id" => $par['dayu_check']), array("id", "reminder")); $pretotal = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_info) . ' WHERE reid = :reid AND openid = :openid', array(":reid" => $reid, ":openid" => $openid));

        $check_url = murl('entry', array("do" => "index", "id" => $par['dayu_check'], "reid" => $reid, "formdo" => $_GPC['do'], "form" => $_W['current_module']['name'], "m" => "dayu_check", "returnurl" => $returnUrl), true, true); $row['var3'] = $_GPC[$par['var3']]; $creditnames = $settings['creditnames']; $this->showMessage('您所在会员组没有相关的操作权限！', '', 'info'); foreach ($datas as $d) {$smsbody .= $fields[$d['refid']]['title'] . ':' . $d['data'] . '，'; $bodym .= '\\n　' . $fields[$d['refid']]['title'] . ':' . $d['data']; $d['data'] = '有'; if (!strstr($d['data'], 'images')) {}

        $btncolor = $this->get_color($reid, $par['btncolor']); if ($par['isrand'] == 1 && !empty($par['randnum'])) {}

        require MODULE_ROOT . '/fans.mobile.php'; if ($activity['mbgroup'] != 0) {

        $staff = pdo_fetchall('SELECT `openid` FROM ' . tablename($this->tb_staff) . ' WHERE reid=:reid AND weid=:weid', array(":weid" => $_W['uniacid'], ":reid" => $row['reid'])); $activity['thumb'] = tomedia($activity['thumb']); if (!(pdo_tableexists('dayu_sendkami') && !empty($par['sendkami']))) {}

        load()->func('tpl'); $this->showMessage('活动已经停止.'); if (!is_array($staff)) {}
        $sendkami = pdo_get('dayu_sendkami', array("weid" => $weid, "cid" => $par['sendkami'], "id" => $row['kid']), array("id", "number", "password")); $reid = intval($_GPC['id']); $row['thumb'] = iserializer($th); $acc = notice_init(); }

        if ($par['follow'] == 1) {

        if (!is_array($staff)) {}

        if (!empty($activity['starttime'])) {}

        if (intval($error) > 0) {}

        if (!(pdo_tableexists('dayu_kami') && !empty($par['kami']))) {}

        if ($activity['status'] == 0) {

        if (!($_W['ispost'] || checksubmit('submit'))) {}

        if (!(intval($par['pretotal']) != 0)) {}

        if (!strexists($key, 'field_')) {}

        if ($refid && $field) {

        foreach ($datas as &$r) {pdo_insert('dayu_form_data', $r); $r['rerid'] = $rerid; }

        if ($par['card'] == 1 || $par['card'] == 2) {

        if ($profile['gender'] == '2') {

        if (in_array($r['type'], array("radio", "checkbox"))) {}

        if ($profile['gender'] == '0') {

        if ($r['type'] == 'reside') {

        if (in_array($r['type'], array("text", "number", "email", "textarea", "idcard", "phone", "tel"))) {}

        if (empty($_GPC['reside'])) {}

        if (!(intval($par['allnum']) != 0)) {}

        if ($activity['starttime'] > TIMESTAMP) {

        if (!(pdo_tableexists('dayu_check') && $par['dayu_check'])) {}

        if (!(pdo_tableexists('dayu_print') && !empty($par['print']))) {}

        if (!is_array($_GPC['thumb'])) {}
        pdo_update('dayu_sendkami', $sendkamidata, array("weid" => $weid, "id" => $row['kid'])); $params = array(); $binds = array(); $sql = 'SELECT * FROM ' . tablename($this->tb_field) . ' WHERE `reid` = :reid ORDER BY `displayorder` DESC'; load()->func('communication'); $this->getFollow(); $this->showMessage($dayu_check_category['reminder'], $check_url, 'info'); pdo_update('dayu_form', $record, array("reid" => $reid)); $this->showMessage('活动还未开始！<br>开始时间：' . date('Y-m-d s', $activity['starttime'])); $this->showMessage('非法访问，主题不存在'); $comment = pdo_fetchall('SELECT * FROM ' . tablename('dayu_comment') . ' WHERE weid = :weid and reid = :reid ORDER BY reid DESC', array(":weid" => $weid, ":reid" => $reid)); $returnUrl = urlencode($_W['siteurl']);

        ihttp_email($activity['noticeemail'], $activity['title'] . '的表单提醒', '<h4>姓名：' . $member . '</h4><h4>手机：' . $_GPC['mobile'] . '</h4>' . $body . $voice . $kamiinfo); setcookie('r_submit', $_GPC['repeat']); $sendkami = pdo_get('dayu_sendkami', array("weid" => $weid, "cid" => $par['sendkami'], "status" => "0"), array("id", "number", "password")); $ycredit = $credits[$behavior['activity']] + $activity['credit']; $kamiinfo = !empty($row['kid']) ? '\\n　卡号：' . $kami['number'] : ''; mc_notice_credit1($openid, $uid, $activity['credit'], $log); $uid && mc_update($uid, $update); if (!(intval($par['daynum']) != 0)) {}

        $this->showMessage('表单不完整，缺少自定义项目，无法正常访问.'); if (!(!empty($par['comment']) && pdo_tableexists('dayu_comment'))) {}

        $row['address'] = $_GPC['address']; if (!empty($rerid)) {}

        $row['kid'] = $kami['id']; foreach ($staff as $s) {$url = $_W['siteroot'] . 'app/' . $this->createMobileUrl('manageform', array("name" => "dayu_form", "weid" => $row['weid'], "id" => $row['reid'])); $info .= "<a href='{$url}'>点击查看详情</a>"; $custom = array("msgtype" => "text", "text" => array("content" => urlencode($info)), "touser" => $s['openid']); $info .= "姓名：{$member}手机：{$_GPC['mobile']}
        内容：{$bodym}{$voice}{$kamiinfo}"; $info = '【您好，有新的消息】
        '; $CustomNotice = $acc->sendCustomNotice($custom); }

        exit; $set = $this->module['config']; if ($activity['endtime'] < TIMESTAMP) {

        $row['var1'] = $_GPC[$par['var1']]; foreach ($formdata as $index => $v) { $alldata[] = $v['title'] . ':' . $v['data'] . ','; }

        $binds[] = 'birthyear'; unset($binds[$key]); unset($binds[$key]); if ($value == 'reside') {
        $binds[] = 'birthday'; }

        $row['linkage'] = iserializer($lg); mc_credit_update($uid, $behavior['activity'], $activity['credit'], array(0, $activity['title'])); if (empty($_GPC['linkage2'])) {}

        $this->send_template_message(urldecode(json_encode($template))); exit; $params[':reid'] = $reid; foreach ($_FILES as $key => $file) {$error = $upfile['error']; $type = $upfile['type']; @mkdirs($upload_path); $desfile = $upload_path . $target_filename; imagejpeg($image, $srcfile); $upfilesize = !empty($activity['filesize']) ? $activity['filesize'] : 12; $entry['data'] = $upload_path . $target_filename; if (is_array($ret)) {}

        $entry['rerid'] = 0; if ($maxfilesize > 0) {

        include $this->template('skins/' . $activity['skins']); $picker = 1; $outlink = !empty($activity['outlink']) ? $activity['outlink'] : $this->createMobileUrl('mydayu_form', array("name" => "dayu_form", "weid" => $row['weid'], "id" => $row['reid'])); $rerid = pdo_insertid(); $ytime = date('Y-m-d s', TIMESTAMP); if ($groupid != $activity['mbgroup']) {

        $this->showMessage('您还不是会员,请先领取您的会员卡.', $to_card, 'info'); foreach ($_GPC['thumb'] as $thumb) { $th[] = tomedia($thumb); }

        $update['mobile'] = $_GPC['mobile']; $datas = $fields = $update = array(); if (!is_error($acc)) {}
        $entry['refid'] = $refid; $refid = intval(str_replace('field_', '', $key)); $update[$bindFiled] = $value; $entry = array(); $entry['displayorder'] = $field['displayorder']; $entry['data'] = implode(',', $value); $entry['rerid'] = 0;

        $entry['reid'] = $reid; if (empty($bindFiled)) {}

        $entry['data'] = implode(',', $value); $datas[] = $entry; $field = $fields[$refid]; if (is_array($value)) {}}

        $row['var2'] = $_GPC[$par['var2']]; $kamidata = array("reid" => $reid, "infoid" => $rerid, "addons" => "dayu_form", "openid" => $openid, "name" => $row['member'], "mobile" => $row['mobile'], "status" => 1, "updatetime" => TIMESTAMP); pdo_update('dayu_kami', $kamidata, array("weid" => $weid, "id" => $row['kid'])); $acc = WeAccount::create($_W['acid']); foreach ($_GPC['reside'] as $key => $value) {$resideData = array("reid" => $reside['reid']); $resideData['rerid'] = 0; $resideData['data'] = $value; $datas[] = $resideData; $resideData['refid'] = $reside['refid']; }

        $this->showMessage('保存失败。'); $qqkey = $set['qqkey']; foreach ($ds as &$r) {if ($r['bind'] == 'email' && strexists($profile[$r['bind']], 'we7.cc')) {}
        $r['tixing'] = !empty($r['description']) ? urldecode($r['description']) : '请选择' . $r['title']; $isloc = $r;
        $binds[$r['type']] = $r['bind'];

        $activity['smsid'] && $r['bind'] == 'mobile' && empty($profile[$r['bind']]) && $this->showMessage('请完善手机号', murl('entry', array("do" => "index", "id" => $activity['smsid'], "m" => "dayu_sms", "form" => $_W['current_module']['name'], "returnurl" => $returnUrl), true, true), 'info'); $r['default'] = ''; $r['options'] = explode(',', $r['value']); if (!(!empty($r['loc']) && pdo_tableexists('dayu_form_plugin_radio') && ($r['type'] == 'radio' || $r['type'] == 'checkbox'))) {}
        $profile['gender'] = '女'; $initRange = true; $profile['gender'] = '男'; $r['default'] = $profile[$r['bind']]; pdo_tableexists('dayu_photograph_fields') && ($r['photograph_url'] = murl('entry', array("do" => "index", "f" => $r['bind'], "m" => "dayu_photograph", "returnurl" => $returnUrl), true, true)); $initCalendar = true;

        $r['image'] = !empty($r['image']) ? $r['image'] : TEMPLATE_WEUI . 'images/nopic.jpg'; if ($r['type'] == 'text' && $r['loc'] > 0) {

        $r['dayu_radio'] = pdo_fetchall('SELECT * FROM ' . tablename('dayu_form_plugin_radio') . ' WHERE weid = :weid and cid = :cid and status=1 ORDER BY id DESC', array(":weid" => $weid, ":cid" => $r['loc'])); $reside = $r; if ($profile['gender'] == '1') {

        $r['tixing'] = !empty($r['description']) ? urldecode($r['description']) : '请填写' . $r['title']; if ($r['type'] == 'range') {

        $r['type'] == 'photograph' && empty($profile[$r['bind']]) && $this->showMessage('请完善' . $r['title'], murl('entry', array("do" => "index", "f" => $r['bind'], "m" => "dayu_photograph", "returnurl" => $returnUrl), true, true), 'info'); if ($r['type'] == 'calendar') {
        $profile['gender'] = '保密';

        $this->showMessage($activity['information'], $outlink); $settings = uni_setting($_W['uniacid'], array("creditnames", "creditbehaviors", "uc", "payment", "passport")); $this->showMessage($activity['information'], $this->createMobileUrl('mydayu_form', array("id" => $reid))); $this->showMessage('抱歉,每天只能提交' . intval($par['daynum']) . '次！', $this->createMobileUrl('mydayu_form', array("name" => "dayu_form", "weid" => $weid, "id" => $reid)), 'info'); if (!(pdo_tableexists('dayu_kami') && !empty($par['kami']))) {}

        $row['reid'] = $reid; $row = array(); if ($activity['credit'] != '0.00' && $par['icredit'] == '0') {!empty($activity['linkage']) && ($la = iunserializer($activity['linkage'])); setcookie('r_submit', $_GPC['repeat']); $row['member'] = $_GPC['member']; $row['getadd'] = $_GPC['getadd']; $formdata = $this->order_foreach ($row['reid'], $rerid); $update['residecity'] = $_GPC['reside']['city']; $this->showMessage('非法访问，提交数据不能为空', '', 'error'); if (!in_array('reside', $binds)) {}
        $body .= '<h4>' . $fields[$d['refid']]['title'] . '：' . $d['data'] . '</h4>'; $bodyp .= $fields[$d['refid']]['title'] . '：' . $d['data'] . '|'; }

        $this->showMessage('活动已经结束！<br>截至时间：' . date('Y-m-d s', $activity['endtime'])); $record['starttime'] = TIMESTAMP; $ishy = $this->isHy($openid); $voice = !empty($_GPC['voice']) ? '\\n　有' . $activity['voice'] : ''; $update['resideprovince'] = $_GPC['reside']['province']; if ($activity['paixu'] != '2') {

        exit; if (empty($_GPC['repeat'])) {}
        return error(-1, $acc['message']); $groupid = $group['groupid'];
        }

        public function doMobileConsult() { {

        $cid = intval($_GPC['cid']); $this->showMessage('已完成，关闭咨询.', referer(), 'error'); $title = '在线咨询'; $check = pdo_get($this->tb_info, array("rerid" => $rerid), array()); $this->showMessage('系统错误.', referer(), 'error'); $rerid = intval($_GPC['rerid']); $staff = '1'; $back_url = $this->createMobileUrl('manageform', array("op" => "detail", "rerid" => $rerid, "id" => $reid)); $back_url = $this->createMobileUrl('mydayu_form', array("op" => "detail", "rerid" => $rerid, "id" => $reid)); if (!(empty($cid) || empty($reid) || empty($rerid) || empty($weid) || empty($openid))) {}
        global $_W, $_GPC; $staff = '0'; include $this->template('consult'); $reid = intval($_GPC['reid']);
        require MODULE_ROOT . '/fans.mobile.php';
        $form = intval($_GPC['form']); $title = '在线咨询管理'; $this->showMessage('非法访问.', referer(), 'error'); $isstaff = $this->get_staff($openid, $reid); }
        }

        public function get_staff($openid, $reid) { global $_GPC, $_W; return pdo_get($this->tb_staff, array("weid" => $_W['uniacid'], "openid" => $openid, "reid" => $reid), array()); }

        $result['localId'] = $localId; $result['path'] = $filename; global $_W, $_GPC; load()->class('account'); $serverId = trim($_GPC['serverId']); $acid = $_W['acid']; $result['status'] = 'success'; $type = !empty($_GPC['type']) ? $_GPC['type'] : 'image'; exit(json_encode(array("status" => true))); $localId = trim($_GPC['localId']); $result['imgurl'] = $_W['attachurl'] . $filename; $acc = WeAccount::create($acid); $params = array(":openid" => $_W['openid'], ":uniacid" => $_W['uniacid']); $_W['acid'] = pdo_fetchcolumn($sql, $params); if (!empty($_W['acid'])) {}

        file_delete($file); $media = array(); $media['type'] = $type; $sql = 'SELECT acid FROM ' . tablename('mc_mapping_fans') . ' WHERE openid = :openid AND uniacid = :uniacid limit 1'; if (!empty($_W['acid'])) {}

        $operation = !empty($_GPC['op']) ? $_GPC['op'] : 'upload'; $result['message'] = '上传失败'; $file = $_GPC['file']; $filename = $acc->downloadMedia($media, true); if (is_error($filename)) {}
        $result['status'] = 'error'; $media['media_id'] = $serverId; $result['message'] = '没有找到相关公众账号';
        $result = array("status" => "error", "message" => "123", "data" => ""); die(json_encode($result)); $result['message'] = '上传成功'; $result['status'] = 'error'; $result['serverId'] = $serverId; }

        public function doMobileUpload() {unlink($upload_path . $pathname); mc_update($_W['member']['uid'], $data); message('远程附件上传失败，请检查配置并重新上传'); load()->func('file'); $pathname = $images_path . $pic; $picurl = $upload_path . $images_path . $pic; $params[':reid'] = $reid; $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `reid`=:reid'; $targetName = $picurl; load()->class('weixin.account'); global $_W, $_GPC; load()->func('tpl'); if (file_exists($upload_path . $images_path)) {} {
        $params = array(); $reid = intval($_GPC['id']); $data = array("avatar" => $images_path . $pic); $picurl = $upload_path . $images_path . $pic; $remotestatus = file_remote_upload($pathname); $access_token = $accObj->fetch_token();
        $ch = curl_init($url); curl_setopt($ch, CURLOPT_HEADER, 0); curl_close($ch); @mkdirs($upload_path . $images_path); $pic = 'avatar_' . date('YmdHis') . mt_rand(1000, 9999) . '.jpg'; curl_setopt($ch, CURLOPT_FILE, $fp); $media_id = $_GET['media_id']; load()->func('file'); $upload_path = ATTACHMENT_ROOT;
        $acc = WeAccount::create($_W['uniacid']); load()->model('mc'); }

        if (empty($_W['setting']['remote']['type'])) {}

        fclose($fp); $fp = fopen($targetName, 'wb'); $url = 'https://file.api.weixin.qq.com/cgi-bin/media/get?access_token=' . $access_token . '&media_id=' . $media_id; $pic = 'form' . $reid . '_' . date('YmdHis') . mt_rand(1000, 9999) . '.jpg'; $images_path = 'dayu_form/' . $_W['uniacid'] . '/'; $remoteurl = $pathname; mkdir($upload_path . $images_path); echo $pathname; $activity = pdo_fetch($sql, $params); curl_exec($ch); $params[':weid'] = $weid; if (is_error($remotestatus)) {}
        }

        public function download_voice($media_id, $retry = 0) {global $_W, $_GPC; load()->func('communication'); $this->download_voice($media_id, 1); $access_token = WeAccount::token(); $voice = ihttp_get('https://file.api.weixin.qq.com/cgi-bin/media/get?access_token=' . $access_token . '&media_id=' . $media_id); return false; return $voice['content']; if (isset($voice['headers']['Content-disposition'])) {} {

        if ($retry === 0) {
        }

        }

        public function upload_qiniu_voice($filename, $content) {if (isset($r['persistentId']) && !empty($r['persistentId'])) {} {
        $setting = $this->module['config']; return ''; $qiniu = new Qiniu($setting['qiniu']); return ''; return $r['persistentId']; $pipeline = $setting['qiniu']['pipeline']; $r = $qiniu->putContent($filename, $content, $pipeline);
        require MODULE_ROOT . '/Qiniu.class.php'; }

        if (empty($setting['qiniu'])) {}
        }

        public function doMobileUploadvoice() {global $_W, $_GPC; $this->showMessage('serverId为空');  {

        $filename = 'dayu_form_' . $_W['uniacid'] . '_' . $_GPC['serverId'] . '.mp3'; $content = $this->download_voice($_GPC['serverId'], ''); $r = $this->upload_qiniu_voice($filename, $content); if (!empty($_GPC['serverId'])) {}
        $setting = $this->module['config']; }
        }

        public function doMobileGetprefop() {echo '1'; if ($r['code'] == 0) { {
        global $_W, $_GPC;

        $r = json_decode($r, true); if (!isset($r['code'])) {}
        $r = file_get_contents('//api.qiniu.com/status/get/prefop?id=' . $_GPC['persistentId']); }
        }

        public function doMobiletest() { global $_W, $_GPC; include $this->template('test'); }

        public function curl_post($sucai, $img) {$ch = curl_init(); curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE); return $output; curl_setopt($ch, CURLOPT_SAFE_UPLOAD, true); curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1); curl_setopt($ch, CURLOPT_POSTFIELDS, $data); curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE); curl_setopt($ch, CURLOPT_POST, 1); curl_setopt($ch, CURLOPT_SAFE_UPLOAD, false); if (class_exists('CURLFile')) {} {

        $data = array("media" => new \CURLFile(realpath($img))); curl_close($ch); $data = array("media" => '@' . realpath($img)); if (!defined('CURLOPT_SAFE_UPLOAD')) {}
        curl_setopt($ch, CURLOPT_URL, $sucai); $output = curl_exec($ch); }
        }

        function curl_post2($url, $data = null) {return $output; curl_setopt($curl, CURLOPT_POSTFIELDS, $data); curl_close($curl); curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1); if (empty($data)) {}
        curl_setopt($curl, CURLOPT_POST, 1); $output = curl_exec($curl); $curl = curl_init(); curl_setopt($curl, CURLOPT_URL, $url); }

        public function doMobileMydayu_form() {$total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_info) . " WHERE openid = :openid and reid = :reid {$where}", array(":openid" => $_W['openid'], ":reid" => $reid)); $ds = $fids = array(); $consult = pdo_fetch('SELECT id,createtime FROM ' . tablename('dayu_consult') . ' WHERE infoid = :infoid and openid = :openid and rid = \'0\' ORDER BY createtime DESC', array(":infoid" => $row['rerid'], ":openid" => $openid)); {
        $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `reid`=:reid';
        $pindex = max(1, intval($_GPC['page']));
        $row = pdo_fetch('SELECT * FROM ' . tablename($this->tb_info) . ' WHERE openid = :openid AND rerid = :rerid', array(":openid" => $_W['openid'], ":rerid" => $rerid));
        $new_array = array();
        $status = intval($_GPC['status']);
        $mname = !empty($par['mname']) ? $par['mname'] : '往期记录';
        $user_footer = 1;
        $setting = $this->module['config'];
        $status = intval($_GPC['status']);
        $pindex = max(1, intval($_GPC['page']));
        $rerid = array_keys($rows);
        $total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_info) . " WHERE openid = :openid {$where} ", array(":openid" => $_W['openid']));
        $member = mc_oauth_userinfo($_W['acid']);
        $linkage = iunserializer($row['linkage']);
        $params = array();
        $operation = !empty($_GPC['op']) ? $_GPC['op'] : 'display';
        $par = iunserializer($activity['par']);
        $consultr = pdo_fetch('SELECT id,createtime FROM ' . tablename('dayu_consult') . ' WHERE infoid = :infoid and rid = \'1\' ORDER BY createtime DESC', array(":infoid" => $row['rerid']));
        $fdatas = pdo_fetchall($sql, $params);
        $sql = 'SELECT * FROM ' . tablename($this->tb_field) . ' WHERE `reid`=:reid ORDER BY displayorder DESC, refid DESC';
        $psize = 10;
        $rows = pdo_fetchall($sql, $params);
        $pager = $this->pagination($total, $pindex, $psize);
        $kami = pdo_get('dayu_sendkami', array("weid" => $weid, "id" => $row['kid']), array());
        $children = array();
        $fields = pdo_fetchall($sql, $params);
        $list = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_form) . " WHERE weid = :weid and reid in({$fids}) and status = 1 ORDER BY reid DESC", array(":weid" => $weid), 'reid');
        $kami = pdo_get('dayu_kami', array("weid" => $weid, "id" => $row['kid']), array());
        $sql = 'SELECT `reid` FROM ' . tablename($this->tb_info) . ' WHERE openid = :openid ORDER BY rerid DESC';
        $status = $this->get_status($row['reid'], $row['status']);
        require MODULE_ROOT . '/fans.mobile.php';
        $la = iunserializer($activity['linkage']);
        $member = mc_oauth_fans($openid, $_W['acid']);
        $params = array();
        $rerid = intval($_GPC['rerid']);
        $rows = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_info) . " WHERE openid = :openid {$where}ORDER BY rerid DESC LIMIT " . ($pindex - 1) * $psize . ",{$psize}", array(":openid" => $_W['openid']));
        $activity = pdo_fetch($sql, $params);
        $last = array();
        $reid = intval($_GPC['id']);
        $sql = 'SELECT * FROM ' . tablename($this->tb_data) . " WHERE `reid`=:reid AND `rerid`='{$row['rerid']}' AND `refid` IN ({$fids})";
        $fids = implode(',', $last);
        $list = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_form) . ' WHERE weid = :weid and status = 1 ORDER BY reid DESC', array(":weid" => $weid), 'reid');
        $rows = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_info) . " WHERE openid = :openid and reid = :reid {$where}ORDER BY rerid DESC LIMIT " . ($pindex - 1) * $psize . ",{$psize}", array(":openid" => $_W['openid'], ":reid" => $reid), 'rerid');
        $c_tishi = '<span class="weui-badge right" style="margin-5px; absolute; 5px; 5px;">有新回复</span>';
        $fids = implode(',', $fids);
        $member = !empty($member) ? $member : $_SESSION['userinfo'];
        global $_W, $_GPC;
        $comment = pdo_get('dayu_comment', array("weid" => $weid, "id" => $row['commentid']), array());
        $childlist = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_data) . ' WHERE rerid IN (\'' . implode('\',\'', is_array($rerid) ? $rerid : array($rerid)) . '\') AND `reid`=:reid', array(":reid" => $reid));
        $title = $activity['title'];
        $params = array();
        $psize = 10;

        if (empty($row['rethumb'])) {}

        if (!is_array($linkage)) {}

        foreach ($new_array as $u => $v) { $last[] = $u;

        foreach ($rows as $key => $val) {$rows[$key]['user'] = mc_fansinfo($val['openid'], $acid, $weid);

        foreach ($fdatas as $fd) { $row['fields'][$fd['refid']] = $fd['data'];

        if (!empty($member['avatar'])) {}

        if (!empty($row)) {}

        foreach ($ds as $value) {if ($value['type'] == 'reside') {

        foreach ($fdatas as $fdata) {if ($fdata['refid'] == $value['refid']) {

        if ($_GPC['status'] != '') {

        if (!(pdo_tableexists('dayu_kami') && !empty($par['kami']) && $row['kid'])) {}

        if (!(pdo_tableexists('dayu_sendkami') && !empty($par['sendkami']) && $row['kid'])) {}

        if (!(!empty($consult['id']) && $consult['createtime'] < $consultr['createtime'])) {}

        foreach ($rows as $v) { $new_array[$v['reid']] = 1;

        foreach ($fields as $f) {$ds[$f['refid']]['fid'] = $f['title'];

        if (!(!empty($par['comment']) && pdo_tableexists('dayu_comment') && !empty($row['commentid']))) {}

        foreach ($childlist as $reply => $r) {$children[$r['rerid']][] = $r;

        if (empty($r['rerid'])) {}}

        if (!empty($fields)) {}

        if (!pdo_tableexists('dayu_consult')) {}

        if ($_GPC['status'] != '') {
        unset($children[$reply]);
        $this->getFollow();
        $dayu_form['content'] = htmlspecialchars_decode($dayu_form['content']);
        $this->showMessage('非法访问.');
        $linkage['l1'] = $this->get_linkage($linkage['l1'], '');
        $rows[$key]['kf'] = mc_fansinfo($val['kf'], $acid, $weid);
        $rows[$key]['comment'] = !empty($par['comment']) && pdo_tableexists('dayu_comment') && !empty($val['commentid']) ? $this->get_comment($val['commentid']) : '';
        $rows[$key]['status'] = $this->get_status($val['reid'], $val['status']);
        $row['createtime'] = !empty($row['createtime']) ? date('Y-m-d i', $row['createtime']) : '时间丢失';
        $row['fields'][$value['refid']] = '';
        $row['fields'][$value['refid']] .= $fdata['data'];
        }}
        $row['voices'] = $row['voice'];
        $row['thumb'] = iunserializer($row['thumb']);
        include $this->template('dayu_form');
        $row['fields'] = array();
        $row['file'] = iunserializer($row['file']);
        $row['user'] = mc_fansinfo($row['openid'], $acid, $weid);
        $where .= ' and ( status=2 or status=-1 )';
        $params[':reid'] = $row['reid'];
        $row['rethumb'] = iunserializer($row['rethumb']);
        $linkage['l2'] = $this->get_linkage($linkage['l2'], '');
        $where .= " and status={$status}";
        $ds[$f['refid']]['refid'] = $f['refid'];
        $ds[$f['refid']]['loc'] = $f['loc'];
        $fids[] = $f['refid'];
        $ds[$f['refid']]['type'] = $f['type'];
        $this->showMessage('非法访问');
        $params[':reid'] = $reid;
        $where .= ' and ( status=2 or status=-1 )';
        $params[':weid'] = $_W['uniacid'];
        $params[':openid'] = $openid;
        $where .= " and status={$status}";
        $this->showMessage('记录不存在或是已经被删除！');
        $row['revoices'] = $row['revoice'];
        $row['yuyuetime'] = !empty($row['yuyuetime']) ? date('Y-m-d i', $row['yuyuetime']) : '请等待客服受理';
        }

        public function doMobileGetForm() {$mylink = $this->createMobileUrl('mydayu_form', array("id" => $form['reid'])); {
        $par = iunserializer($form['par']);
        $form = pdo_get($this->tb_form, array("weid" => $weid, "reid" => $_GPC['id']), array());
        $weid = $_W['uniacid'];
        $html2 = '
        $result['id'] = $form['reid'];
        $result['html2'] = $html2;
        $result['html'] = $html;
        <div class="weui_tabbar tab-bottom">
        <a href="javascript:;" class="weui_tabbar_item close-popup">
        <div class="weui_tabbar_icon">
        <svg class="icon" aria-hidden="true">
        <use href="#icon-close"></use>
        </svg>
        </div>
        <p class="weui_tabbar_label">关闭</p>
        </a>
        <a href="' . $link . '" class="weui_tabbar_item">
        <div class="weui_tabbar_icon">
        <svg class="icon" aria-hidden="true">
        <use href="#icon-xinzeng"></use>
        </svg>
        </div>
        <p class="weui_tabbar_label">' . $form['title'] . '</p>
        </a>
        <a href="' . $mylink . '" class="weui_tabbar_item">
        <div class="weui_tabbar_icon">
        <svg class="icon" aria-hidden="true">
        <use href="#icon-jihuajindu"></use>
        </svg>
        </div>
        <p class="weui_tabbar_label">' . $par['mname'] . '</p>
        </a>
        </div>
        '; $thumb = tomedia($form['thumb']); $link = $this->createMobileUrl('dayu_form', array("id" => $form['reid'])); $result['mname'] = $form['mname']; global $_GPC, $_W; $html = '
        <div class="weui-header bg-blue">
        <div class="weui-header-left">
        <a href="javascript:;" class="icon icon-109 f-white close-popup">
        <svg class="icon" aria-hidden="true">
        <use href="#icon-left"></use>
        </svg>
        </a>
        </div>
        <h1 class="weui-header-title">' . $form['title'] . '</h1>
        </div>
        <div class="weui-weixin">
        <div class="weui-weixin-ui">
        <div class="weui-weixin-page">
        <div class="weui-weixin-img text-center"><img src="' . $thumb . '" id="image" class="center" style="100%;"></div>
        <div class="weui-weixin-content">' . htmlspecialchars_decode($form['content']) . '</div>
        </div>
        </div>
        </div>
        '; message($result, '', 'ajax'); }
        }

        public function doMobilelist() {global $_W, $_GPC; include $this->template('list'); $list = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_form) . ' WHERE weid = :weid and status = 1 ORDER BY reid DESC', array(":weid" => $_W['uniacid']), 'reid'); }
        手机：{$content['mobile']}
        管理员派单

        "; $data = array("kf" => $_GPC['openid']); $CustomNotice = $acc->sendCustomNotice($custom); if ($_GPC['table'] == 'manage') {

        message($result, '', 'ajax'); $activity = pdo_get($this->tb_form, array("weid" => $_W['uniacid'], "reid" => $reid), array("title", "k_templateid", "kfirst", "kfoot")); if ($_W['account']['level'] == ACCOUNT_SERVICE_VERIFY && !empty($activity['k_templateid'])) {}

        $result['msg'] = '派单失败'; $this->send_template_message(urldecode(json_encode($template))); $reid = $_GPC['reid']; $result['msg'] = '转移失败'; if (pdo_update($this->tb_form, $data, array("reid" => $reid, "weid" => $_W['uniacid'])) === false) {}

        $template = array("touser" => $_GPC['openid'], "template_id" => $activity['k_templateid'], "url" => $_W['siteroot'] . 'app/' . $this->createMobileUrl('manageform', array("name" => "dayu_form", "weid" => $_W['uniacid'], "op" => "detail", "id" => $reid, "rerid" => $rerid)), "topcolor" => "#FF0000", "data" => array("first" => array("value" => urlencode($activity['kfirst'] . '\\n'), "color" => "#743A3A"), "keyword1" => array("value" => urlencode($content['member']), "color" => "#000000"), "keyword2" => array("value" => urlencode($content['mobile']), "color" => "#000000"), "keyword3" => array("value" => urlencode(date('Y-m-d s', $content['createtime'])), "color" => "#000000"), "keyword4" => array("value" => urlencode('管理员派单\\n'), "color" => "#FF0000"), "remark" => array("value" => urlencode($activity['kfoot']), "color" => "#008000"))); if ($_GPC['table'] == 'case') {
        global $_GPC, $_W; $custom = array("msgtype" => "text", "text" => array("content" => urlencode($info)), "touser" => $_GPC['openid']); $content = pdo_get($this->tb_info, array("reid" => $reid, "rerid" => $rerid), array("member", "mobile", "createtime")); $rerid = $_GPC['rerid']; $info = "【您好，{$activity['title']} 通知】

        "; if (pdo_update($this->tb_info, $data, array("reid" => $reid, "rerid" => $rerid)) === false) {}
        $result['status'] = 0; $acc = WeAccount::create($_W['acid']); $result['status'] = 1; $url = $_W['siteroot'] . 'app/' . $this->createMobileUrl('manageform', array("name" => "dayu_form", "weid" => $_W['uniacid'], "op" => "detail", "id" => $reid, "rerid" => $rerid)); }

        public function doMobilemanageform() {$pindex = max(1, intval($_GPC['page'])); $fids = implode(',', $fids); $record['status'] = intval($_GPC['status']); $log = $activity['title'] . '-' . $activity['credit'] . '积分'; $rerid = array_keys($rows); $par = iunserializer($activity['par']); if (!(pdo_tableexists('dayu_sendkami') && !empty($par['sendkami']) && $row['kid'])) {} {
        $title = $activity['title']; load()->func('communication'); $ytime = date('Y-m-d s', $yuyuetime); $info = '【您好，受理结果通知】

        $sql = 'SELECT * FROM ' . tablename($this->tb_field) . ' WHERE `reid`=:reid ORDER BY displayorder DESC, refid DESC'; $sms_data = array("mobile" => $row['mobile'], "title" => $activity['title'], "mname" => $row['member'], "mmobile" => $row['mobile'], "openid" => $row['openid'], "status" => $status['name'], "ptname" => $_W['account']['name'], "addons" => $_W['current_module']['name'], "content" => $alldata); if (!empty($row)) {}
        $consultr = pdo_fetch('SELECT id,createtime FROM ' . tablename('dayu_consult') . ' WHERE infoid = :infoid AND rid = \'1\' ORDER BY createtime DESC', array(":infoid" => $row['rerid']));
        $row = pdo_fetch('SELECT * FROM ' . tablename($this->tb_info) . ' WHERE rerid = :rerid', array(":rerid" => $rerid));
        $msg = '';
        global $_W, $_GPC;
        $list = pdo_fetchall('SELECT s.reid, y.* FROM ' . tablename($this->tb_staff) . ' s left join ' . tablename($this->tb_form) . ' y on y.reid=s.reid WHERE y.weid=:weid AND y.status=1 AND s.openid=:openid ORDER BY y.reid DESC', array(":weid" => $weid, ":openid" => $openid), 'reid');
        $sql = 'SELECT * FROM ' . tablename($this->tb_info) . " WHERE reid=:reid {$where}ORDER BY createtime DESC,rerid DESC LIMIT " . ($pindex - 1) * $psize . ',' . $psize;
        $c_tishi = '<span class="weui-badge right" style="margin-5px; absolute; 5px; 5px;">有新咨询</span>';
        $fields = pdo_fetchall($sql, $params);
        $custom = array("msgtype" => "text", "text" => array("content" => urlencode($info)), "touser" => $row['openid']);
        $childlist = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_data) . ' WHERE rerid IN (\'' . implode('\',\'', is_array($rerid) ? $rerid : array($rerid)) . '\') AND `reid`=:reid', array(":reid" => $reid));
        $settings = uni_setting($_W['uniacid'], array("creditnames", "creditbehaviors"));
        $activity = $this->get_form($reid);
        $acc = WeAccount::create($_W['acid']);
        $custom = array("msgtype" => "text", "text" => array("content" => urlencode($info)), "touser" => $row['openid']);
        $sql = 'SELECT * FROM ' . tablename($this->tb_data) . " WHERE `reid`=:reid AND `rerid`='{$row['rerid']}' AND `refid` IN ({$fids})";
        $huifu = $status['name'] . $kfinfo . $revoice;

        $outurl = !empty($par['noticeurl']) ? $par['noticeurl'] : $_W['siteroot'] . 'app/' . $this->createMobileUrl('mydayu_form', array("op" => "detail", "rerid" => $rerid, "id" => $row['reid'])); if (!is_error($acc)) {}

        $dayu_form = pdo_fetch('SELECT * FROM ' . tablename($this->tb_form) . ' WHERE reid = :reid', array(":reid" => $row['reid'])); $row['fields'] = array(); $linkage = iunserializer($row['linkage']); $isstaff = pdo_get($this->tb_staff, array("weid" => $weid, "openid" => $openid), array("id")); $record = array(); foreach ($rows as $key => $val) {$rows[$key]['kf'] = mc_fansinfo($val['kf'], $acid, $weid); $rows[$key]['status'] = $this->get_status($val['reid'], $val['status']); $rows[$key]['user'] = mc_fansinfo($val['openid'], $acid, $weid); $rows[$key]['comment'] = !empty($par['comment']) && pdo_tableexists('dayu_comment') && !empty($val['commentid']) ? $this->get_comment($val['commentid']) : ''; }

        $formdata = $this->order_foreach ($row['reid'], $rerid); foreach ($childlist as $reply => $r) {$children[$r['rerid']][] = $r; if (empty($r['rerid'])) {}

        load()->func('tpl'); if ($activity['guanli'] == '0') {

        $state = array(); $params2[':openid'] = $openid; $record['kf'] = $openid; if (!pdo_tableexists('dayu_consult')) {}

        $behavior = $settings['creditbehaviors']; $this->showMessage('记录不存在或是已经被删除！'); $revoice = !empty($_GPC['revoice']) ? '\\n有语音答复' : ''; foreach ($fields as $f) {$ds[$f['refid']]['loc'] = $f['loc']; $ds[$f['refid']]['type'] = $f['type']; $ds[$f['refid']]['fid'] = $f['title']; $ds[$f['refid']]['refid'] = $f['refid']; $fids[] = $f['refid']; }

        $where2 = 'weid=:weid and status = 1'; $url = $outurl; $acc = WeAccount::create($_W['acid']); $row['voices'] = $row['voice']; $record['yuyuetime'] = TIMESTAMP; require MODULE_ROOT . '/fans.mobile.php'; $status = $this->get_status($row['reid'], $row['status']); $children = array(); $params[':reid'] = $row['reid']; $template = array("touser" => $row['openid'], "template_id" => $dayu_form['m_templateid'], "url" => $outurl, "topcolor" => "#FF0000", "data" => array("first" => array("value" => urlencode($dayu_form['mfirst']), "color" => "#743A3A"), "keyword1" => array("value" => urlencode($row['member']), "color" => "#000000"), "keyword2" => array("value" => urlencode($row['mobile']), "color" => "#000000"), "keyword3" => array("value" => urlencode(date('Y-m-d s', TIMESTAMP)), "color" => "#000000"), "keyword4" => array("value" => urlencode($huifu), "color" => "#FF0000"), "remark" => array("value" => urlencode($dayu_form['mfoot']), "color" => "#008000"))); foreach ($ds as $value) {foreach ($fdatas as $fdata) {$row['fields'][$value['refid']] .= $fdata['data'];

        $status = $_GPC['status']; include $this->template('manage_form'); setcookie('r_submit', $_GPC['repeat']); $linkage['l2'] = $this->get_linkage($linkage['l2'], ''); return error(-1, $acc['message']); foreach ($formdata as $index => $v) { $alldata[] = $v['title'] . ':' . $v['data'] . ','; }

        $kfinfo = !empty($record['kfinfo']) ? '\\n客服回复：' . $record['kfinfo'] : ''; if (!(!empty($par['wxcard']) && pdo_tableexists('dayu_wxcard_activity'))) {}

        $operation = !empty($_GPC['op']) ? $_GPC['op'] : 'display'; $wxcard = pdo_fetchall('SELECT * FROM ' . tablename('dayu_wxcard') . ' WHERE weid = :weid AND cid=:cid AND status=1', array(":weid" => $_W['uniacid'], ":cid" => $par['wxcard'])); $rerid = intval($_GPC['rerid']); $comment = pdo_get('dayu_comment', array("weid" => $weid, "id" => $row['commentid']), array()); $titles = $activity['title']; $rows = pdo_fetchall($sql, $params, 'rerid'); setcookie('r_submit', $_GPC['repeat']); $params = array(); if ($status != '') {
        $acc = notice_init(); $row['user'] = mc_fansinfo($row['openid'], $acid, $weid); $this->showMessage('非法访问！你不是管理员。');

        $alldata = array(); $acc = notice_init(); $staff = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_staff) . ' WHERE reid = :reid ORDER BY `id` DESC', array(":reid" => $reid)); $total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_info) . " WHERE reid = :reid {$where} ", $params); if (!(pdo_tableexists('dayu_comment') && !empty($par['comment']) && empty($row['commentid']) && $_GPC['status'] == '3')) {}

        $status = $this->get_status($reid, $_GPC['status']); $url = $outurl; $setting = $this->module['config']; $reid = intval($_GPC['id']); $params2[':weid'] = $weid; mc_credit_update(mc_openid2uid($row['openid']), $behavior['activity'], $activity['credit'], array(0, $activity['title'])); $dayu_form['content'] = htmlspecialchars_decode($dayu_form['content']); $arr2 = array("0", "1", "2", "3", "8"); $picker = 1; $fdatas = pdo_fetchall($sql, $params); $where .= " and kf='{$openid}'"; $row['file'] = iunserializer($row['file']); return error(-1, $acc['message']); $msg .= $wxcard_post['msg']; $params = array(); $consult = pdo_fetch('SELECT id,createtime FROM ' . tablename('dayu_consult') . ' WHERE infoid = :infoid AND rid = \'0\' ORDER BY createtime DESC', array(":infoid" => $row['rerid'])); $psize = 10; foreach ($wxcard as &$val) { $val['coupon'] = pdo_get('coupon', array("uniacid" => $_W['uniacid'], "card_id" => $val['cardid']), array("id", "title")); }

        if (!($par['sms'] != '0' && $par['paixu'] != '2' && !empty($activity['smstype']))) {}

        if (!(!empty($par['comment']) && pdo_tableexists('dayu_comment') && !empty($row['commentid']))) {}

        if ($value['type'] == 'reside') {

        if ($row['icredit'] != '1' && $par['icredit'] == '1' && $activity['credit'] != '0.00' && $_GPC['status'] == '3') {

        if (!empty($repeat)) {}

        foreach ($list as $key => $val) { $list[$key]['count'] = $this->count_form($val['reid'], 2); }

        if (!is_error($acc)) {}

        if (!(pdo_tableexists('dayu_kami') && $row['kid'])) {}

        if (!is_array($wxcard)) {}

        foreach ($_GPC['thumb'] as $thumb) { $th[] = tomedia($thumb); }

        foreach ($arr2 as $index => $v) { $state[$v][] = $this->get_status($reid, $v); }
        ihttp_post(murl('entry', array("do" => "Notice", "id" => $activity['smstype'], "m" => "dayu_sms"), true, true), $sms_data);
        mc_notice_credit1($row['openid'], mc_openid2uid($row['openid']), $activity['credit'], $log);
        unset($children[$reply]); }

        mc_group_update(mc_openid2uid($row['openid'])); $repeat = $_COOKIE['r_submit']; $yuyuetime = !empty($row['yuyuetime']) ? date('Y-m-d i', $row['yuyuetime']) : date('Y-m-d i', TIMESTAMP); $linkage['l1'] = $this->get_linkage($linkage['l1'], ''); $this->send_template_message(urldecode(json_encode($template))); $face = mc_fansinfo($row['openid'], $acid, $weid); if (!is_array($linkage)) {}
        $row['thumb'] = iunserializer($row['thumb']); $info = $activity['title'] . ' 处理结果 ' . $status['name'] . '
        '; $pager = $this->pagination($total, $pindex, $psize); $manage_footer = 1;

        '; $params2 = array(); if ($_GPC['status'] == '3' && $par['icredit'] == '1') {
        $record['rethumb'] = iserializer($th);
        $this->showMessage($activity['information'], $this->createMobileUrl('mydayu_form', array("id" => $reid)));
        $row['rethumb'] = iunserializer($row['rethumb']);
        $params[':reid'] = $reid;
        $info .= "<a href='{$url}'>现在去评价</a>";
        $where .= " and status={$status}";
        $info .= "姓名：{$row['member']}
        手机：{$row['mobile']}

        受理结果：{$huifu}"; if (!(!empty($consult['id']) && $consult['createtime'] > $consultr['createtime'])) {}
        $row['fields'][$value['refid']] = ''; }
        $info .= "{$par['commenttitle']}

        "; if (empty($row['rethumb'])) {}

        $this->showMessage('非法访问，空'); pdo_update('dayu_form_info', $record, array("rerid" => $rerid)); if (!empty($fields)) {}
        $record['kfinfo'] = $_GPC['kfinfo']; $where2 .= ' and kfid = :openid'; $info .= "<a href='{$url}'>点击查看详情</a>";

        $this->showMessage('非法访问！你不是管理员。', $this->createMobileUrl('index'), 'info'); if (!(!empty($par['wxcard']) && !empty($_GPC['wxcard']) && pdo_tableexists('dayu_wxcard'))) {}

        $where .= ' and ( status=2 or status=-1 )'; $record['icredit'] = 1; $kami = pdo_get('dayu_sendkami', array("weid" => $weid, "id" => $row['kid']), array()); $row['yuyuetime'] = !empty($row['yuyuetime']) ? date('Y年m月d日 i', $row['yuyuetime']) : '客服尚未受理'; $custom = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_custom) . ' WHERE weid = :weid ORDER BY displayorder DESC', array(":weid" => $weid)); $la = iunserializer($activity['linkage']); if ($openid != $activity['kfid'] && $activity['guanli'] == '0') {

        $this->showMessage('修改成功' . $msg, referer(), 'success'); $CustomNotice = $acc->sendCustomNotice($custom); $wxcard_post = ihttp_post(murl('entry', array("do" => "recorded", "couponid" => $_GPC['wxcard'], "m" => "dayu_wxcard"), true, true), array("addons" => $_W['current_module']['name'], "openid" => $row['openid'], "infoid" => $rerid)); $ds = $fids = array(); if (empty($_GPC['repeat'])) {}

        $record['revoice'] = empty($row['revoice']) ? $_GPC['revoice'] : $row['revoice']; $kami = pdo_get('dayu_kami', array("weid" => $_W['uniacid'], "id" => $row['kid']), array()); $list = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_form) . " WHERE {$where2}ORDER BY reid DESC", $params2, 'reid'); $this->showMessage('非法访问.'); $CustomNotice = $acc->sendCustomNotice($custom); if (!is_array($_GPC['thumb'])) {}

        $row['createtime'] = !empty($row['createtime']) ? date('Y年m月d日 i', $row['createtime']) : '时间丢失'; $row['member'] = !empty($row['member']) ? $row['member'] : $row['user']['nickname']; foreach ($fdatas as $fd) { $row['fields'][$fd['refid']] = $fd['data']; }}
        }

        public function isHy($openid) {global $_W; if (empty($card)) {}
        load()->model('mc'); return false; $card = pdo_fetch('SELECT * FROM ' . tablename('mc_card_members') . ' WHERE uniacid=:uniacid AND openid = :openid ', array(":uniacid" => $_W['uniacid'], ":openid" => $openid)); return true; }

        public function send_template_message($data) {load()->func('communication'); $url = 'https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=' . $access_token; return error(-1, "访问公众平台接口失败, 错误: {$response['message']}"); return error(-1, "访问微信接口错误, 错误代码: {$result['errcode']}, 错误信息: {$result['errmsg']},信息详情：{$this->error_code($result['errcode'], $result['errmsg'])}"); if (empty($result)) {} {

        load()->class('weixin.account'); return error(-1, "接口调用失败, 原数据: {$response['meta']}"); global $_W, $_GPC; return true; $result = @json_decode($response['content'], true); if (!is_error($response)) {}
        $response = ihttp_request($url, $data); $access_token = WeAccount::token(); }

        if (!empty($result['errcode'])) {}
        }

        public function AjaxMessage($msg, $status = 0) {$result = array("message" => $msg, "status" => $status); echo json_encode($result); exit; }
        ', "color" => "#743A3A"), "keyword1" => array("value" => $row['member']), "keyword2" => array("value" => $row['mobile']), "keyword3" => array("value" => date('Y-m-d s', TIMESTAMP)), "keyword4" => array("value" => $activity['state3']), "remark" => array("value" => '

        ' . $activity['mfoot'], "color" => "#008000")); if (!empty($id)) {}
        $url = !empty($par['noticeurl']) ? $par['noticeurl'] : $_W['siteroot'] . 'app/' . $this->createMobileUrl('mydayu_form', array("op" => "detail", "rerid" => $id, "id" => $reid)); $acc->sendTplNotice($row['openid'], $activity['m_templateid'], $data, $url, '#FF0000'); $status = $_GPC['status']; $this->AjaxMessage('更新成功!', 1); $activity = pdo_fetch($sql, $params); $reid = intval($_GPC['reid']); $par = iunserializer($activity['par']); $row = pdo_fetch('SELECT * FROM ' . tablename($this->tb_info) . ' WHERE rerid = :rerid', array(":rerid" => $id)); $this->AjaxMessage('更新失败!', 0); global $_W, $_GPC; $params[':weid'] = $_W['uniacid']; $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `reid`=:reid'; }

        public function doMobileLocate() {$result['message'] = ''; $result['lat'] = $setting['contact']['lat']; $result['name'] = $setting['contact']['company']; $result['lng'] = $setting['contact']['lng']; if ($_GPC['op'] == 'contact') {
        require MODULE_ROOT . '/fans.mobile.php'; global $_W, $_GPC; $result = array("error" => "error", "message" => "", "data" => ""); $result['mobile'] = $setting['contact']['mobile']; die(json_encode($result)); $result['address'] = $setting['contact']['province'] . $setting['contact']['city'] . $setting['contact']['district'] . $setting['contact']['address']; }

        function pagination($tcount, $pindex, $psize = 15, $url = "", $context = array("before" => 5, "after" => 4, "ajaxcallback" => "")) {$html .= "<div class=\"pager-first\"><a {$pdata['faa']} {

        global $_W; $cindex = min($cindex, $pdata['tpage']); $pdata['faa'] = 'href="javascript:;" onclick="p(\'' . $_W['script_name'] . $url . '\', \'' . $pdata['findex'] . '\', ' . $context['ajaxcallback'] . ')"'; $pdata['paa'] = 'href="?' . str_replace('*', $pdata['pindex'], $url) . '"'; $pdata['laa'] = 'href="?' . str_replace('*', $pdata['lindex'], $url) . '"'; if ($pdata['tpage'] <= 1) {
        $url = $_W['script_name'] . '?' . http_build_query($_GET); $html .= "<div class=\"pager-cen\">{$pindex} / " . $pdata['tpage'] . '</div>';
        class=\"pager-nav\">首页</a></div>"; $pdata['tcount'] = $tcount; $html .= "<div class=\"pager-next\"><a {$pdata['naa']}
        class=\"pager-nav\">下一页</a></div>"; $html .= '<div class="pager-left">'; $_GET['page'] = $pdata['lindex'];
        $pdata['naa'] = 'href="?' . str_replace('*', $pdata['nindex'], $url) . '"'; $html = '<div class="pager">'; $pdata['paa'] = 'href="javascript:;" onclick="p(\'' . $_W['script_name'] . $url . '\', \'' . $pdata['pindex'] . '\', ' . $context['ajaxcallback'] . ')"'; $pdata['cindex'] = $cindex; $html .= "<div class=\"pager-pre\"><a {$pdata['paa']}>上一页</a></div>"; $pdata['nindex'] = $cindex < $pdata['tpage'] ? $cindex + 1 : $pdata['tpage'];
        $pdata['naa'] = 'href="javascript:;" onclick="p(\'' . $_W['script_name'] . $url . '\', \'' . $pdata['nindex'] . '\', ' . $context['ajaxcallback'] . ')"'; $pdata['laa'] = 'href="' . $_W['script_name'] . '?' . http_build_query($_GET) . '"'; $html .= '</div>'; $_GET['page'] = $pdata['findex']; $pdata['faa'] = 'href="' . $_W['script_name'] . '?' . http_build_query($_GET) . '"'; $cindex = max($cindex, 1); $pdata['faa'] = 'href="?' . str_replace('*', $pdata['findex'], $url) . '"'; $pdata['paa'] = 'href="' . $_W['script_name'] . '?' . http_build_query($_GET) . '"';
        $_GET['page'] = $pdata['nindex']; $html .= '</div>'; $html .= '</div>'; $pdata = array("tcount" => 0, "tpage" => 0, "cindex" => 0, "findex" => 0, "pindex" => 0, "nindex" => 0, "lindex" => 0, "options" => ""); $html .= '<div class="clr"></div></div>'; $html .= '<div class="pager-next" style="100%"><a href="###">尾页</a></div>'; $_GET['page'] = $pdata['pindex']; $html .= '<div class="pager-right">'; $context['isajax'] = true; $pdata['tpage'] = ceil($tcount / $psize); $html .= "<div class=\"pager-end\"><a {$pdata['laa']}
        class=\"pager-nav\">尾页</a></div>"; $html .= '</div>'; $pdata['lindex'] = $pdata['tpage']; }
        return $html; $cindex = $pindex; $pdata['pindex'] = $cindex > 1 ? $cindex - 1 : 1; $pdata['laa'] = 'href="javascript:;" onclick="p(\'' . $_W['script_name'] . $url . '\', \'' . $pdata['lindex'] . '\', ' . $context['ajaxcallback'] . ')"'; $html .= '<div class="pager-pre" style="100%"><a href="###">第一页</a></div>'; $pdata['findex'] = 1; $pdata['naa'] = 'href="' . $_W['script_name'] . '?' . http_build_query($_GET) . '"'; $html .= '<div class="pager-left">'; return ''; $html .= '<div class="pager-right">';
        }

        public function doMobileFansUs() {include $this->template('fans_us'); $qrcodesrc = tomedia('qrcode_' . $_W['acid'] . '.jpg'); global $_W, $_GPC; require MODULE_ROOT . '/fans.mobile.php'; }
        require MODULE_ROOT . '/fans.mobile.php'; header('' . $this->createMobileUrl('FansUs'), true, 301); $p = pdo_fetch('SELECT follow FROM ' . tablename('mc_mapping_fans') . ' WHERE uniacid = :weid AND openid = :openid LIMIT 1', array(":weid" => $_W['uniacid'], ":openid" => $_W['openid'])); return true; global $_GPC, $_W; }

        private function checkauth3($openid, $nickname, $headimgurl) {$default_groupid = pdo_fetchcolumn('SELECT groupid FROM ' . tablename('mc_groups') . ' WHERE uniacid = :uniacid AND isdefault = 1', array(":uniacid" => $_W['uniacid'])); $fanid = pdo_insertid(); $post = array("acid" => $_W['acid'], "uniacid" => $_W['uniacid'], "nickname" => $nickname, "openid" => $_W['fans']['openid'], "salt" => random(8), "follow" => 0, "updatetime" => TIMESTAMP, "tag" => base64_encode(iserializer($_W['fans']))); if (empty($fan['uid'])) {} {

        if (empty($_W['member']['uid']) && empty($settings['passport']['focusreg'])) {}
        pdo_insert('mc_members', $data); $_W['member']['uid'] = $uid; }

        $_W['member']['uid'] = $fan['uid']; $fanid = $fan['fanid']; $uid = pdo_insertid(); pdo_insert('mc_mapping_fans', $post); $data = array("uniacid" => $_W['uniacid'], "email" => $email, "salt" => random(8), "groupid" => $default_groupid, "createtime" => TIMESTAMP, "password" => md5($message['from'] . $data['salt'] . $_W['config']['setting']['authkey']), "avatar" => $headimgurl, "nickname" => $nickname); $fan = pdo_get('mc_mapping_fans', array("uniacid" => $_W['uniacid'], "openid" => $openid)); global $_W, $engine; $settings = cache_load('' . $_W['uniacid']); $_W['fans']['uid'] = $fan['uid']; $email = md5($oauth['openid']) . '@vqiyi.cn'; pdo_update('mc_mapping_fans', array("uid" => $uid), array("fanid" => $fanid)); $_W['fans']['uid'] = $uid; checkauth(); if (!empty($fan)) {}
        }

        private function checkAuth2() {$_W['member']['uid'] = $fan['uid']; $_W['member']['uid'] = $uid; $uid = pdo_insertid(); $setting = cache_load('' . $_W['uniacid']); $_W['fans']['uid'] = $uid; if (empty($_W['member']['uid']) && empty($setting['passport']['focusreg'])) {} {

        if (!empty($fan)) {}

        pdo_insert('mc_members', array("uniacid" => $_W['uniacid'])); pdo_update('mc_mapping_fans', array("uid" => $uid), array("fanid" => $fanid)); $fan = pdo_get('mc_mapping_fans', array("uniacid" => $_W['uniacid'], "openid" => $_W['openid'])); $_W['fans']['uid'] = $fan['uid']; $post = array("acid" => $_W['acid'], "uniacid" => $_W['uniacid'], "openid" => $_W['openid'], "updatetime" => time(), "follow" => 0); if (empty($fan['uid'])) {}
        checkauth(); $fanid = $fan['fanid']; global $_W; }
        }

        public function get_fields($fid) { global $_GPC, $_W; return pdo_get($this->tb_field, array("refid" => $fid), array()); }

        public function get_linkage($id, $type) {
        return pdo_fetch('SELECT * FROM ' . tablename($this->tb_linkage) . ' WHERE id = :id LIMIT 1', array(":id" => $id)); return pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_linkage) . ' WHERE reid = :reid', array(":reid" => $id)); }

        public function get_role($uid) { global $_GPC, $_W; return pdo_fetch('SELECT username, uid FROM ' . tablename('users') . ' WHERE uid = :uid LIMIT 1', array(":uid" => $uid)); }

        public function doMobileUpthumb() {$result['msg'] = '更新头像失败'; message($result, '', 'ajax'); $result['msg'] = '更新头像成功'; $result['status'] = 'error'; if ($mode == 'member') { {

        global $_W, $_GPC; $result['status'] = 'success'; if (mc_update($_GPC['uid'], $data) === false) {}
        $mode = $_GPC['mode']; load()->model('mc'); $data = array("avatar" => $_GPC['thumb']); }
        }

        public function doMobileUploadFiles() {global $_GPC, $_W; message($result, '', 'ajax'); $result['msg'] = $pathname . '上传成功'; load()->func('file'); foreach ($_FILES as $key => $files) {$size = intval($files['size']); $result['status'] = '0'; if ($files['error'] != 0) { {

        $pathname = $file['path']; $file = file_upload($files); message($result, '', 'ajax'); $result['message'] = $file['message']; if (!empty($files['name'])) {}
        $originname = $files['name']; exit; $result['msg'] = '上传失败, 请重试.'; $result['status'] = '0'; die(json_encode($result)); $ext = pathinfo($files['name'], PATHINFO_EXTENSION); }

        message($result, '', 'ajax'); $result['msg'] = '上传失败, 请选择要上传的文件！'; exit; $ext = strtolower($ext); if (!is_error($file)) {}
        $result['status'] = '1'; } }
        }

        if (!empty($options['class_extra'])) {}

        exit('图片上传目录错误,只能指定最多两级目录,如: "store","store/d1"'); $options['global'] = false; if (!(isset($options['dest_dir']) && !empty($options['dest_dir']))) {}

        if (!empty($default)) {}
        $options['class_extra'] = ''; $s .= '
        <div class="input-group ' . $options['class_extra'] . '">
        <input type="text" name="' . $name . '" value="' . $value . '"' . ($options['extras']['text'] ? $options['extras']['text'] : '') . ' id="re-image" class="form-control" autocomplete="off">
        <span class="input-group-btn">
        <button class="btn btn-default" type="button" onclick="showImageDialog(this);">选择图片</button>
        </span>
        </div>
        <div class="col-xs-12 ' . $options['class_extra'] . '" style="margin-.5em;">
        <em class="close" style="absolute; 0px; -14px;font-18px; #333;" title="删除这张图片" onclick="deleteImage(this)">× 删除</em>

        </div>'; $val = tomedia($value); $options['thumb'] = !empty($options['thumb']); return $s; $options['global'] = true; $val = $default; $options['direct'] = true; if (preg_match('/^\\w+([\\/]\\w+)?$/i', $options['dest_dir'])) {}
        define('TPL_INIT_IMAGE', true); $s = '
        <script type=\"text/javascript\">

        function showImageDialog(elm, opts, options) { {

        if (url.url){

        if (img.length > 0){

        if (url.media_id){

        if (img.length > 0){
        require([\"util\"], function(util){
        var btn = $(elm);
        var ipt = btn.parent().prev();
        var val = ipt.val();
        var img = ipt.parent().next().children();
        options = ' . str_replace('"', '\'', json_encode($options)) . ';
        util.image(val, function(url){
        img.get(0).src = url.url;
        ipt.val(url.attachment);
        ipt.attr(\"filename\",url.filename);
        ipt.attr(\"url\",url.url);
        img.get(0).src = \"\";
        ipt.val(url.media_id);
        }, null, options);
        });
        }

        function deleteImage(elm){ {
        require([\"jquery\"], function($){
        $(elm).prev().attr(\"src\", \"./resource/images/nopic.jpg\");
        $(elm).parent().prev().find(\"input\").val(\"\");
        });
        }

        </script>'; if (!empty($options['global'])) {}

        $default = './resource/images/nopic.jpg'; $s = ''; if (!isset($options['thumb'])) {}

        global $_W; $options['multiple'] = false; if (defined('TPL_INIT_IMAGE')) {}} ?>