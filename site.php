<?php
 goto DOjgm; DOjgm: defined('IN_IA') or exit('Access Denied'); goto abxj_; pfjif: function dayu_form_field_district($name, $values = array()) { goto lk9eZ; N7F__: ssKR9: goto Tmob9; hY2X3: CuKdn: goto gyVvU; XSzCt: $html .= '
		<div class="tpl-district-container" style="display: block;">
			<div class="col-lg-4">
				<select name="' . $name . '[province]" data-value="' . $values['province'] . '" class="tpl-province">
				</select><i></i>
			</div>
			<div class="col-lg-4">
				<select name="' . $name . '[city]" data-value="' . $values['city'] . '" class="tpl-city">
				</select><i></i>
			</div>
			<div class="col-lg-4">
				<select name="' . $name . '[district]" data-value="' . $values['district'] . '" class="tpl-district">
				</select><i></i>
			</div>
		</div>'; goto AI3hM; GC2eX: $values = array("province" => "", "city" => "", "district" => ""); goto hY2X3; GwTlp: $values['city'] = ''; goto ZepCg; gyVvU: if (!empty($values['province'])) { goto ssKR9; } goto UHmnS; Tmob9: if (!empty($values['city'])) { goto qn8Yb; } goto GwTlp; zdNB7: $html .= '
		<script type=\"text/javascript\">
			require([\"jquery\", \"district\"], function($, dis){
				$(\".tpl-district-container\").each(function(){
					var elms = {};
					elms.province = $(this).find(\".tpl-province\")[0];
					elms.city = $(this).find(\".tpl-city\")[0];
					elms.district = $(this).find(\".tpl-district\")[0];
					var vals = {};
					vals.province = $(elms.province).attr(\"data-value\");
					vals.city = $(elms.city).attr(\"data-value\");
					vals.district = $(elms.district).attr(\"data-value\");
					dis.render(elms, vals, {withTitle: true});
				});
			});
		</script>'; goto HO0zO; HO0zO: define('TPL_INIT_DISTRICT', true); goto ABbff; ZepCg: qn8Yb: goto QaFqG; xlvPz: if (defined('TPL_INIT_DISTRICT')) { goto Ja3Nu; } goto zdNB7; UHmnS: $values['province'] = ''; goto N7F__; qoSSl: $values['district'] = ''; goto h4jEE; QaFqG: if (!empty($values['district'])) { goto kUoq0; } goto qoSSl; RasYa: if (!(empty($values) || !is_array($values))) { goto CuKdn; } goto GC2eX; AI3hM: return $html; goto KTF52; lk9eZ: $html = ''; goto xlvPz; h4jEE: kUoq0: goto XSzCt; ABbff: Ja3Nu: goto RasYa; KTF52: } goto VPssP; jgy4g: function tpl_form_field_images2($name, $value, $title) { goto I77Mf; I77Mf: $thumb = empty($value) ? 'images/global/nopic.jpg' : $value; goto yx5EC; UAGHo: return $html; goto EF1yy; yx5EC: $thumb = tomedia($thumb); goto k0fKS; k0fKS: $html = "	<li class=\"mui-table-view-cell mui-media mui-col-xs-6\">
		<a href=\"javascript:;\" class=\"js-image-{$name}\">
			<span class=\"js-image-{$name}s\"><img class=\"mui-media-object\" src=\"{$thumb}\"></span>
			<div class=\"mui-media-body\">
									<input type=\"hidden\" id=\"{$name}\">
				<input class=\"weui_uploader_input\" type=\"file\" name=\"{$name}\" accept=\"image/*\" capture=\"camera\" value=\"{$title}\"></div>
		</a>
	</li>
<script>
	util.image(\$('.js-image-{$name}'), function(url){
		\$('.js-image-{$name}').prev().val(url.attachment);
		\$('.js-image-{$name}s').find('img').attr('src',url.url);
	}, {
		crop : false,
		multiple : false
	});
</script>"; goto UAGHo; EF1yy: } goto e_1fv; g_ZGE: define('TEMPLATE_WEUI', '../addons/dayu_form/template/weui/'); goto K5UGc; NDRzF: define('TEMPLATE_PATH', '../addons/dayu_form/template/style/'); goto g_ZGE; abxj_: define('MODULE_NAME', 'dayu_form'); goto NDRzF; K5UGc: require IA_ROOT . '/addons/dayu_form/inc/func/core.php'; goto n8l9n; VPssP: function notice_init() { goto gv3fk; cox_d: hbXuR: goto lTyXO; lswEb: return error(-1, '创建公众号操作对象失败'); goto cox_d; xanLw: if (!is_null($acc)) { goto hbXuR; } goto lswEb; xISUU: $acc = WeAccount::create(); goto xanLw; gv3fk: global $_W; goto xISUU; lTyXO: return $acc; goto QOVxS; QOVxS: } goto jgy4g; VmX7D: function dayu_fans_form($field, $value = "") { goto RSsTT; RSsTT: switch ($field) { case 'reside': case 'resideprovince': case 'residecity': case 'residedist': $html = dayu_form_field_district('reside', $value); goto kTJda; } goto ucoQR; ucoQR: RBBxQ: goto ie3Rk; M8cZe: return $html; goto W4uYv; ie3Rk: kTJda: goto M8cZe; W4uYv: } goto pfjif; n8l9n: class dayu_formModuleSite extends Core { function __construct() { goto iD42H; QM0EC: $this->_openid = $_COOKIE[$this->_auth2_openid]; goto CJ3KY; XdQBX: NyO3a: goto VfHKo; mqLVW: $this->_appid = $this->_account['key']; goto qw6B2; LHxju: $this->_appid = $_W['account']['key']; goto IAkmL; J6IVY: $oauth = $settings['oauth']; goto JDiZA; qm3Bx: $this->_appsecret = $_W['account']['secret']; goto P2HJZ; JDiZA: if (!(!empty($oauth) && !empty($oauth['account']))) { goto H6Wu5; } goto nKC7X; N01b6: $this->_appid = $_W['account']['key']; goto qm3Bx; kkObs: $this->_accountlevel = $account['level']; goto Z8e_s; CJ3KY: QMNpf: goto S3D0Y; WF_jD: $this->_auth2_headimgurl = 'auth2_headimgurl_' . $_W['uniacid']; goto LHxju; IAkmL: $this->_appsecret = $_W['account']['secret']; goto kkObs; yHL13: H6Wu5: goto tz30x; XJvAs: $this->_auth2_openid = 'auth2_openid_' . $_W['uniacid']; goto YW4wT; b9uu3: load()->model('mc'); goto xZVGX; qw6B2: $this->_appsecret = $this->_account['secret']; goto yHL13; YW4wT: $this->_auth2_nickname = 'auth2_nickname_' . $_W['uniacid']; goto WF_jD; HFXH7: $account = $_W['account']; goto XJvAs; tz30x: TkrEQ: goto hMg_t; iD42H: global $_W, $_GPC; goto b9uu3; F09Cd: $this->_weid = $_W['uniacid']; goto HFXH7; nKC7X: $this->_account = account_fetch($oauth['account']); goto mqLVW; Z8e_s: if (!isset($_COOKIE[$this->_auth2_openid])) { goto QMNpf; } goto QM0EC; xZVGX: $this->_openid = $_W['openid']; goto F09Cd; S3D0Y: if ($this->_accountlevel < 4) { goto NyO3a; } goto N01b6; P2HJZ: goto TkrEQ; goto XdQBX; VfHKo: $settings = uni_setting($this->_weid); goto J6IVY; hMg_t: } public function oauth2($url) { goto yfPzC; uJMxU: header("location:{$oauth2_code}"); goto aZpgI; edor3: lShLL: goto pkOFe; yfPzC: global $_GPC, $_W; goto pQvzD; h1kU3: $userinfo = $this->get_User_Info($from_user); goto cqiX_; qxSua: $code = $_GPC['code']; goto fMwqa; pkOFe: if (!(empty($userinfo) || !is_array($userinfo) || empty($userinfo['openid']) || empty($userinfo['nickname']))) { goto jkBf1; } goto bnmKN; WThdN: return $userinfo; goto AoOju; qBYxA: $this->showMessage('code获取失败.', '', '', '', ''); goto j5wk9; bx4F0: setcookie($this->_auth2_nickname, $userinfo['nickname'], time() + 3600 * 24); goto bT7ks; j5wk9: mTL4n: goto u71oP; D_54K: $state = 0; goto Cv3gH; QhWJe: $from_user = $token['openid']; goto h1kU3; cqiX_: $state = 1; goto SfTPk; r3XX5: jkBf1: goto AXOCg; u71oP: $token = $this->get_Authorization_Code($code, $url); goto QhWJe; bnmKN: echo '<h1>获取微信公众号授权失败[无法取得粉丝信息], 请稍后重试！ 公众平台返回原始数据: <br />' . $state . $userinfo['meta'] . '<h1>'; goto iCZI_; AXOCg: setcookie($this->_auth2_headimgurl, $userinfo['headimgurl'], time() + 3600 * 24); goto bx4F0; PopJa: setcookie($this->_auth2_sex, $userinfo['sex'], time() + 3600 * 24); goto WThdN; u_PMU: $oauth2_code = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' . $this->_appid . '&redirect_uri=' . urlencode($url) . '&response_type=code&scope=snsapi_userinfo&state=0#wechat_redirect'; goto uJMxU; iCZI_: exit; goto r3XX5; U7rZY: if (!($authkey == 0)) { goto Hr04I; } goto u_PMU; u78Mh: $userinfo = $this->get_User_Info($from_user, $token['access_token']); goto edor3; bT7ks: setcookie($this->_auth2_openid, $from_user, time() + 3600 * 24); goto PopJa; fMwqa: if (!empty($code)) { goto mTL4n; } goto qBYxA; pQvzD: load()->func('communication'); goto qxSua; aZpgI: Hr04I: goto u78Mh; Cv3gH: $authkey = intval($_GPC['authkey']); goto U7rZY; SfTPk: if (!($userinfo['subscribe'] == 0)) { goto lShLL; } goto D_54K; AoOju: } public function get_Access_Token() { goto NooK1; MKZfn: load()->classs('weixin.account'); goto vcvd1; MMvzX: return $access_token; goto NYCiv; ddHGj: $account = $_W['account']; goto Yug3D; qTN6N: $account = $this->_account; goto E8BTV; NooK1: global $_W; goto ddHGj; ZsIHe: $access_token = $accObj->fetch_token(); goto MMvzX; Yug3D: if (!($this->_accountlevel < 4)) { goto Yxjye; } goto e0zEs; e0zEs: if (empty($this->_account)) { goto qn0sx; } goto qTN6N; E8BTV: qn0sx: goto IYxYD; vcvd1: $accObj = WeixinAccount::create($account['acid']); goto ZsIHe; IYxYD: Yxjye: goto MKZfn; NYCiv: } public function get_Authorization_Code($code, $url) { goto jC4RK; qFpRt: echo '微信授权失败! 公众平台返回原始数据: <br>' . $error['meta']; goto xh8Dr; jC4RK: $oauth2_code = "https://api.weixin.qq.com/sns/oauth2/access_token?appid={$this->_appid}&secret={$this->_appsecret}&code={$code}&grant_type=authorization_code"; goto EF1b6; nly5e: wb7MD: goto Khoxt; x8XcK: header("location:{$oauth2_code}"); goto qFpRt; EF1b6: $error = ihttp_get($oauth2_code); goto BRnEj; BRnEj: $token = @json_decode($error['content'], true); goto SIYSD; xh8Dr: exit; goto nly5e; SIYSD: if (!(empty($token) || !is_array($token) || empty($token['access_token']) || empty($token['openid']))) { goto wb7MD; } goto mMwRp; Khoxt: return $token; goto boaWc; mMwRp: $oauth2_code = $url; goto x8XcK; boaWc: } public function get_User_Info($from_user, $ACCESS_TOKEN = "") { goto KGJ1P; kEA3T: $json = ihttp_get($url); goto OHXu8; Flyxf: $url = "https://api.weixin.qq.com/sns/userinfo?access_token={$ACCESS_TOKEN}&openid={$from_user}&lang=zh_CN"; goto B0Rto; KGJ1P: if ($ACCESS_TOKEN == '') { goto JyyC0; } goto Flyxf; vPQpB: fzOue: goto kEA3T; B0Rto: goto fzOue; goto pD1rS; J3PFF: return $userinfo; goto Fb1hF; OHXu8: $userinfo = @json_decode($json['content'], true); goto J3PFF; stkQ6: $url = "https://api.weixin.qq.com/cgi-bin/user/info?access_token={$ACCESS_TOKEN}&openid={$from_user}&lang=zh_CN"; goto vPQpB; pD1rS: JyyC0: goto gYeCQ; gYeCQ: $ACCESS_TOKEN = $this->get_Access_Token(); goto stkQ6; Fb1hF: } public function get_Code($url) { goto t2fG5; t2fG5: global $_W; goto FB_a9; eOXn5: header("location:{$oauth2_code}"); goto M4W4K; FB_a9: $url = urlencode($url); goto XYg5M; XYg5M: $oauth2_code = "https://open.weixin.qq.com/connect/oauth2/authorize?appid={$this->_appid}&redirect_uri={$url}&response_type=code&scope=snsapi_base&state=0#wechat_redirect"; goto eOXn5; M4W4K: } public function getHomeTiles() { goto W6nmX; Gdt2A: if (empty($list)) { goto D9FCt; } goto OsJ_h; W6nmX: global $_W; goto zlLn_; ULHkK: return $urls; goto eeyCz; hV2xE: XrTIs: goto ZHXEi; pLT5s: $list = pdo_fetchall('SELECT title, reid FROM ' . tablename($this->tb_form) . " WHERE weid = '{$_W['uniacid']}'"); goto Gdt2A; zlLn_: $urls = array(); goto pLT5s; OsJ_h: foreach ($list as $row) { $urls[] = array("title" => $row['title'], "url" => $_W['siteroot'] . 'app/' . $this->createMobileUrl('dayu_form', array("id" => $row['reid']))); qKCWT: } goto hV2xE; ZHXEi: D9FCt: goto ULHkK; eeyCz: } public function __call($name, $arguments) { goto IW3BF; Eqv4Y: CEm3f: goto q103D; Ox3ry: $fromurl = urldecode($_GPC['fromurl']); goto TYrk2; EH6jC: avFb2: goto BsduP; BodXx: Eqt0J: goto HdOJa; iEdNI: $avatar = $_W['attachurl'] . 'images/global/noavatar_middle.gif'; goto O02Vv; VEigm: $isMobile = stripos($name, 'doMobile') === 0; goto Vuzst; TYrk2: if (!empty($profile['avatar'])) { goto PCp8T; } goto J2HIi; ndvL1: $fun = strtolower(substr($name, 5)); goto vQsZY; xFvpp: $id = $_GPC['id']; goto v155O; RvKQo: $dir .= 'web/'; goto MULPF; obLGJ: $op = $operation = trim($_GPC['op']) ? trim($_GPC['op']) : 'display'; goto L1omZ; pmHpW: goto uPGza; goto BodXx; MULPF: require MODULE_ROOT . '/fans.web.php'; goto GtQah; EQJCd: $dayuset = $this->module['config']; goto obLGJ; HdOJa: $avatar = $fans['tag']['avatar']; goto cjoQH; x8E1f: if (tomedia('headimg_' . $_W['acid'] . '.jpg')) { goto z610u; } goto iEdNI; J2HIi: if (!empty($fans['tag']['avatar'])) { goto Eqt0J; } goto x8E1f; q103D: $file = $dir . $fun . '.php'; goto oBQPw; Fx0B4: return null; goto cQinK; tURsu: if (!$isMobile) { goto CEm3f; } goto t2fSh; Ur9WS: iHosc: goto qfjBc; oBQPw: if (!file_exists($file)) { goto avFb2; } goto hjgrW; MX_53: $fun = strtolower(substr($name, 8)); goto Eqv4Y; PfL63: PCp8T: goto hVDpR; LQtI_: $dir = MODULE_ROOT . '/inc/'; goto xFvpp; NU01K: require MODULE_ROOT . '/fans.mobile.php'; goto oagLP; L1omZ: if (!$isWeb) { goto AzwdZ; } goto RvKQo; YK01T: exit; goto EH6jC; hVDpR: $avatar = $profile['avatar']; goto pmHpW; g4_Z9: $avatar = tomedia('headimg_' . $_W['acid'] . '.jpg'); goto Ur9WS; Vuzst: if (!($isWeb || $isMobile)) { goto G9c22; } goto LQtI_; qfjBc: goto uPGza; goto PfL63; t2fSh: $dir .= 'mobile/'; goto NU01K; GtQah: load()->func('tpl'); goto ndvL1; O02Vv: goto iHosc; goto iQFH5; iQFH5: z610u: goto g4_Z9; hjgrW: require $file; goto YK01T; GxWzj: trigger_error("访问的方法 {$name} 不存在.", E_USER_WARNING); goto Fx0B4; vQsZY: AzwdZ: goto tURsu; v155O: $weid = $_W['uniacid']; goto EQJCd; BsduP: G9c22: goto GxWzj; d_Tc3: $isWeb = stripos($name, 'doWeb') === 0; goto VEigm; oagLP: $returnUrl = urlencode($_W['siteurl']); goto Ox3ry; cjoQH: uPGza: goto MX_53; IW3BF: global $_W, $_GPC; goto d_Tc3; cQinK: } public function doWebFansSearch() { goto XCFlc; r6zm2: $params[':nickname'] = "%{$kwd}%"; goto Xt9bW; mNSle: include $this->template('fanssearch'); goto EZ7_U; Xt9bW: $sql = 'SELECT * FROM ' . tablename('mc_mapping_fans') . " WHERE {$where} ORDER BY fanid DESC LIMIT 20"; goto vB101; chyP5: $pindex = max(1, intval($_GPC['page'])); goto uSV_0; uSV_0: $psize = 20; goto D8ez0; RFGv2: $params[':uniacid'] = $_W['uniacid']; goto r6zm2; D8ez0: $where = 'uniacid = :uniacid AND `nickname` LIKE :nickname'; goto RFGv2; vB101: $boss = pdo_fetchall($sql, $params); goto pj4FH; lAVH7: load()->model('mc'); goto ZKQu4; XCFlc: global $_W, $_GPC; goto lAVH7; pj4FH: foreach ($boss as &$row) { goto TqNVI; SVWSQ: $row['fans'] = mc_fansinfo($row['openid'], $_W['uniacid']); goto rxqwp; yd1ta: $r['openid'] = $row['openid']; goto j6Bp2; G54AO: $row['entry'] = $r; goto SVWSQ; j6Bp2: $r['follow'] = $row['follow']; goto GTAex; GTAex: $r['fanid'] = $row['fanid']; goto G54AO; rxqwp: ixkS7: goto GALxx; TqNVI: $r = array(); goto OYSb1; OYSb1: $r['nickname'] = $row['nickname']; goto yd1ta; GALxx: } goto JpOLY; JpOLY: ifDRH: goto mNSle; ZKQu4: $kwd = $_GPC['keyword']; goto chyP5; EZ7_U: } public function doWebQuery() { goto y8wTs; NEgHR: $ds = pdo_fetchall($sql, $params); goto FGAV9; Zo0b6: $kwd = $_GPC['keyword']; goto UIlTs; UIlTs: $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `title` LIKE :title ORDER BY reid DESC LIMIT 0,8'; goto jF2l0; y8wTs: global $_W, $_GPC; goto Zo0b6; FGAV9: foreach ($ds as &$row) { goto Eng7o; KtdZL: $r['thumb'] = $row['thumb']; goto dpXwQ; Eng7o: $r = array(); goto MSACE; EIVus: $row['entry'] = $r; goto qbnqd; MSACE: $r['title'] = $row['title']; goto A0tW7; A0tW7: $r['description'] = cutstr(strip_tags($row['description']), 50); goto KtdZL; qbnqd: c6J0K: goto DKJE1; dpXwQ: $r['reid'] = $row['reid']; goto EIVus; DKJE1: } goto onJS6; jKb1o: $params[':weid'] = $_W['uniacid']; goto ancYI; onJS6: CG7Xc: goto tZPX2; ancYI: $params[':title'] = "%{$kwd}%"; goto NEgHR; jF2l0: $params = array(); goto jKb1o; tZPX2: include $this->template('query'); goto dMwlz; dMwlz: } public function doWebLinkage() { goto BoCgx; KPV2y: $linkage = pdo_fetch('SELECT * FROM ' . tablename($this->tb_linkage) . " WHERE id = '{$id}'"); goto ClSaU; CFQLI: message('更新联动成功！', $this->createWebUrl('linkage', array("op" => "display", "reid" => $reid)), 'success'); goto dMFe1; xcD3_: if (!empty($parent)) { goto xqEXr; } goto YZnwY; RKRkU: $id = intval($_GPC['id']); goto KPV2y; bwQWb: $children = array(); goto XRA20; JMigh: aawpw: goto ymeio; KTz1s: $data = array("reid" => $reid, "title" => $_GPC['title'], "parentid" => intval($parentid), "displayorder" => intval($_GPC['displayorder'])); goto j_pVy; OZxJR: Ww6Pd: goto ZeeMC; K5KVU: $record['linkage'] = iserializer($data); goto jXLfI; ZL4zJ: goto F7mLC; goto CNR3t; aTblx: message('您没有权限进行该操作.'); goto syU2M; ULCXn: foreach ($_GPC['displayorder'] as $id => $displayorder) { pdo_update($this->tb_linkage, array("displayorder" => $displayorder), array("id" => $id)); XyPAM: } goto JMigh; Qpysq: TNEFW: goto KTz1s; yCEfH: if ($operation == 'post') { goto Ado1Q; } goto UBguX; XRA20: $linkage = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_linkage) . " WHERE reid = '{$reid}' ORDER BY parentid ASC, displayorder desc"); goto PZRpP; zlV98: message('保存成功', $this->createWebUrl('linkage', array("op" => "display", "reid" => $reid)), 'success'); goto zCAHd; vCVRS: pdo_update($this->tb_linkage, $data, array("id" => $id)); goto tBW91; DnqiF: FDLje: goto u7X7X; k5_1F: F7mLC: goto TXcP9; wFSzY: itoast('联动删除成功！', referer(), 'success'); goto OZxJR; KN3D6: Ado1Q: goto q76M4; y6Jzh: message('抱歉，请输入联动标题！'); goto Qpysq; TuF99: $linkage = array("displayorder" => 0); goto ZL4zJ; nyg5b: hzDLw: goto RKRkU; ymeio: message('联动排序更新成功！', $this->createWebUrl('linkage', array("op" => "display", "reid" => $reid)), 'success'); goto S7bNp; ij8uL: goto Ww6Pd; goto nyg5b; AV6Tr: $reid = intval($_GPC['reid']); goto NN0Fh; B9lzW: goto Ww6Pd; goto KN3D6; bilrt: eJy_x: goto wDL4I; S7bNp: C1_Kw: goto bwQWb; hLFce: $parent = pdo_fetch('SELECT id, title FROM ' . tablename($this->tb_linkage) . " WHERE id = '{$parentid}'"); goto xcD3_; ClSaU: if (!empty($linkage)) { goto aQmFG; } goto qDcCm; BoCgx: global $_GPC, $_W; goto LjZuQ; CNR3t: SFMda: goto XT7ZJ; t1u9j: $id = intval($_GPC['id']); goto Fjnjq; j_pVy: if (!empty($id)) { goto FDLje; } goto YkEQt; VCyUt: goto tzi4N; goto DnqiF; EOB_e: $id = pdo_insertid(); goto VCyUt; Cv1zm: pdo_delete($this->tb_linkage, array("id" => $id)); goto wFSzY; UBguX: if ($operation == 'delete') { goto hzDLw; } goto uhun3; sQXt_: xqEXr: goto bilrt; syU2M: Iniz9: goto Ky4Xo; s4IHY: if (!checksubmit('paixu')) { goto C1_Kw; } goto ULCXn; PZRpP: foreach ($linkage as $index => $item) { goto Gyh82; VodSO: $children[$item['parentid']][] = $item; goto mKvbD; m0y3e: ANk71: goto zamr5; dZGin: Qhufw: goto m0y3e; Gyh82: if (empty($item['parentid'])) { goto Qhufw; } goto VodSO; mKvbD: unset($linkage[$index]); goto dZGin; zamr5: } goto R_Ccs; Xik5L: $record = array(); goto K5KVU; jXLfI: pdo_update($this->tb_form, $record, array("reid" => $reid)); goto zlV98; w4FUG: if (!($setting['role'] == 1 && $_W['role'] == 'operator' && !$role)) { goto Iniz9; } goto aTblx; aYSSz: if (!checksubmit('submit')) { goto h12Mr; } goto rkAXl; fQXx2: $operation = !empty($_GPC['op']) ? $_GPC['op'] : 'display'; goto e3NkR; wDL4I: if (!checksubmit('submit')) { goto HI8v4; } goto fA2rt; tBW91: tzi4N: goto CFQLI; VP6xL: include $this->template('linkage'); goto B9lzW; rkAXl: $data = array("l1" => $_GPC['la1'], "l2" => $_GPC['la2']); goto Xik5L; qDcCm: itoast('抱歉，联动不存在或是已经被删除！', referer(), 'error'); goto YbJ0r; q76M4: $parentid = intval($_GPC['parentid']); goto t1u9j; XT7ZJ: $linkage = pdo_fetch('SELECT * FROM ' . tablename($this->tb_linkage) . " WHERE id = '{$id}'"); goto k5_1F; YbJ0r: aQmFG: goto Cv1zm; NN0Fh: $role = $this->get_isrole($reid, $_W['user']['uid']); goto w4FUG; qqamk: include $this->template('linkage'); goto ij8uL; fekoc: $la = iunserializer($activity['linkage']); goto aYSSz; lRQB0: require MODULE_ROOT . '/fans.web.php'; goto AV6Tr; LjZuQ: load()->func('tpl'); goto lRQB0; YZnwY: message('抱歉，上级联动不存在或是已经被删除！', $this->createWebUrl('linkage', array("op" => "post", "reid" => $reid)), 'error'); goto sQXt_; dMFe1: HI8v4: goto qqamk; fA2rt: if (!empty($_GPC['title'])) { goto TNEFW; } goto y6Jzh; u7X7X: unset($data['parentid']); goto vCVRS; Fjnjq: if (!empty($id)) { goto SFMda; } goto TuF99; uhun3: goto Ww6Pd; goto RBUf9; e3NkR: if ($operation == 'display') { goto RBZtx; } goto yCEfH; TXcP9: if (empty($parentid)) { goto eJy_x; } goto hLFce; Ky4Xo: $activity = pdo_get($this->tb_form, array("weid" => $_W['uniacid'], "reid" => $reid), array("title", "linkage")); goto fQXx2; RBUf9: RBZtx: goto fekoc; YkEQt: pdo_insert($this->tb_linkage, $data); goto EOB_e; R_Ccs: hrCxF: goto VP6xL; zCAHd: h12Mr: goto s4IHY; ZeeMC: } public function doMobileGetLinkage() { goto Sg6zb; HXXLS: message($result, '', 'ajax'); goto woZGx; zf5y1: message($result, '', 'ajax'); goto mlQH1; RBXY6: $result['status'] = 1; goto NV7Nu; W6JZi: $result['status'] = 0; goto T1y7w; T1y7w: $result['jss'] = '没有下级内容'; goto zf5y1; mlQH1: ogVVL: goto RBXY6; TkPiF: if (!empty($jss)) { goto ogVVL; } goto W6JZi; NV7Nu: $result['jss'] = $jss; goto HXXLS; nrjBn: $jss = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_linkage) . ' WHERE parentid = :parentid ORDER BY displayorder desc, id DESC', array(":parentid" => $_GPC['linkage1'])); goto TkPiF; Sg6zb: global $_GPC, $_W; goto nrjBn; woZGx: } public function doWebStaff() { goto esD5U; qRssl: $total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_staff) . ' WHERE ' . $where, $params); goto iJIG6; qFN_y: yvNSK: goto Y92tc; q5u3Z: bwE7o: goto pak_O; pak_O: B8yGG: goto rnk92; TrW60: wTzSI: goto PENgf; yTwHe: if ($op == 'post') { goto jfdEu; } goto eQmYz; RR6Kq: iB73h: goto xAJnR; xmG6N: if (!($setting['role'] == 1 && $_W['role'] == 'operator' && !$role)) { goto Il5_H; } goto xLZeB; EEoBa: foreach ($_GPC['ids'] as $k => $v) { goto fzDe8; fzDe8: $data = array("nickname" => trim($_GPC['nickname'][$k]), "openid" => trim($_GPC['openid'][$k]), "weid" => trim($_GPC['weid'][$k])); goto fwbKX; fwbKX: pdo_update('dayu_form_staff', $data, array("reid" => $reid, "id" => intval($v))); goto OwpAR; OwpAR: KxZMn: goto szEmn; szEmn: } goto cto_y; rnk92: include $this->template('staff'); goto KVErX; vJjU1: $data['nickname'] = $_GPC['nickname']; goto PXbwX; lIaug: $params[':reid'] = $reid; goto Q2eP5; uehY1: message('表单不存在或已删除', $this->createWebUrl('display'), 'error'); goto xHE6E; EJSBV: $pager = pagination($total, $pindex, $psize); goto y_M2B; sbrGT: if (!checksubmit('submit')) { goto sGfuZ; } goto EPLZb; On3yp: include $this->template('staff'); goto Sslt2; YKc83: if ($op == 'list') { goto wTzSI; } goto yTwHe; PXbwX: $data['openid'] = $_GPC['openid']; goto FwK7A; dB9mV: itoast('编辑成功', $this->createWebUrl('staff', array("op" => "list", "reid" => $reid)), 'success'); goto q5u3Z; KVErX: goto n472w; goto c2b2Y; cto_y: bMqPH: goto dB9mV; Sslt2: goto n472w; goto qFN_y; xLZeB: message('您没有权限进行该操作.'); goto SyWy6; EPLZb: $data['reid'] = $reid; goto vJjU1; SyWy6: Il5_H: goto PkR3z; cG7Q4: itoast('删除成功.', referer()); goto K_FkG; Q2eP5: if (empty($_GPC['keyword'])) { goto iB73h; } goto bIj3b; eQmYz: if ($op == 'staffdel') { goto yvNSK; } goto IbHvZ; esD5U: global $_W, $_GPC; goto qPSFb; y_M2B: if (!checksubmit('submit')) { goto B8yGG; } goto QiHe9; bIj3b: $where .= " AND nickname LIKE '%{$_GPC['keyword']}%'"; goto RR6Kq; c2b2Y: jfdEu: goto sbrGT; SrXs9: $weid = $_W['uniacid']; goto edmja; IbHvZ: goto n472w; goto TrW60; n8gHi: $op = trim($_GPC['op']) ? trim($_GPC['op']) : 'list'; goto SrXs9; QiHe9: if (empty($_GPC['ids'])) { goto bwE7o; } goto EEoBa; qPSFb: require MODULE_ROOT . '/fans.web.php'; goto n8gHi; K_FkG: n472w: goto cSZHC; OZv3H: if (empty($id)) { goto zXOgf; } goto Aa5AX; iJIG6: $lists = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_staff) . ' WHERE ' . $where . ' ORDER BY createtime DESC,id ASC LIMIT ' . ($pindex - 1) * $psize . ',' . $psize, $params, 'id'); goto EJSBV; DJm5u: $psize = 20; goto qRssl; xHE6E: RjKU6: goto YKc83; FwK7A: $data['weid'] = $_GPC['weid']; goto bA42a; yGU2m: pdo_insert('dayu_form_staff', $data); goto dlaU7; bA42a: $data['createtime'] = time(); goto yGU2m; PENgf: $where = ' reid = :reid'; goto lIaug; DG7wU: zXOgf: goto cG7Q4; edmja: $reid = intval($_GPC['reid']); goto gPz4H; bhQ5c: if (!empty($activity)) { goto RjKU6; } goto uehY1; cYXSM: sGfuZ: goto On3yp; dlaU7: itoast('添加客服成功', $this->createWebUrl('staff', array("reid" => $reid, "op" => "list")), 'success'); goto cYXSM; Aa5AX: pdo_delete('dayu_form_staff', array("id" => $id)); goto DG7wU; PkR3z: $activity = $this->get_form($reid); goto bhQ5c; gPz4H: $role = $this->get_isrole($reid, $_W['user']['uid']); goto xmG6N; xAJnR: $pindex = max(1, intval($_GPC['page'])); goto DJm5u; Y92tc: $id = intval($_GPC['id']); goto OZv3H; cSZHC: } public function doWebchangecheckedAjax() { goto nglW6; YU3Sa: exit('0'); goto jfSqU; yc7yF: message('您没有权限进行该操作.'); goto BcxrL; eBe9Q: if (!($setting['role'] == 1 && $_W['role'] == 'operator' && !$role)) { goto BpKzj; } goto yc7yF; PO1Rl: $change = $_GPC['change']; goto tRP5E; BcxrL: BpKzj: goto NZh6k; NZh6k: if (false !== pdo_update($this->tb_form, array($field => $change), array("reid" => intval($id), "weid" => $_W['uniacid']))) { goto hXGN2; } goto YU3Sa; RlvWu: IJhnf: goto NsCIv; viNUb: require MODULE_ROOT . '/fans.web.php'; goto zOfdM; G9bDS: hXGN2: goto k2M0I; zOfdM: $id = $_GPC['id']; goto gvzEQ; k2M0I: exit('1'); goto RlvWu; nglW6: global $_W, $_GPC; goto viNUb; tRP5E: $role = $this->get_isrole($id, $_W['user']['uid']); goto eBe9Q; gvzEQ: $field = $_GPC['field']; goto PO1Rl; jfSqU: goto IJhnf; goto G9bDS; NsCIv: } public function doWebEditkf() { goto oK00V; wA7Qn: exit; goto sTpB8; y97eW: CRlPN: goto y8jA7; oK00V: global $_W, $_GPC; goto mbmbu; G50eU: $openid = $_GPC['openid']; goto niWM1; Ua3Of: $nickname = $_GPC['nickname']; goto G50eU; a0I8p: $fff = pdo_fetchall('SELECT reid,title FROM ' . tablename($this->tb_form)); goto yK07q; gbvMk: include $this->template('kf_edit'); goto USGI3; y8jA7: if (!is_array($reid)) { goto J_bj7; } goto WspDI; ObmsA: if (!($setting['role'] == 1 && $_W['role'] == 'operator' && !$role)) { goto CRlPN; } goto FV_iZ; SN0_1: message('更改成功!', referer()); goto wA7Qn; sTpB8: xw6vD: goto a0I8p; niWM1: $role = $this->get_isrole($reid, $_W['user']['uid']); goto ObmsA; TdzaK: $fun = explode(',', $config['reid']); goto gbvMk; uurJP: J_bj7: goto DaRTm; cTQoT: kFakg: goto uurJP; mbmbu: require MODULE_ROOT . '/fans.web.php'; goto CkRzY; yK07q: $config = pdo_fetch('SELECT * from ' . tablename($this->tb_staff) . ' where id=' . $_GPC['id']); goto TdzaK; DaRTm: $actid = substr($actid, 0, strlen($actid) - 1); goto lX7iA; lV5y4: $reid = $_GPC['reid']; goto Ua3Of; CkRzY: if (!($_GPC['dopost'] == 'update')) { goto xw6vD; } goto lV5y4; FV_iZ: message('您没有权限进行该操作.'); goto y97eW; WspDI: foreach ($reid as $k => $v) { $actid = $v . ','; Pl4eF: } goto cTQoT; lX7iA: $a = pdo_update('dayu_form_staff', array("reid" => $actid, "nickname" => $nickname, "openid" => $openid), array("id" => $_GPC['id'])); goto SN0_1; USGI3: } public function doWebDetail() { goto w1M7S; vCdfm: lPhnO: goto T7D1W; BT2Es: mc_group_update(mc_openid2uid($row['openid'])); goto cTb05; Gfsxm: BIrXt: goto jQkX_; dDhDV: mExKV: goto Y0fAA; aShiO: $sms_data = array("mobile" => $row['mobile'], "title" => $activity['title'], "mname" => $row['member'], "mmobile" => $row['mobile'], "openid" => $row['openid'], "status" => $state['name'], "ptname" => $_W['account']['name'], "addons" => $_W['current_module']['name'], "content" => $alldata); goto BFaaA; Semvq: $row = pdo_fetch($sql, $params); goto VeiLX; kcelK: $linkage['l1'] = $this->get_linkage($linkage['l1'], ''); goto r_KS6; QBV38: $kami = pdo_get('dayu_sendkami', array("weid" => $_W['uniacid'], "id" => $row['kid']), array()); goto ZzFaw; a42BC: message('访问非法.'); goto XapQt; AtzFV: $huifu = $state['name'] . $kfinfo . $revoice; goto rlLBy; iDM9Q: $acc = WeAccount::create($_W['acid']); goto wqaAT; MIq9v: $kami = pdo_get('dayu_kami', array("weid" => $_W['uniacid'], "id" => $row['kid']), array()); goto d5HrN; Fltq6: $info .= "<a href='{$url}'>现在去评价</a>"; goto nN8Oh; wHdNe: $record['icredit'] = 1; goto xIipI; IsRTf: H270z: goto lqUQ5; bERmg: $linkage = iunserializer($row['linkage']); goto hPIgW; y5Vfz: if (!is_array($_GPC['rethumb'])) { goto RNPvx; } goto YMD82; K7MGc: itoast('修改成功', referer(), 'success'); goto y8nZV; aphJp: if (!empty($activity)) { goto mExKV; } goto p6THv; JkLAN: if (!(pdo_tableexists('dayu_comment') && !empty($par['comment']) && empty($row['commentid']) && $_GPC['status'] == '3')) { goto lPhnO; } goto xc89V; f9BVL: $acc = notice_init(); goto puD5b; wg1cF: $kfinfo = !empty($_GPC['kfinfo']) ? '
客服回复：' . $_GPC['kfinfo'] : ''; goto Kf5FY; p47tc: foreach ($wxcard as &$val) { $val['coupon'] = pdo_get('coupon', array("uniacid" => $_W['uniacid'], "card_id" => $val['cardid']), array("id", "title")); yGly8: } goto ctE4j; xjCi0: $thumb1 = unserialize($row['rethumb']); goto E7jG5; N7xxs: $_W['page']['title'] = $activity['title'] . ' 表单详情'; goto iMM0j; rETyQ: if (!($row['icredit'] != '1' && $par['icredit'] == '1' && $activity['credit'] != '0.00' && $_GPC['status'] == '3')) { goto m3Mhn; } goto pHp7G; XayEm: i08TZ: goto b0yUF; WoK5A: svgow: goto zBNUp; Bkg4T: $testfile = $_FILES['upfile']; goto gAc0r; Qsam2: $info = '【您好，受理结果通知】

'; goto vStKB; cUYk8: $row['user'] = mc_fansinfo($row['openid'], $acid, $weid); goto s3DTN; ctE4j: twNFp: goto XayEm; dcPsD: $status = $this->get_status($row['reid'], $row['status']); goto pzIPi; o_8QI: $record['yuyuetime'] = strtotime($_GPC['yuyuetime']); goto y5Vfz; YVGGN: VQ2pC: goto owYuT; xIipI: llV8T: goto cUYk8; btlfk: $data = array("first" => array("value" => $activity['mfirst'] . '
', "color" => "#743A3A"), "keyword1" => array("value" => $row['member']), "keyword2" => array("value" => $row['mobile']), "keyword3" => array("value" => $_GPC['yuyuetime']), "keyword4" => array("value" => $huifu), "remark" => array("value" => '
' . $activity['mfoot'], "color" => "#008000")); goto iDM9Q; dFtfD: if (!(pdo_tableexists('dayu_sendkami') && !empty($par['sendkami']) && $row['kid'])) { goto WgRH0; } goto QBV38; tm06O: $record['status'] = intval($_GPC['status']); goto o_8QI; NxOM2: YgnZI: goto rETyQ; k4LrH: load()->func('communication'); goto aShiO; YMD82: foreach ($_GPC['rethumb'] as $thumb) { $th[] = tomedia($thumb); AqYCz: } goto IQ_1w; WR2gJ: $info .= "<a href='{$url}'>点击查看详情</a>"; goto lzdJB; cTb05: $log = $activity['title'] . '-' . $activity['credit'] . '积分'; goto SQOoj; pHp7G: $settings = uni_setting($_W['uniacid'], array("creditnames", "creditbehaviors")); goto REklD; Kf5FY: $state = $this->get_status($row['reid'], $_GPC['status']); goto AtzFV; jQkX_: $row['file'] = iunserializer($row['file']); goto OxZGw; Y0fAA: $role = $this->get_isrole($row['reid'], $_W['user']['uid']); goto rB_zc; w1M7S: global $_W, $_GPC; goto TEYSz; wqaAT: $acc->sendTplNotice($row['openid'], $activity['m_templateid'], $data, $outurl, '#FF0000'); goto R6ixr; nN8Oh: $custom = array("msgtype" => "text", "text" => array("content" => urlencode($info)), "touser" => $row['openid']); goto tL8M6; hQvXz: $outurl = !empty($par['noticeurl']) ? $par['noticeurl'] : $_W['siteroot'] . 'app/' . $this->createMobileUrl('mydayu_form', array("op" => "detail", "rerid" => $rerid, "id" => $row['reid'])); goto Bkg4T; rB_zc: if (!($setting['role'] == 1 && $_W['role'] == 'operator' && !$role)) { goto yEhtX; } goto KxzoL; t48vh: $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `reid`=:reid'; goto cXpfT; WGIlm: if (!($_GPC['status'] == '3' && $par['icredit'] == '1')) { goto llV8T; } goto wHdNe; Lu78a: S_rQO: goto ysQ7m; K8RDX: message('发送失败，原因为' . $status['message']); goto WoK5A; OLk2P: $activity = pdo_fetch($sql, $params); goto aphJp; e4450: $CustomNotice = $acc->sendCustomNotice($custom); goto vCdfm; OSnk_: dQ9yl: goto MD5VY; lzdJB: $custom = array("msgtype" => "text", "text" => array("content" => urlencode($info))); goto UXLeF; tL8M6: $acc = WeAccount::create($_W['acid']); goto e4450; SFZIo: foreach ($thumb1 as $p) { $rethumb[] = is_array($p) ? $p['attachment'] : $p; teazr: } goto Yf6AI; tAPFr: yEhtX: goto zS5cF; XGmlM: return error(-1, $acc['message']); goto YOT2j; r_KS6: $linkage['l2'] = $this->get_linkage($linkage['l2'], ''); goto Gfsxm; cXpfT: $params = array(); goto App3_; dOe6Y: oHvO2: goto tYI9A; pxnzR: $wxcard = pdo_fetchall('SELECT * FROM ' . tablename('dayu_wxcard') . ' WHERE weid = :weid AND cid=:cid AND status=1', array(":weid" => $_W['uniacid'], ":cid" => $par['wxcard'])); goto hkVUx; jmXP0: $row['thumb'] = iunserializer($row['thumb']); goto eDNHj; xonSN: $acc = WeAccount::create($_W['acid']); goto wCMS3; WIaLJ: mc_credit_update(mc_openid2uid($row['openid']), $behavior['activity'], $activity['credit'], array(0, $activity['title'])); goto BT2Es; ENhe8: $formdata = $this->order_foreach($row['reid'], $rerid); goto xI3UC; BrSvr: $url = $outurl; goto Qsam2; ysQ7m: $record = array(); goto tm06O; MD5VY: if (!(pdo_tableexists('dayu_kami') && !empty($par['kami']) && $row['kid'])) { goto B8VzJ; } goto MIq9v; xc89V: $status = $this->get_status($reid, $_GPC['status']); goto f9BVL; TFs0K: $settings = $this->module['config']; goto FSqZN; App3_: $params[':weid'] = $_W['uniacid']; goto EYmHV; s3DTN: $row['member'] = !empty($row['member']) ? $row['member'] : $row['user']['nickname']; goto oONh_; y8nZV: VnOTI: goto DtgJy; OrTAL: if (!($activity['custom_status'] == 1)) { goto tae7f; } goto BrSvr; rlLBy: $msg = ''; goto DtlR6; hPIgW: if (!is_array($linkage)) { goto BIrXt; } goto kcelK; gAc0r: if (!checksubmit('submit')) { goto VnOTI; } goto wg1cF; wmQB0: $info .= "{$par['commenttitle']}
"; goto Fltq6; DtgJy: $row['yuyuetime'] && ($row['yuyuetime'] = date('Y-m-d H:i:s', $row['yuyuetime'])); goto ZCEe2; b5XAL: foreach ($_GPC['file'] as $file) { $th[] = $file; t4ScS: } goto IsRTf; vStKB: $info .= "姓名：{$row['member']}
手机：{$row['mobile']}
受理结果：{$huifu}

"; goto WR2gJ; tYI9A: $la = iunserializer($activity['linkage']); goto bERmg; DtlR6: if (!(!empty($par['wxcard']) && !empty($_GPC['wxcard']) && pdo_tableexists('dayu_wxcard'))) { goto eCsjM; } goto PWVpC; mT9SB: $params = array(); goto Jf3HH; BFaaA: ihttp_post(murl('entry', array("do" => "Notice", "id" => $activity['smstype'], "m" => "dayu_sms"), true, true), $sms_data); goto NxOM2; wCMS3: $CustomNotice = $acc->sendCustomNotice($custom); goto ArtLW; nB5cb: RNPvx: goto wOgJs; lkVOu: m3Mhn: goto JkLAN; ptvV2: $sql = 'SELECT * FROM ' . tablename($this->tb_info) . ' WHERE `rerid`=:rerid'; goto mT9SB; iSF8m: if (!($activity['custom_status'] == '0' && !empty($activity['m_templateid']))) { goto j07Ft; } goto btlfk; ArtLW: if (!is_error($status)) { goto svgow; } goto K8RDX; pzIPi: $state = array(); goto ZeNC4; Yf6AI: RxEcM: goto dOe6Y; UZ2cG: if (!(!empty($par['comment']) && pdo_tableexists('dayu_comment') && !empty($row['commentid']))) { goto VQ2pC; } goto xOxG_; F67In: $info = $activity['title'] . ' 处理结果 ' . $status['name'] . '

'; goto wmQB0; p6THv: message('非法访问.'); goto dDhDV; tUXAg: $msg .= $wxcard_post['msg']; goto sirYS; KxzoL: message('您没有权限进行该操作.'); goto tAPFr; ZCEe2: load()->func('file'); goto PCqHr; PWVpC: $wxcard_post = ihttp_post(murl('entry', array("do" => "recorded", "couponid" => $_GPC['wxcard'], "m" => "dayu_wxcard"), true, true), array("addons" => $_W['current_module']['name'], "openid" => $row['openid'], "infoid" => $rerid)); goto tUXAg; E7jG5: $rethumb = array(); goto Rv817; NZZq1: $record['rethumb'] = iserializer($th); goto nB5cb; YOT2j: G82K_: goto Q1Jz_; G3lPp: $alldata = array(); goto ENhe8; Rv817: if (!is_array($thumb1)) { goto oHvO2; } goto SFZIo; wTgY5: if (empty($_GPC['file'])) { goto QTSEV; } goto b5XAL; rFItb: QTSEV: goto WGIlm; eDNHj: p1CXu: goto G4kOz; lqUQ5: $record['file'] = iserializer($th); goto rFItb; b0yUF: bZLF7: goto VkfAf; zBNUp: tae7f: goto iSF8m; xI3UC: foreach ($formdata as $index => $v) { goto gDvjO; Sjz7H: HsjAV: goto Y28lH; uVXq0: $alldata[] = $v['title'] . ':' . $v['data'] . ','; goto Sjz7H; garIX: $formdata[$index]['data'] .= $fdata['data']; goto Bmv4l; Bmv4l: ynJ1N: goto uVXq0; gDvjO: if (!($value['type'] == 'reside')) { goto ynJ1N; } goto garIX; Y28lH: } goto Lu78a; PCqHr: load()->func('tpl'); goto N7xxs; zS5cF: if (empty($row['thumb'])) { goto p1CXu; } goto jmXP0; hkVUx: if (!is_array($wxcard)) { goto i08TZ; } goto p47tc; Q1Jz_: $url = $outurl; goto F67In; IQ_1w: nVM8J: goto NZZq1; VeiLX: if (!empty($row)) { goto FUZaK; } goto a42BC; G4kOz: $row['voices'] = $row['voice']; goto SSKOJ; owYuT: if (!(!empty($par['wxcard']) && pdo_tableexists('dayu_wxcard_activity'))) { goto bZLF7; } goto pxnzR; R6ixr: j07Ft: goto wn3a_; SQOoj: mc_notice_credit1($row['openid'], mc_openid2uid($row['openid']), $activity['credit'], $log); goto lkVOu; T7D1W: pdo_update('dayu_form_info', $record, array("rerid" => $rerid)); goto K7MGc; xOxG_: $comment = pdo_get('dayu_comment', array("weid" => $_W['uniacid'], "id" => $row['commentid']), array()); goto YVGGN; OxZGw: $par = iunserializer($activity['par']); goto dcPsD; d5HrN: B8VzJ: goto dFtfD; C5i9i: foreach ($arr2 as $index => $v) { $state[$v][] = $this->get_status($row['reid'], $v); KRh1D: } goto OSnk_; UXLeF: $custom['touser'] = trim($row['openid']); goto xonSN; VkfAf: $custom = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_custom) . ' WHERE weid = :weid ORDER BY displayorder DESC', array(":weid" => $_W['uniacid'])); goto G3lPp; ZzFaw: WgRH0: goto UZ2cG; REklD: $behavior = $settings['creditbehaviors']; goto WIaLJ; Jf3HH: $params[':rerid'] = $rerid; goto Semvq; iMM0j: include $this->template('detail'); goto YnuxH; puD5b: if (!is_error($acc)) { goto G82K_; } goto XGmlM; ZeNC4: $arr2 = array("0", "1", "2", "3", "8", "7"); goto C5i9i; TEYSz: require MODULE_ROOT . '/fans.web.php'; goto TFs0K; wOgJs: $record['kfinfo'] = $_GPC['kfinfo']; goto wTgY5; EYmHV: $params[':reid'] = $row['reid']; goto OLk2P; XapQt: FUZaK: goto t48vh; wn3a_: if (!($par['sms'] != '0' && $par['paixu'] != '2' && !empty($activity['smstype']))) { goto YgnZI; } goto k4LrH; oONh_: $ytime = date('Y-m-d H:i:s', TIMESTAMP); goto hQvXz; FSqZN: $rerid = intval($_GPC['id']); goto ptvV2; sirYS: eCsjM: goto OrTAL; SSKOJ: $row['revoices'] = $row['revoice']; goto xjCi0; YnuxH: } public function doWebupfile() { goto bHSEP; qUPl1: uLnSf: goto siog3; JYadI: $max_file_size = 2000000; goto NCwE4; bHSEP: global $_W, $_GPC; goto JYadI; W7qc9: if (!($max_file_size < $file['size'])) { goto YuMva; } goto s3F2X; NM3HT: $destination = $destination_folder . time() . '.' . $ftype; goto N1kCv; Ntm45: $pinfo = pathinfo($file['name']); goto vVlL8; ES2wr: exit; goto qUPl1; U4Un9: if (is_uploaded_file($_FILES['upfile'][tmp_name])) { goto Im4Ft; } goto fpmVF; o5oiL: echo 'name'; goto ES2wr; NCwE4: $destination_folder = ATTACHMENT_ROOT . 'dayu_form/' . $_W['uniacid'] . '/file/'; goto tiQ59; siog3: if (move_uploaded_file($filename, $destination)) { goto Qdd48; } goto s74Es; vVlL8: $ftype = $pinfo['extension']; goto NM3HT; rnYq8: Im4Ft: goto Nt0Wv; iV3jz: $fname = $pinfo[basename]; goto K3dTP; aHgwK: YuMva: goto b8V3D; ICA9V: exit; goto hVply; UuNUn: $image_size = getimagesize($filename); goto Ntm45; s3F2X: echo 'size'; goto U4B4y; N1kCv: if (!(file_exists($destination) && $overwrite != true)) { goto uLnSf; } goto o5oiL; eixon: mkdir($destination_folder); goto moUUs; U4B4y: exit; goto aHgwK; b8V3D: if (file_exists($destination_folder)) { goto G5gVt; } goto eixon; K3dTP: echo $dest . $fname; goto LWkOE; Nt0Wv: $file = $_FILES['upfile']; goto W7qc9; V98Ml: $pinfo = pathinfo($destination); goto iV3jz; SvKrQ: $filename = $file['tmp_name']; goto UuNUn; ubyeA: exit; goto rnYq8; s74Es: echo 'move'; goto ICA9V; moUUs: G5gVt: goto SvKrQ; hVply: Qdd48: goto V98Ml; fpmVF: echo 'nothing'; goto ubyeA; tiQ59: $dest = '/attachment/dayu_form/' . $_W['uniacid'] . '/file/'; goto U4Un9; LWkOE: } public function doWebManage() { goto IyBeq; XFKyh: S_vfr: goto oCddo; J1Xlo: $where = 'reid = :reid'; goto kBm8S; LtFBH: if (empty($list)) { goto AbH0O; } goto vc65H; j2nMz: $where2 .= " and a.status='{$status}'"; goto XFKyh; iLc57: w0RRz: goto MbRDr; AsZp0: header("Content-Disposition:attachment; filename={$activity['title']}=={$stime}-{$etime}.csv"); goto ZMhHt; a3lo1: $htmlheader = array("openid" => "粉丝编号", "member" => $activity['member'], "mobile" => $activity['phone']); goto NaYmH; XELPd: if (empty($_GPC['kf'])) { goto R_IRW; } goto lGmgI; IXt3A: if (!empty($activity)) { goto w0RRz; } goto FWP1g; GK6Y1: $fields = pdo_fetchall($sql, $params, 'refid'); goto COpwH; D52jM: $params = array(); goto dG13S; qXfcn: $par = iunserializer($activity['par']); goto nIqJW; srdvk: $childlist = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_data) . ' WHERE rerid IN (\'' . implode('\',\'', is_array($rerid) ? $rerid : array($rerid)) . '\') AND `reid`=:reid', array(":reid" => $reid)); goto F7Ieb; z5QFm: agppa: goto a3lo1; ERvgJ: L3Wq7: goto LtFBH; ZRrxJ: $where2 .= ' and (a.member like :member or a.mobile like :mobile)'; goto k_szj; Im0rX: $params2[':starttime'] = $starttime; goto ojkRI; wE3a2: zlZ3B: goto s0PPN; SWL2s: mkg6S: goto dWi0z; FcZ22: if (empty($_GPC['time'])) { goto lHm7y; } goto FJ98u; rAGMI: foreach ($listall as $index => $v) { goto aBoLH; Tkqe8: foreach (explode(',', $v['data']) as $val) { $v[] = $val; aUGjn: } goto zFWhh; t9gyE: $v['link']['l1'] = $this->get_linkage($linkage['l1'], ''); goto Jj1YV; zBtrT: unset($v['linkage']); goto q7yI3; r_x9K: $v['l2'] = $v['link']['l2']['title']; goto Tkqe8; Jj1YV: $v['link']['l2'] = $this->get_linkage($linkage['l2'], ''); goto a46Sm; aBoLH: $linkage = iunserializer($v['linkage']); goto LXKDD; a46Sm: nnluZ: goto Q7t6L; zFWhh: Bzzw_: goto XAGjb; q7yI3: unset($v['data']); goto X54j0; Q7t6L: $v['l1'] = $v['link']['l1']['title']; goto r_x9K; SsOpQ: MCECU: goto VCKdR; XAGjb: unset($v['link']); goto zBtrT; X54j0: $childrens[] = $v; goto SsOpQ; LXKDD: if (!is_array($linkage)) { goto nnluZ; } goto t9gyE; VCKdR: } goto z5QFm; pq7rY: $staff = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_staff) . ' WHERE reid = :reid ORDER BY `id` DESC', array(":reid" => $reid)); goto KCPPq; yDsb6: $where .= ' AND createtime >= :starttime AND createtime <= :endtime '; goto mCmMH; QMjcc: $where .= " and status='{$status}'"; goto WTzNl; oadEt: foreach ($list as &$r) { goto WkAix; hTyF_: $r['kf'] = mc_fansinfo($r['kf'], $acid, $_W['uniacid']); goto zvaAz; IdA8I: $r['groupid'] = mc_fetch($r['user']['uid'], array("groupid")); goto vpI9N; zvaAz: $r['voices'] = strstr($r['voice'], 'http://') ? $r['voice'] : $setting['qiniu']['host'] . '/' . $r['voice']; goto s25jw; WkAix: $r['user'] = mc_fansinfo($r['openid']); goto hTyF_; CBGaH: $r['consult'] = pdo_fetch('SELECT id FROM ' . tablename('dayu_consult') . ' WHERE infoid = :infoid ORDER BY createtime DESC', array(":infoid" => $r['rerid'])); goto tQdTP; tQdTP: if (empty($r['consult']['id'])) { goto DMQfR; } goto eJN9U; kQztp: DMQfR: goto dIzH8; s25jw: $r['revoices'] = strstr($r['revoice'], 'http://') ? $r['revoice'] : $setting['qiniu']['host'] . '/' . $r['revoice']; goto IdA8I; eJN9U: $r['consultid'] = '1'; goto kQztp; vpI9N: $r['state'] = $this->get_status($r['reid'], $r['status']); goto sUvoM; SEaIV: if (!pdo_tableexists('dayu_consult')) { goto zhf1k; } goto CBGaH; idg0p: oaiXj: goto Hr3vI; sUvoM: $r['comment'] = !empty($par['comment']) && pdo_tableexists('dayu_comment') && !empty($r['commentid']) ? $this->get_comment($r['commentid']) : ''; goto SEaIV; dIzH8: zhf1k: goto idg0p; Hr3vI: } goto Nw4Lh; WTzNl: feJD_: goto jy0kM; lGmgI: $where .= " and kf LIKE '%{$_GPC['kf']}%'"; goto O3Fan; g34FK: TaNH7: goto XELPd; SdG97: $total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_info) . " WHERE {$where}", $params); goto Lvo4C; OWatS: $params[':member'] = "%{$_GPC['keywords']}%"; goto Aqsk2; dj81w: if (!checksubmit('export', true)) { goto L3Wq7; } goto GSvNu; kBm8S: $params = array(":reid" => $reid); goto vZ7u7; sCmCw: $activity = pdo_get($this->tb_form, array("weid" => $_W['uniacid'], "reid" => $reid)); goto IXt3A; ZMhHt: echo $html; goto V6mJO; BWK4B: $endtime = empty($_GPC['time']['end']) ? TIMESTAMP : strtotime($_GPC['time']['end']); goto J1Xlo; COpwH: if (!empty($fields)) { goto zlZ3B; } goto XPx0W; O0ODa: $zuhe = array_merge($htmlheader, $ds, $htmlfoot); goto PREum; GSvNu: $ds = array(); goto OgUDd; Dco23: $etime = date('Ymd', $endtime); goto hoRCl; GhV0N: yOag9: goto NkgTf; ztI1t: $rerid = array_keys($list); goto i09Pe; FJ98u: $where2 .= ' AND a.createtime >= :starttime AND a.createtime <= :endtime '; goto Im0rX; vsvz6: $reid = intval($_GPC['id']); goto sCmCw; vZ7u7: if (empty($_GPC['time'])) { goto YtKDC; } goto yDsb6; X23O_: require MODULE_ROOT . '/fans.web.php'; goto vsvz6; Nw4Lh: OIBtA: goto ztI1t; IyBeq: global $_W, $_GPC; goto X23O_; ZreNt: $params2 = array(":reid" => $reid); goto FcZ22; HvvoO: if (!($status != '')) { goto feJD_; } goto QMjcc; mCmMH: $params[':starttime'] = $starttime; goto L33y6; XPx0W: message('非法访问.'); goto wE3a2; O3Fan: R_IRW: goto HvvoO; yl6Qd: $params2[':mobile'] = "%{$_GPC['keywords']}%"; goto Nwr5I; QG_9C: $where2 = 'a.reid = :reid'; goto ZreNt; HeE25: load()->model('mc'); goto oadEt; pZDH3: AbH0O: goto pq7rY; DlEzY: FaMPn: goto pZDH3; k_szj: $params2[':member'] = "%{$_GPC['keywords']}%"; goto yl6Qd; NaYmH: $htmlfoot = array("status" => "状态", "kfinfo" => "回复", "createtime" => "提交时间"); goto O0ODa; CRd7_: LFWyb: goto FC615; nonVp: YtKDC: goto abofV; ffM8a: if (empty($_GPC['kf'])) { goto LFWyb; } goto cOJk8; KCPPq: include $this->template('manage'); goto LGREg; jy0kM: $sql = 'SELECT * FROM ' . tablename($this->tb_info) . " WHERE {$where} ORDER BY createtime DESC LIMIT " . ($pindex - 1) * $psize . ',' . $psize; goto LF_J8; Sem8m: message('您没有权限进行该操作.'); goto mG8UH; Lvo4C: $allTotal = pdo_fetchall('SELECT `reid` FROM ' . tablename($this->tb_info) . ' WHERE reid = :reid GROUP BY openid', array(":reid" => $reid)); goto HeE25; MbRDr: $_W['page']['title'] = $activity['title'] . ' 记录管理'; goto m3yUu; NkgTf: $childrens = array(); goto QG_9C; ojkRI: $params2[':endtime'] = $endtime; goto WwSTy; R8Cg3: $stime = date('Ymd', $starttime); goto Dco23; PREum: $html = $this->from_export_parse($zuhe, $childrens, $reid); goto R8Cg3; WwSTy: lHm7y: goto crO24; AyzED: $where .= ' and (member like :member or mobile like :mobile)'; goto OWatS; OgUDd: foreach ($fields as $f) { $ds[$f['refid']] = $f['title']; m2ToQ: } goto GhV0N; x27BR: if (!($setting['role'] == 1 && $_W['role'] == 'operator' && !$role)) { goto LYgCV; } goto Sem8m; FC615: if (!($status != '')) { goto S_vfr; } goto j2nMz; mG8UH: LYgCV: goto qXfcn; crO24: if (empty($_GPC['keywords'])) { goto Y7W2i; } goto ZRrxJ; LF_J8: $list = pdo_fetchall($sql, $params, 'rerid'); goto SdG97; V6mJO: exit; goto ERvgJ; dG13S: $params[':reid'] = $reid; goto GK6Y1; qoFk1: $pindex = max(1, intval($_GPC['page'])); goto OEaEf; OEaEf: $psize = 15; goto Hrh2s; cOJk8: $where2 .= " and a.kf LIKE '%{$_GPC['kf']}%'"; goto CRd7_; Nwr5I: Y7W2i: goto ffM8a; Aqsk2: $params[':mobile'] = "%{$_GPC['keywords']}%"; goto g34FK; nIqJW: $la = iunserializer($activity['linkage']); goto kKI0G; dWi0z: $pager = pagination($total, $pindex, $psize); goto dj81w; FWP1g: message('非法访问.'); goto iLc57; vc65H: foreach ($list as $key => &$value) { goto awELA; MjT2K: unset($v); goto awOLT; fmYSZ: igF6y: goto pD_4u; awOLT: o3zge: goto fmYSZ; awELA: if (!is_array($value['fields'])) { goto o3zge; } goto IJ9rQ; IJ9rQ: foreach ($value['fields'] as &$v) { goto vVyWK; F7Eab: MK_7L: goto l0wNT; l0wNT: EzZox: goto pnj1Y; VmqwJ: if (!(strstr($field, 'images') || strstr($field, 'dayu_form'))) { goto MK_7L; } goto HdtsG; HdtsG: $v = $img . tomedia($v) . '" style="width:50px;height:50px;"/>'; goto F7Eab; vVyWK: $img = '<img src="'; goto VmqwJ; pnj1Y: } goto Tj4zA; Tj4zA: IWr7P: goto MjT2K; pD_4u: } goto DlEzY; L33y6: $params[':endtime'] = $endtime; goto nonVp; i09Pe: $children = array(); goto srdvk; hoRCl: header('Content-type:text/csv'); goto AsZp0; s0PPN: $status = $_GPC['status']; goto qoFk1; abofV: if (empty($_GPC['keywords'])) { goto TaNH7; } goto AyzED; Hrh2s: $starttime = empty($_GPC['time']['start']) ? strtotime('-1 month') : strtotime($_GPC['time']['start']); goto BWK4B; oCddo: $listall = pdo_fetchall('SELECT a.reid,a.rerid,a.member,a.mobile,a.openid,a.linkage,a.status,a.kfinfo,a.createtime,(SELECT GROUP_CONCAT(b.data ORDER BY b.displayorder desc) FROM ' . tablename($this->tb_data) . ' AS b WHERE b.rerid = a.rerid) data FROM ' . tablename($this->tb_info) . " AS a WHERE {$where2} ORDER BY a.rerid DESC", $params2); goto rAGMI; kKI0G: $sql = 'SELECT * FROM ' . tablename($this->tb_field) . ' WHERE `reid`=:reid ORDER BY displayorder desc'; goto D52jM; m3yUu: $role = $this->get_isrole($reid, $_W['user']['uid']); goto x27BR; F7Ieb: foreach ($childlist as $reply => $d) { goto RSV_F; r09hy: pBc2a: goto lNZs9; b6dxL: hHj0I: goto r09hy; Lmyox: unset($children[$reply]); goto b6dxL; cyLGQ: $children[$d['rerid']][] = $d; goto Lmyox; RSV_F: if (empty($d['rerid'])) { goto hHj0I; } goto cyLGQ; lNZs9: } goto SWL2s; LGREg: } public function doWebbatchrecord() { goto QbevX; sWI09: if (!empty($reply)) { goto nGM9P; } goto DMdQr; sDAXD: $reid = intval($_GPC['reid']); goto iUYs9; dWffW: keQuH: goto sDAXD; huvI3: ucMsF: goto tYe0Q; TBdMx: if (!($setting['role'] == 1 && $_W['role'] == 'operator' && !$role)) { goto keQuH; } goto YbXXg; zbGlp: require MODULE_ROOT . '/fans.web.php'; goto McVdb; DMdQr: message('抱歉，表单主题不存在或是已经被删除！'); goto wW3zt; tYe0Q: message('记录删除成功！', '', 0); goto cvN2W; iUYs9: $reply = pdo_fetch('select reid from ' . tablename($this->tb_form) . ' where reid = :reid', array(":reid" => $reid)); goto sWI09; YbXXg: message('您没有权限进行该操作.'); goto dWffW; QbevX: global $_GPC, $_W; goto zbGlp; wW3zt: nGM9P: goto cNHRK; cNHRK: foreach ($_GPC['idArr'] as $k => $rerid) { goto pGlcz; IoiuF: A2yW3: goto CAzXC; eBvwq: pdo_delete('dayu_form_info', array("rerid" => $rerid, "reid" => $reid)); goto OzQ5u; OzQ5u: pdo_delete('dayu_form_data', array("rerid" => $rerid)); goto IoiuF; pGlcz: $rerid = intval($rerid); goto eBvwq; CAzXC: } goto huvI3; McVdb: $role = $this->get_isrole($reid, $_W['user']['uid']); goto TBdMx; cvN2W: } public function doWebupdategroup() { goto N3L6i; vBpel: exit(json_encode(array("status" => "error", "mess" => $data['message']))); goto sSTqa; dcECN: if (is_error($data)) { goto S3v2r; } goto TStYN; UOKZ7: exit(json_encode(array("status" => "success"))); goto UJmFs; Kgkri: EdPl7: goto bf1pM; M0pri: oZtfR: goto He6LG; bqwDa: goto FxGg8; goto M0pri; KFl3t: if (!$_W['isajax']) { goto EdPl7; } goto gNlkr; gNlkr: $groupid = intval($_GPC['groupid']); goto QfYdQ; UJmFs: goto USAZH; goto DIDse; N3L6i: global $_GPC, $_W; goto KFl3t; TStYN: pdo_update('mc_mapping_fans', array("groupid" => $groupid), array("uniacid" => $_W['uniacid'], "acid" => $_W['acid'], "openid" => $openid)); goto UOKZ7; btkPC: $data = $acc->updateFansGroupid($openid, $groupid); goto dcECN; QfYdQ: $openid = trim($_GPC['openid']); goto o5Ar8; sSTqa: USAZH: goto g3P6H; XugYH: exit(json_encode(array("status" => "error", "mess" => "粉丝openid错误"))); goto bqwDa; o5Ar8: if (!empty($openid)) { goto oZtfR; } goto XugYH; He6LG: $acc = WeAccount::create($_W['acid']); goto btkPC; DIDse: S3v2r: goto vBpel; g3P6H: FxGg8: goto Kgkri; bf1pM: } public function doWebDisplay() { goto l2Ufm; w774W: message('复制表单出错', '', 'error'); goto v0oTP; C0OfU: $op = trim($_GPC['op']) ? trim($_GPC['op']) : ''; goto xgjet; vtwNM: $params = array(); goto D26yN; hb_BM: if (empty($_GPC['keyword'])) { goto tB5RM; } goto C68ZJ; MCfXm: unset($form['reid']); goto J9b0i; hKy3x: if (!empty($form)) { goto LIU1Q; } goto fUJQO; T35au: $roleid = array_keys($role); goto JYH6d; O6ubI: $pager = pagination($total, $pindex, $psize); goto l1qCX; yURvO: if (!$cateid) { goto nhbgZ; } goto IzNR5; idvAY: B1VHu: goto w774W; TCeoz: $where .= ' AND reid IN (\'' . implode('\',\'', is_array($roleid) ? $roleid : array($roleid)) . '\')'; goto UPWQy; dqHj5: $id = intval($_GPC['id']); goto r9bYW; fSSFD: if (!($op == 'copy')) { goto mNcZJ; } goto dqHj5; D26yN: $params[':status'] = $switch; goto S2WII; Oogd4: message('复制表单成功', $this->createWebUrl('display'), 'success'); goto kTMCk; l2Ufm: global $_W, $_GPC; goto YoGgN; rG4_G: pdo_query($sql, $params); goto FTAAo; mhXP1: $ds = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_form) . ' WHERE ' . $where . ' ORDER BY status DESC,reid DESC LIMIT ' . ($pindex - 1) * $psize . ',' . $psize, $params); goto yzzKY; lwBee: mNcZJ: goto AzojN; kTMCk: goto CXHzl; goto idvAY; FTAAo: exit; goto T7Hml; T7Hml: yqtcu: goto fSSFD; r9bYW: $form = pdo_fetch('SELECT * FROM ' . tablename($this->tb_form) . ' WHERE weid = :weid AND reid = :reid', array(":weid" => $_W['uniacid'], ":reid" => $id)); goto hKy3x; gerK6: foreach ($fields as &$val) { goto o_ORh; o_ORh: unset($val['refid']); goto T39qs; or0le: pdo_insert($this->tb_field, $val); goto ZMOHm; ZMOHm: sYGAJ: goto DTz6I; T39qs: $val['reid'] = $form_id; goto or0le; DTz6I: } goto Fez0A; fUJQO: message('表单不存在或已删除', referer(), 'error'); goto QJuZq; v0oTP: CXHzl: goto lwBee; zFOT5: $reid = intval($_GPC['reid']); goto Ib0BX; Fez0A: bFSc0: goto u3_9i; u3_9i: SLMPS: goto Oogd4; XwLaB: $_W['page']['title'] = '表单列表'; goto jAWgz; c10DC: $form['createtime'] = TIMESTAMP; goto MCfXm; nkbXY: PfiTr: goto XwLaB; UPWQy: ivcPe: goto cXotp; YoGgN: require MODULE_ROOT . '/fans.web.php'; goto C0OfU; SINAN: $role = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_role) . ' WHERE weid = :weid and roleid = :roleid  ORDER BY id DESC', array(":roleid" => $_W['user']['uid'], ":weid" => $weid), 'reid'); goto T35au; eMnJI: tB5RM: goto icd94; JvOFJ: nhbgZ: goto sT_Oo; TDPNH: $where = 'weid = :weid'; goto M_9Qu; oXVFO: $form_id = pdo_insertid(); goto JcRQr; xgjet: if (!$_W['ispost']) { goto yqtcu; } goto zFOT5; S2WII: $params[':reid'] = $reid; goto rG4_G; l1qCX: foreach ($ds as &$item) { goto DF_Yk; zYacT: $var1 = '&' . $item['var1'] . '=变量1'; goto JQ82g; CkNeD: $item['cate'] = $this->get_category($item['cid']); goto TvnRO; gdT6Z: if (empty($item['var3'])) { goto AAUCS; } goto Zpy1v; DBTxz: oFoLL: goto aqJ2d; TvnRO: $item['la'] = $this->get_linkage($item['reid'], 1); goto hAdzA; aTS7J: $item['record'] = $item['isget'] == 1 ? murl('entry', array("do" => "record", "id" => $item['reid'], "m" => "dayu_form"), true, true) . $var1 . $var2 . $var3 : murl('entry', array("do" => "record", "id" => $item['reid'], "m" => "dayu_form"), true, true); goto VN3cp; Leo60: $item['isstart'] = $item['starttime'] > 0; goto kt1Ni; VN3cp: $item['mylink'] = murl('entry', array("do" => "Mydayu_form", "id" => $item['reid'], "weid" => $item[weid], "m" => "dayu_form"), true, true); goto DBTxz; Vncl1: $item['par'] = iunserializer($item['par']); goto Leo60; MeNi_: AAUCS: goto Vncl1; b5x4G: $item['isvar'] = $item['isget'] == 1 ? '<span class="btn btn-success btn-sm">启用</span>' : '<span class="btn btn-default btn-sm">关闭</span>'; goto fTBDG; Zpy1v: $var3 = '&' . $item['var3'] . '=变量3'; goto MeNi_; fTBDG: $item['link'] = $item['isget'] == 1 ? murl('entry', array("do" => "dayu_form", "id" => $item['reid'], "m" => "dayu_form"), true, true) . $var1 . $var2 . $var3 : murl('entry', array("do" => "dayu_form", "id" => $item['reid'], "m" => "dayu_form"), true, true); goto aTS7J; DF_Yk: if (empty($item['var1'])) { goto sf0zC; } goto zYacT; JQ82g: sf0zC: goto ZtZ21; kt1Ni: $item['switch'] = $item['status']; goto CkNeD; ROFRq: V7zfq: goto gdT6Z; ZtZ21: if (empty($item['var2'])) { goto V7zfq; } goto uSwpy; hAdzA: $item['role'] = $this->get_isrole($item['reid'], $_W['user']['uid']); goto ndfil; uSwpy: $var2 = '&' . $item['var2'] . '=变量2'; goto ROFRq; ndfil: $item['count'] = $this->get_count($item['reid']); goto b5x4G; aqJ2d: } goto nkbXY; yzzKY: $total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_form) . ' WHERE ' . $where, $params); goto O6ubI; XKcJT: $psize = 10; goto TDPNH; Fvd9I: bnBoC: goto hb_BM; eXnoL: $fields = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_field) . ' WHERE reid = :reid', array(":reid" => $id)); goto QAWwQ; Ib0BX: $switch = intval($_GPC['switch']); goto eJ6PF; Lw57o: $form['title'] = $form['title'] . '_' . random(6); goto c10DC; jAWgz: include $this->template('display'); goto IiQXC; sT_Oo: if (!($status != '')) { goto bnBoC; } goto dP5Xi; QAWwQ: if (empty($fields)) { goto SLMPS; } goto gerK6; KVHuH: $pindex = max(1, intval($_GPC['page'])); goto XKcJT; M_9Qu: $status = $_GPC['status']; goto yURvO; icd94: if (!($setting['role'] == 1 && $_W['role'] == 'operator')) { goto ivcPe; } goto TCeoz; cXotp: $params = array(":weid" => $weid); goto mhXP1; eJ6PF: $sql = 'UPDATE ' . tablename($this->tb_form) . ' SET `status`=:status WHERE `reid`=:reid'; goto vtwNM; JYH6d: $cateid = intval($_GPC['formid']); goto KVHuH; QJuZq: LIU1Q: goto Lw57o; C68ZJ: $where .= " AND title LIKE '%{$_GPC['keyword']}%'"; goto eMnJI; dP5Xi: $where .= ' and status=' . intval($status); goto Fvd9I; J9b0i: pdo_insert($this->tb_form, $form); goto oXVFO; JcRQr: if (!$form_id) { goto B1VHu; } goto eXnoL; AzojN: $category = pdo_fetchall('SELECT id,title FROM ' . tablename($this->tb_category) . ' WHERE weid = :weid ORDER BY `id` DESC', array(":weid" => $_W['uniacid'])); goto SINAN; IzNR5: $where .= ' and cid=' . intval($cateid); goto JvOFJ; IiQXC: } public function doWebDelete() { goto HsT0d; u_5bU: $params[':reid'] = $reid; goto Un6GC; huIgu: $sql = 'DELETE FROM ' . tablename($this->tb_field) . ' WHERE `reid`=:reid'; goto AI2w1; RxtI_: TCDHC: goto inBEO; Un6GC: $sql = 'DELETE FROM ' . tablename($this->tb_form) . ' WHERE `reid`=:reid'; goto mP6Zp; mP6Zp: pdo_query($sql, $params); goto NTd4A; rjfk1: message('非法访问.'); goto fF3F3; JUVc_: $params = array(); goto u_5bU; xRrD7: $sql = 'DELETE FROM ' . tablename($this->tb_staff) . ' WHERE `reid`=:reid'; goto tT2lq; TN8ny: message('操作成功.', referer()); goto RpWBe; NTd4A: $sql = 'DELETE FROM ' . tablename($this->tb_info) . ' WHERE `reid`=:reid'; goto Oh80t; JeVwi: message('您没有权限进行该操作.'); goto RxtI_; TkPI2: if (!($_W['role'] == 'operator' && !$role)) { goto TCDHC; } goto JeVwi; AI2w1: pdo_query($sql, $params); goto EuK6X; FRUXa: $role = $this->get_isrole($reid, $_W['user']['uid']); goto TkPI2; inBEO: if (!($reid > 0)) { goto WsCiW; } goto JUVc_; RpWBe: WsCiW: goto rjfk1; Z15_j: require MODULE_ROOT . '/fans.web.php'; goto vJkIQ; Oh80t: pdo_query($sql, $params); goto huIgu; utp4m: pdo_query($sql, $params); goto xRrD7; HsT0d: global $_W, $_GPC; goto Z15_j; vJkIQ: $reid = intval($_GPC['id']); goto FRUXa; tT2lq: pdo_query($sql, $params); goto TN8ny; EuK6X: $sql = 'DELETE FROM ' . tablename($this->tb_data) . ' WHERE `reid`=:reid'; goto utp4m; fF3F3: } public function doWebdayu_formDelete() { goto VnX22; RGaeD: if (empty($id)) { goto lrc6H; } goto Xuk2p; QvRKa: $id = intval($_GPC['id']); goto bJ266; hgcVT: w9IN3: goto RGaeD; f9l5I: require MODULE_ROOT . '/fans.web.php'; goto QvRKa; Xuk2p: pdo_delete('dayu_form_info', array("rerid" => $id)); goto mA6ds; O4HDN: message('操作成功.', referer()); goto qRtYP; VnX22: global $_W, $_GPC; goto f9l5I; TbR5a: lrc6H: goto O4HDN; bJ266: $role = $this->get_isrole($id, $_W['user']['uid']); goto YPTZ0; CEWti: message('您没有权限进行该操作.'); goto hgcVT; mA6ds: pdo_delete('dayu_form_data', array("rerid" => $id)); goto TbR5a; YPTZ0: if (!($setting['role'] == 1 && $_W['role'] == 'operator' && !$role)) { goto w9IN3; } goto CEWti; qRtYP: } public function doMobiledayu_formDelete() { goto GdkEe; tklmn: if (!empty($id) && $openid == $form['openid']) { goto EF4gG; } goto SiEH0; Quta1: goto IqPu_; goto KCyFE; KCyFE: EF4gG: goto TKWgl; YeKy1: $id = intval($_GPC['id']); goto VBA4Z; UBYQL: pdo_delete('dayu_form_data', array("rerid" => $id)); goto mDeKO; TKWgl: pdo_delete('dayu_form_info', array("rerid" => $id)); goto UBYQL; KW67P: IqPu_: goto TOt2z; l9D73: $form = pdo_fetch('SELECT rerid, openid FROM ' . tablename($this->tb_info) . " WHERE rerid = '{$id}'"); goto tklmn; WdPQK: $reid = intval($_GPC['reid']); goto l9D73; mDeKO: $this->showMessage('删除成功.', $this->createMobileUrl('mydayu_form', array("weid" => $_W['uniacid'], "id" => $reid)), '', '', ''); goto KW67P; GdkEe: global $_W, $_GPC; goto YeKy1; SiEH0: $this->showMessage('删除失败，原因：该记录不在您的名下.', $this->createMobileUrl('mydayu_form', array("weid" => $_W['uniacid'], "id" => $reid)), '', '', ''); goto Quta1; VBA4Z: $openid = intval($_GPC['openid']); goto WdPQK; TOt2z: } public function doWebPost() { goto HIaSp; K1TsT: $sql = 'SELECT * FROM ' . tablename($this->tb_field) . ' WHERE `reid`=:reid ORDER BY `displayorder` DESC,`refid`'; goto b6rQe; tcrLk: $sql = 'SELECT openid,nickname FROM ' . tablename('mc_mapping_fans') . ' WHERE acid =:acid AND openid = :openid'; goto Ny43C; zqVao: plnrk: goto RhaLA; wVKlE: foreach ($skins as &$s) { $s['weid'] = !empty($s['ids']) ? explode(',', $s['ids']) : ''; ihdM0: } goto TkUcq; IW3T4: if (!empty($openid)) { goto XSM6j; } goto qk6p5; c3qQa: $record['cid'] = intval($_GPC['cate']); goto No1Dh; V_o5o: $activity['starttime'] && ($activity['starttime'] = date('Y-m-d H:i:s', $activity['starttime'])); goto OemNW; aIxvC: goto RjfqL; goto tcJGd; AxssB: $record['information'] = trim($_GPC['information']); goto jVwfx; jVwfx: if (empty($_GPC['thumb'])) { goto zIyV6; } goto oQ0iK; nYs5i: $title = !empty($_GPC['id']) ? '编辑表单' : '新建表单'; goto zB386; dmecL: $record['mbgroup'] = $_GPC['mbgroup']; goto RaEeo; Rw_S3: aWjIC: goto oSruJ; oSruJ: itoast('保存成功.', 'refresh'); goto MrglE; xbjtk: XSoGv: goto uH0hc; QQVQL: $record['agreement'] = trim($_GPC['agreement']); goto rHsjU; qk6p5: $sql = 'SELECT openid,nickname FROM ' . tablename('mc_mapping_fans') . ' WHERE acid =:acid AND nickname = :nickname'; goto aWYVc; uII6P: $record['kfirst'] = trim($_GPC['kfirst']); goto wmhJ2; Lb2wh: $types = array(); goto pC57j; PFq2L: $record['title'] = trim($_GPC['activity']); goto TVGvB; lYoc0: $params[':reid'] = $reid; goto nHThi; x5Ml0: $var1 = !empty($par['var1']) ? '&' . $par['var1'] . '=自定义变量1' : ''; goto KWM23; O2NWc: $activity = pdo_fetch($sql, $params); goto V_o5o; ecyHx: if (!pdo_tableexists('dayu_wxcard_activity')) { goto TlA6k; } goto RHxe4; nAO6p: yo7RJ: goto Cv3iL; lc4v4: $links = '保存表单后生成链接'; goto aOdeW; eZNW7: if ($reply) { goto KFZ9d; } goto QA3mP; ap7Y2: $print = pdo_fetchall('SELECT * FROM ' . tablename('dayu_print') . ' WHERE weid = :weid and status=1', array(":weid" => $_W['uniacid'])); goto ShFiP; q9hM7: $record['list'] = intval($_GPC['list']); goto PFq2L; rHsjU: $record['paixu'] = intval($_GPC['paixu']); goto ikjHC; Olp4P: $types['checkbox'] = '多选(checkbox)'; goto TW0fg; mgYwK: if ($reid) { goto FHFOJ; } goto lc4v4; Hy6Pf: $dayu_check = pdo_fetchall('SELECT * FROM ' . tablename('dayu_check_category') . ' WHERE weid = :weid', array(":weid" => $_W['uniacid'])); goto nsqao; Z2OdD: if (is_array($_GPC['slide'])) { goto hfHqk; } goto ui_1F; erjrx: zIyV6: goto LB7XU; VeBNZ: c35_c: goto yk4pC; k8BYS: XAnmV: goto LwxDL; CkUNA: $ds = pdo_fetchall($sql, $params); goto lKxEf; oQ0iK: $record['thumb'] = $_GPC['thumb']; goto kaIzC; a6OPC: lRgBu: goto nAO6p; KRZJ4: $record['inhome'] = intval($_GPC['inhome']); goto qsn2C; nsqao: Xy_Ff: goto VSqEc; eiHGp: $params[':reid'] = $reid; goto TklLf; zldqD: $par = iunserializer($activity['par']); goto yLG0i; Xr21q: $record['mfoot'] = trim($_GPC['mfoot']); goto NCmLe; N98YW: KFZ9d: goto AkEzA; VJQoZ: foreach ($_GPC['title'] as $k => $v) { goto B7rw0; t0N0Z: $field['loc'] = $_GPC['loc'][$k]; goto ijbxS; mp3EG: hlZEW: goto g70Tn; S8roZ: $field['displayorder'] = range_limit($_GPC['displayorder'][$k], 0, 254); goto AVtJW; V44jn: $field['essential'] = $_GPC['essentialvalue'][$k] == 'true' ? 1 : 0; goto PEVap; ijbxS: pdo_insert('dayu_form_fields', $field); goto mp3EG; mH88W: $field['title'] = trim($v); goto S8roZ; neX3m: $field['description'] = $_GPC['desc'][$k]; goto e6pwh; e6pwh: $field['image'] = $_GPC['image'][$k]; goto t0N0Z; AVtJW: $field['type'] = $_GPC['type'][$k]; goto V44jn; B7rw0: $field = array(); goto G8Hsa; PEVap: $field['only'] = $_GPC['only'][$k]; goto m_3uO; m_3uO: $field['bind'] = $_GPC['bind'][$k]; goto nM44L; nM44L: $field['value'] = $_GPC['value'][$k]; goto zrKo_; G8Hsa: $field['reid'] = $reid; goto mH88W; zrKo_: $field['value'] = urldecode($field['value']); goto neX3m; g70Tn: } goto uB9yo; n5mKw: $activity = array("kfirst" => "有客户提交新的表单，请及时跟进", "kfoot" => "点击处理客户提交的表单。", "mfirst" => "受理结果通知", "mfoot" => "如有疑问，请致电联系我们。", "information" => "您提交申请我们已经收到, 请等待客服跟进.", "adds" => "联系地址", "voice" => "录音", "voicedec" => "录音说明", "pluraltit" => "上传图片", "status" => 1, "credit" => 0, "member" => "姓名", "phone" => "手机", "endtime" => date('Y-m-d H:i:s', strtotime('+365 day'))); goto N98YW; eZMeO: $types['email'] = '电子邮件(email)'; goto KiylS; KrE3Q: if ($op == 'verify') { goto vsfRs; } goto Hukwl; gUI2V: goto Lz10y; goto HTek1; qSSDY: $record['isget'] = intval($_GPC['isget']); goto yqn97; RaEeo: $record['isinfo'] = intval($_GPC['isinfo']); goto fCips; ikjHC: $record['pluraltit'] = trim($_GPC['pluraltit']); goto jqsu8; QCy4A: $record['isrevoice'] = intval($_GPC['isrevoice']); goto Ycp6S; b6rQe: $params = array(); goto bNIHw; Xi7Nd: $params[':weid'] = $_W['uniacid']; goto LCnqd; xahkr: itoast('保存表单失败2, 请稍后重试.', '', 'error'); goto k1dhR; rn_yJ: $record['isloc'] = intval($_GPC['isloc']); goto qSSDY; ui_1F: $record['slide'] = ''; goto aIxvC; yUxVF: foreach ($permission as &$p) { goto n7Wu8; TaSSR: kvss4: goto yZg1P; n7Wu8: $p['user'] = $this->get_role($p['uid']); goto UwHKc; l0wkZ: x69Et: goto TaSSR; kadjN: $p['select'] = 1; goto l0wkZ; UwHKc: if (!(!empty($role) && in_array($p['uid'], $rolearr))) { goto x69Et; } goto kadjN; yZg1P: } goto LoL1y; uB9yo: MEDIc: goto Rw_S3; M8yO9: if (!pdo_tableexists('dayu_consult_category')) { goto RqTgu; } goto hk0Lq; SFYus: $sql = 'DELETE FROM ' . tablename($this->tb_field) . ' WHERE `reid`=:reid'; goto XKRpD; LoL1y: vExyL: goto sarjS; AITXY: $types['text'] = '字符串(text)'; goto HrBJu; HTek1: XSM6j: goto tcrLk; exwwf: goto S8PDY; goto LnOJP; Uw9G0: zK1zL: goto VwFtp; NCmLe: $record['m_templateid'] = trim($_GPC['m_templateid']); goto qSneh; dj0Y2: $params[':weid'] = $_W['uniacid']; goto lYoc0; dkocw: $record['noticeemail'] = trim($_GPC['noticeemail']); goto NWHW_; kgOqQ: itoast('保存表单失败1, 请稍后重试.', '', 'error'); goto NxE3u; s9ltT: if (!($_GPC['role'] && $reid)) { goto lRgBu; } goto FJ1EX; KzNJX: $types['tel'] = '电话(tel)'; goto suxeC; brIO3: $data = array("edit" => intval($_GPC['edit']), "isdel" => intval($_GPC['isdel']), "follow" => intval($_GPC['follow']), "replace" => intval($_GPC['replace']), "card" => intval($_GPC['card']), "pretotal" => intval($_GPC['pretotal']), "daynum" => intval($_GPC['daynum']), "allnum" => intval($_GPC['allnum']), "header" => intval($_GPC['header']), "state1" => trim($_GPC['state1']), "state2" => trim($_GPC['state2']), "state3" => trim($_GPC['state3']), "state4" => trim($_GPC['state4']), "state5" => trim($_GPC['state5']), "state8" => trim($_GPC['state8']), "var1t" => trim($_GPC['var1t']), "var1" => trim($_GPC['var1']), "var2t" => trim($_GPC['var2t']), "var2" => trim($_GPC['var2']), "var3t" => trim($_GPC['var3t']), "var3" => trim($_GPC['var3']), "title" => trim($_GPC['titles']), "ismname" => intval($_GPC['ismname']), "mname" => trim($_GPC['mname']), "submitname" => trim($_GPC['submitname']), "btncolor" => trim($_GPC['btncolor']), "business" => trim($_GPC['business']), "address" => trim($_GPC['address']), "tel" => trim($_GPC['tel']), "lat" => $_GPC['baidumap']['lat'], "lng" => $_GPC['baidumap']['lng'], "noticeurl" => trim($_GPC['noticeurl']), "kami" => intval($_GPC['kami']), "print" => intval($_GPC['print']), "sendkami" => intval($_GPC['sendkami']), "comment" => intval($_GPC['comment']), "commenttitle" => trim($_GPC['commenttitle']), "consult" => intval($_GPC['consult']), "wxcard" => intval($_GPC['wxcard']), "getadd" => intval($_GPC['getadd']), "icredit" => intval($_GPC['icredit']), "onlytit" => trim($_GPC['onlytit']), "sms" => $_GPC['sms'], "smstype" => $_GPC['yztype'], "subtitle" => trim($_GPC['subtitle']), "icon" => $_GPC['icon'], "isrand" => $_GPC['isrand'], "randnum" => $_GPC['randnum'], "dayu_check" => intval($_GPC['dayu_check'])); goto SVZ1p; jqsu8: $record['plural'] = intval($_GPC['plural']); goto c3qQa; qsn2C: $record['member'] = trim($_GPC['member']); goto yZNOX; MrglE: kaloQ: goto Lb2wh; ynRzB: RqTgu: goto DMuo_; Q3W0i: P6q1R: goto Kz0q5; k1dhR: okosh: goto exwwf; sarjS: kOKKW: goto byvKd; FJ1EX: foreach ($_GPC['role'] as $rid) { goto aqn8Q; aqn8Q: $rid = intval($rid); goto YUNga; fInNP: LMtTX: goto IiAGT; YUNga: $insert = array("weid" => $_W['uniacid'], "reid" => $reid, "roleid" => $rid); goto Bus11; HXYfN: unset($insert); goto fInNP; Bus11: pdo_insert($this->tb_role, $insert) ? '' : itoast('抱歉，更新失败！', referer(), 'error'); goto HXYfN; IiAGT: } goto mF4N9; NOoRA: $par['map'] = array("lat" => $par['lat'], "lng" => $par['lng']); goto lmmbY; WI84Q: $record['smstype'] = $_GPC['smstype']; goto QQVQL; h_oUD: $behavior = $settings['creditbehaviors']; goto D9Dxu; Gu1Qs: if ($reid) { goto FTZj7; } goto kgOqQ; uXDbd: $types['radio'] = '单选(radio)'; goto Olp4P; wmhJ2: $record['kfoot'] = trim($_GPC['kfoot']); goto nPe4H; LB7XU: $record['status'] = intval($_GPC['status']); goto gADQ5; zVACA: txLzZ: goto zqVao; Auo6y: if (!pdo_tableexists('dayu_kami_category')) { goto m2tza; } goto eQeI4; bNIHw: $params[':reid'] = $reid; goto CkUNA; epAo9: $openid = trim($_GPC['openid']); goto y3cpo; LCnqd: $params[':reid'] = $reid; goto O2NWc; RVqf_: $settings = uni_setting($_W['uniacid'], array("creditnames", "creditbehaviors", "uc", "payment", "passport")); goto h_oUD; hbx38: $record['ivoice'] = intval($_GPC['ivoice']); goto rn_yJ; yLG0i: !empty($activity['slide']) && ($slide = iunserializer($activity['slide'])); goto NOoRA; z7Hv7: $record['skins'] = trim($_GPC['skins']); goto mO9WK; OlSZs: $params = array(); goto dj0Y2; XKRpD: $params = array(); goto eiHGp; pUCMp: file_delete($_GPC['thumb-old']); goto erjrx; G0zAV: $sendkami = pdo_fetchall('SELECT * FROM ' . tablename('dayu_sendkami_category') . ' WHERE weid = :weid', array(":weid" => $_W['uniacid'])); goto znSV7; E8fNs: $record['smsid'] = $_GPC['smsid']; goto PEnsz; mF4N9: muOR6: goto a6OPC; gXw8v: if (empty($skins)) { goto c35_c; } goto wVKlE; Vtnne: XVJsp: goto Lb0gD; bh0se: FHFOJ: goto Wu5DG; b9GYs: $record['slide'] = iserializer($se); goto V7F2e; No1Dh: $record['par'] = iserializer($data); goto Z2OdD; GsZjz: $groups = mc_groups(); goto WsaWy; tcJGd: hfHqk: goto eRsAk; D9Dxu: $creditnames = $settings['creditnames']; goto GsZjz; lvOvU: $record['voicedec'] = trim($_GPC['voicedec']); goto hbx38; oE7u3: $record['content'] = trim($_GPC['content']); goto AxssB; LwxDL: $reid = intval($_GPC['id']); goto hY6NI; byvKd: if (!checksubmit()) { goto kaloQ; } goto brIO3; lbnAh: $types['phone'] = '手机(phone)'; goto KzNJX; KiylS: $types['image'] = '上传图片(image)'; goto sI_U3; TW0fg: $types['select'] = '下拉框(select)'; goto dyWWk; KV1Q0: TlA6k: goto Auo6y; RhaLA: load()->model('mc'); goto RVqf_; BqDiM: $hasData = true; goto zVACA; xAI5p: $links = murl('entry', array("do" => "dayu_form", "id" => $reid, "m" => "dayu_form"), true, true) . $var1 . $var2 . $var3; goto Uw9G0; JdbuR: if (empty($permission)) { goto kOKKW; } goto yUxVF; znSV7: WrhO9: goto k_C3e; ancTZ: ae0UF: goto M8yO9; dyWWk: $types['calendar'] = '日历(calendar)'; goto eZMeO; x1z47: $record['starttime'] = strtotime($_GPC['starttime']); goto FMKbD; cn516: if (!empty($exist)) { goto P6q1R; } goto CNd1E; hk0Lq: $consult = pdo_fetchall('SELECT * FROM ' . tablename('dayu_consult_category') . ' WHERE weid = :weid', array(":weid" => $_W['uniacid'])); goto ynRzB; TkUcq: iDpYr: goto VeBNZ; ulUzd: $comment = pdo_fetchall('SELECT * FROM ' . tablename('dayu_comment_category') . ' WHERE weid = :weid', array(":weid" => $_W['uniacid'])); goto ancTZ; QA3mP: $par = array("state1" => "待受理", "state2" => "受理中", "state3" => "已完成", "state4" => "拒绝受理", "state5" => "已取消", "state8" => "退回修改", "mname" => "往期记录", "submitname" => "立 即 提 交", "header" => "1"); goto n5mKw; U_l5e: $var3 = !empty($par['var3']) ? '&' . $par['var3'] . '=自定义变量3' : ''; goto xAI5p; yk4pC: Nid4g: goto bw2Cd; qSneh: $record['mobile'] = trim($_GPC['mobile']); goto z7Hv7; Hukwl: goto XSoGv; goto k8BYS; gSIGA: $op = trim($_GPC['op']) ? trim($_GPC['op']) : 'post'; goto SQnzB; fCips: $record['isrethumb'] = intval($_GPC['isrethumb']); goto Y_5hs; ShFiP: Fw2Bk: goto ecyHx; Lb0gD: $permission = pdo_fetchall('SELECT id, uid, role FROM ' . tablename('uni_account_users') . ' WHERE uniacid = :weid and role != :role  ORDER BY uid ASC, role DESC', array(":role" => "clerk", ":weid" => $weid)); goto JdbuR; TklLf: pdo_query($sql, $params); goto VJQoZ; Y_5hs: $record['isvoice'] = intval($_GPC['isvoice']); goto QCy4A; Ycp6S: $record['voice'] = trim($_GPC['voice']); goto lvOvU; k_C3e: if (!pdo_tableexists('dayu_comment_category')) { goto ae0UF; } goto ulUzd; VSqEc: $category = pdo_fetchall('select * from ' . tablename($this->tb_category) . ' where weid = :weid ORDER BY id DESC', array(":weid" => $weid)); goto nYs5i; CpGNH: $params = array(); goto Xi7Nd; AkEzA: goto XSoGv; goto H8AVs; DMuo_: if (!pdo_tableexists('dayu_form_skins')) { goto Nid4g; } goto YOzH_; hY6NI: $hasData = false; goto HyaVU; lH2zX: if (!$_W['isajax']) { goto tPiyo; } goto epAo9; lwu7k: if ($op == 'post') { goto XAnmV; } goto KrE3Q; mO9WK: $record['outlink'] = trim($_GPC['outlink']); goto dmecL; CNd1E: message(error(-1, '未找到对应的粉丝编号，请检查昵称或openid是否有效'), '', 'ajax'); goto Q3W0i; OemNW: $activity['endtime'] && ($activity['endtime'] = date('Y-m-d H:i:s', $activity['endtime'])); goto zldqD; HrBJu: $types['textarea'] = '文本(textarea)'; goto uXDbd; yZNOX: $record['phone'] = trim($_GPC['phone']); goto x1z47; eQeI4: $kami = pdo_fetchall('SELECT * FROM ' . tablename('dayu_kami_category') . ' WHERE weid = :weid', array(":weid" => $_W['uniacid'])); goto Ex7In; eRsAk: foreach ($_GPC['slide'] as $slide) { $se[] = tomedia($slide); CcZYg: } goto GWfnb; p9QWk: p47NF: goto Vtnne; aWYVc: $exist = pdo_fetch($sql, array(":nickname" => $nickname, ":acid" => $_W['acid'])); goto gUI2V; bw2Cd: if (!pdo_tableexists('dayu_check_category')) { goto Xy_Ff; } goto Hy6Pf; Lc_Fo: $record['createtime'] = TIMESTAMP; goto BtkAw; nHThi: $reply = pdo_fetch($sql, $params); goto eZNW7; NPLLg: $reid = pdo_insertid(); goto Gu1Qs; SVZ1p: $record = array(); goto q9hM7; Ma1Ne: S8PDY: goto sFrMF; KWM23: $var2 = !empty($par['var2']) ? '&' . $par['var2'] . '=自定义变量2' : ''; goto U_l5e; suxeC: $types['idcard'] = '身份证(idcard)'; goto AMReZ; Af7Hp: $record['status'] = 1; goto Lc_Fo; NxE3u: FTZj7: goto Ma1Ne; WsaWy: $role = pdo_fetchall('SELECT roleid FROM ' . tablename($this->tb_role) . " WHERE weid = '{$_W['uniacid']}' AND reid = '{$reid}'"); goto LM0I3; LM0I3: if (empty($role)) { goto XVJsp; } goto Rffum; kaIzC: load()->func('file'); goto pUCMp; sFrMF: if (!($setting['role'] == 1 && ($_W['role'] == 'founder' || $_W['role'] == 'manager'))) { goto yo7RJ; } goto e3Okm; YAAJo: if (!pdo_tableexists('dayu_print')) { goto Fw2Bk; } goto ap7Y2; LnOJP: gLDJy: goto Af7Hp; AMReZ: pdo_tableexists('dayu_photograph_fields') && ($types['photograph'] = '证件照(photo)'); goto uC7c0; uqCJ2: $sql = 'SELECT COUNT(*) FROM ' . tablename($this->tb_info) . ' WHERE `reid`=' . $reid; goto DbNqm; HyaVU: if (!$reid) { goto plnrk; } goto uqCJ2; uH0hc: if (!pdo_tableexists('dayu_sms')) { goto VXbtA; } goto T1DeX; sI_U3: $types['range'] = '日期时间(range)'; goto hiOk2; V7F2e: RjfqL: goto RyYQd; DbNqm: if (!(pdo_fetchcolumn($sql) > 0)) { goto txLzZ; } goto BqDiM; Ny43C: $exist = pdo_fetch($sql, array(":openid" => $openid, ":acid" => $_W['acid'])); goto B_ZFg; Kz0q5: message(error(0, $exist), '', 'ajax'); goto W2AP1; BtkAw: pdo_insert('dayu_form', $record); goto NPLLg; Rffum: foreach ($role as $r) { $rolearr[] = $r['roleid']; T5hjL: } goto p9QWk; PdwxE: require MODULE_ROOT . '/fans.web.php'; goto gSIGA; SQnzB: $modules = uni_modules(); goto lwu7k; VwFtp: $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `reid`=:reid'; goto OlSZs; gADQ5: $record['custom_status'] = intval($_GPC['custom_status']); goto KRZJ4; zB386: include $this->template('post'); goto QF0l5; xu8B3: VXbtA: goto YAAJo; aOdeW: goto zK1zL; goto bh0se; RyYQd: if (empty($reid)) { goto gLDJy; } goto Jiv6D; yqn97: $record['credit'] = $_GPC['credit']; goto E8fNs; FMKbD: $record['endtime'] = strtotime($_GPC['endtime']); goto dkocw; Wu5DG: $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `reid`=:reid'; goto CpGNH; e3Okm: pdo_delete($this->tb_role, array("weid" => $_W['uniacid'], "reid" => $reid)); goto s9ltT; pC57j: $types['number'] = '数字(number)'; goto AITXY; B_ZFg: Lz10y: goto cn516; RHxe4: $wxcard = pdo_fetchall('SELECT * FROM ' . tablename('dayu_wxcard_activity') . ' WHERE weid = :weid and status=1', array(":weid" => $_W['uniacid'])); goto KV1Q0; uC7c0: $fields = mc_fields(); goto mgYwK; y3cpo: $nickname = trim($_GPC['nickname']); goto IW3T4; T1DeX: $sms = pdo_fetchall('SELECT * FROM ' . tablename('dayu_sms') . ' WHERE weid = :weid', array(":weid" => $_W['uniacid'])); goto xu8B3; c5fUI: $record['description'] = trim($_GPC['description']); goto oE7u3; zdOfE: if (!pdo_tableexists('dayu_sendkami_category')) { goto WrhO9; } goto G0zAV; lmmbY: if (!$activity) { goto mzb6R; } goto K1TsT; Ex7In: m2tza: goto zdOfE; nPe4H: $record['mfirst'] = trim($_GPC['mfirst']); goto Xr21q; Jiv6D: if (!(pdo_update('dayu_form', $record, array("reid" => $reid)) === false)) { goto okosh; } goto xahkr; NWHW_: $record['k_templateid'] = trim($_GPC['k_templateid']); goto uII6P; GWfnb: bYxcI: goto b9GYs; TVGvB: $record['weid'] = $_W['uniacid']; goto c5fUI; HIaSp: global $_W, $_GPC; goto PdwxE; lKxEf: mzb6R: goto x5Ml0; PEnsz: $record['smsnotice'] = $_GPC['smsnotice']; goto WI84Q; YOzH_: $skins = pdo_fetchall('SELECT * FROM ' . tablename('dayu_form_skins') . ' WHERE status = 1 ORDER BY id', array()); goto gXw8v; H8AVs: vsfRs: goto lH2zX; W2AP1: tPiyo: goto xbjtk; Cv3iL: if ($hasData) { goto aWjIC; } goto SFYus; hiOk2: $types['reside'] = '省市区(reside)'; goto lbnAh; QF0l5: } public function doWebBatchrRcord() { goto oShG6; INT3A: if (!empty($reply)) { goto Qhm1y; } goto kT4Ar; oxgp4: $role = $this->get_isrole($reid, $_W['user']['uid']); goto iPVw3; cmI2L: $result['msg'] = '记录批量删除成功！'; goto Ndcq6; MhfAU: Qhm1y: goto z7zVl; EgRNb: $result['status'] = 1; goto cmI2L; FPLQP: $reply = pdo_fetch('select reid from ' . tablename($this->tb_form) . ' where reid = :reid', array(":reid" => $reid)); goto INT3A; XUnAb: require MODULE_ROOT . '/fans.web.php'; goto ZeBdo; JukLD: message('您没有权限进行该操作.'); goto OlflB; OlflB: hXxuL: goto FPLQP; kT4Ar: $result['status'] = 0; goto AFN9V; oShG6: global $_GPC, $_W; goto XUnAb; AFN9V: $result['msg'] = '抱歉，表单主题不存在或是已经被删除！'; goto MhfAU; ZeBdo: $reid = intval($_GPC['reid']); goto oxgp4; Ndcq6: message($result, '', 'ajax'); goto vp4ib; iPVw3: if (!($setting['role'] == 1 && $_W['role'] == 'operator' && !$role)) { goto hXxuL; } goto JukLD; z7zVl: foreach ($_GPC['idArr'] as $k => $rerid) { goto vdBIR; Un4J4: yDwCS: goto TVhdi; vdBIR: pdo_delete($this->tb_info, array("rerid" => $rerid, "reid" => $reid)); goto wmEwf; wmEwf: pdo_delete($this->tb_data, array("rerid" => $rerid, "reid" => $reid)); goto Un4J4; TVhdi: } goto WnZav; WnZav: v_v7r: goto EgRNb; vp4ib: } public function doWebRecordSet() { goto xoYin; ZtSot: message('保存成功.', 'refresh'); goto nBr9Z; ya09V: $record['fields'] = iserializer($th); goto DqhqR; UkNIm: fkpqM: goto ya09V; evNRx: foreach ($_GPC['fields'] as $fields) { $th[] = $fields; tFlyW: } goto UkNIm; Wd5nl: $reid = intval($_GPC['reid']); goto t2a6_; bit3t: $params = array(); goto jGic3; IeRZc: require MODULE_ROOT . '/fans.web.php'; goto Wd5nl; xoYin: global $_W, $_GPC; goto IeRZc; QPHOl: if (!(pdo_update('dayu_form', $record, array("reid" => $reid)) === false)) { goto icbVW; } goto RNR67; t2a6_: $role = $this->get_isrole($reid, $_W['user']['uid']); goto T11wz; UL1Hp: if (empty($_GPC['fields'])) { goto GmcAO; } goto evNRx; vkMKT: icbVW: goto ZtSot; dRdyd: $record['avatar'] = intval($_GPC['avatar']); goto nvyz0; l90yT: $sql = 'SELECT * FROM ' . tablename($this->tb_field) . ' WHERE `reid`=:reid ORDER BY `displayorder` DESC,`refid`'; goto bit3t; T6w2C: $params = array(); goto g1BGo; Bbl2E: if (!checksubmit()) { goto MmrGH; } goto UEFTI; g1BGo: $params[':weid'] = $_W['uniacid']; goto MPYna; T11wz: if (!($setting['role'] == 1 && $_W['role'] == 'operator' && !$role)) { goto KeRYi; } goto Pp9Lw; UEFTI: $record = array(); goto nH2ZJ; iw9Cr: $activity = pdo_fetch($sql, $params); goto tYoGs; MPYna: $params[':reid'] = $reid; goto iw9Cr; RHAio: $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `reid`=:reid'; goto T6w2C; RNR67: message('保存表单失败, 请稍后重试.'); goto vkMKT; qtvvH: include $this->template('recordset'); goto x6AvV; nH2ZJ: $record['field'] = intval($_GPC['field']); goto dRdyd; Pp9Lw: message('您没有权限进行该操作.'); goto nGHw_; nGHw_: KeRYi: goto RHAio; nvyz0: $record['bcolor'] = $_GPC['bcolor']; goto UL1Hp; DqhqR: GmcAO: goto QPHOl; tYoGs: $record = iunserializer($activity['fields']); goto l90yT; LYlHL: $ds = pdo_fetchall($sql, $params); goto Bbl2E; jGic3: $params[':reid'] = $reid; goto LYlHL; nBr9Z: MmrGH: goto qtvvH; x6AvV: } public function doWebCustom() { goto yg38h; rnrS2: include $this->template('custom'); goto tIqkD; nauFl: RarCE: goto GlJVU; BcVsg: if (!($setting['role'] == 1 && $_W['role'] == 'operator' && !$role)) { goto gbZ1k; } goto LJ_xy; eXRpR: itoast('抱歉，快捷回复内容不存在或是已经被删除！', $this->createWebUrl('custom', array("op" => "display")), 'error'); goto R3t9i; tzdxf: pdo_insert($this->tb_custom, $data); goto mrf27; LJ_xy: message('您没有权限进行该操作.'); goto g7kD0; FPw3S: if (!empty($_GPC['raply'])) { goto Ap8RK; } goto S1zWM; ey8bU: if ($operation == 'display') { goto WArD_; } goto jcRal; GlJVU: include $this->template('custom'); goto Ro7_y; i3I35: $op = $operation = $_GPC['op'] ? $_GPC['op'] : 'display'; goto ey8bU; ASNEy: foreach ($_GPC['displayorder'] as $id => $displayorder) { pdo_update($this->tb_custom, array("displayorder" => $displayorder), array("id" => $id)); vQ0FD: } goto ZC0Ur; yg38h: global $_W, $_GPC; goto UqNOX; m3aZs: goto dcVOR; goto q5Jli; GB2mP: $custom = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_custom) . ' WHERE weid = :weid ORDER BY displayorder DESC', array(":weid" => $weid)); goto rnrS2; WuQda: dcVOR: goto NHSmV; ZKZRy: $data = array("weid" => $weid, "displayorder" => intval($_GPC['displayorder']), "raply" => $_GPC['raply']); goto ePyiJ; ZTW8e: $role = $this->get_isrole($id, $_W['user']['uid']); goto BcVsg; pMPvr: $id = intval($_GPC['id']); goto DJYSa; Ir4VV: itoast('快捷回复内容排序更新成功！', $this->createWebUrl('custom', array("op" => "display")), 'success'); goto Q4gi8; n2U3s: WArD_: goto Ue292; kJpIP: pdo_delete($this->tb_custom, array("id" => $id)); goto XnjTK; vc872: $city = array("displayorder" => 0); goto m3aZs; XnjTK: itoast('快捷回复内容删除成功！', $this->createWebUrl('custom', array("op" => "display")), 'success'); goto c05Xr; NHSmV: if (!checksubmit('submit')) { goto RarCE; } goto FPw3S; J5IFc: if ($operation == 'delete') { goto kkBsA; } goto sm17q; DJYSa: $custom = pdo_fetch('SELECT * FROM ' . tablename($this->tb_custom) . " WHERE id = '{$id}'"); goto zJbfh; R3t9i: pCbqv: goto kJpIP; XpSxa: if (!empty($id)) { goto YY9fI; } goto vc872; kRXpq: $id = $_GPC['id']; goto ZTW8e; Ue292: if (empty($_GPC['displayorder'])) { goto Qx2oB; } goto ASNEy; jcRal: if ($operation == 'post') { goto gNDN5; } goto J5IFc; z317W: goto OmFCN; goto nEJlj; Q4gi8: Qx2oB: goto GB2mP; q5Jli: YY9fI: goto C0Lxn; ZC0Ur: A82lP: goto Ir4VV; XwEvn: gNDN5: goto MrwqK; tIqkD: goto jVfom; goto XwEvn; aM8D9: itoast('更新快捷回复内容成功！', $this->createWebUrl('custom', array("op" => "display")), 'success'); goto nauFl; ni3Bh: kkBsA: goto pMPvr; UqNOX: require MODULE_ROOT . '/fans.web.php'; goto kRXpq; sm17q: goto jVfom; goto n2U3s; zJbfh: if (!empty($custom)) { goto pCbqv; } goto eXRpR; c05Xr: jVfom: goto hlFsy; EtCVW: pdo_update($this->tb_custom, $data, array("id" => $id)); goto kl3Ux; ePyiJ: if (!empty($id)) { goto xDYaG; } goto tzdxf; mrf27: $id = pdo_insertid(); goto z317W; Ro7_y: goto jVfom; goto ni3Bh; kl3Ux: OmFCN: goto aM8D9; DhQ7N: Ap8RK: goto ZKZRy; g7kD0: gbZ1k: goto Es7SZ; nEJlj: xDYaG: goto EtCVW; Es7SZ: load()->func('tpl'); goto i3I35; MrwqK: $id = intval($_GPC['id']); goto XpSxa; C0Lxn: $custom = pdo_fetch('SELECT * FROM ' . tablename($this->tb_custom) . " WHERE id = '{$id}'"); goto WuQda; S1zWM: message('抱歉，请输入快捷回复内容！'); goto DhQ7N; hlFsy: } public function doMobilerecord() { goto kIunM; GOgho: $rows = pdo_fetchall($sql, $params, 'rerid'); goto Pwmra; Mo5uX: $record = iunserializer($activity['fields']); goto Yhqou; JvfN3: $reid = intval($_GPC['id']); goto si00V; XpX7C: $list = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_form) . ' WHERE weid = :weid and kfid = :openid and status = 1 ORDER BY reid DESC', array(":weid" => $weid, ":openid" => $openid), 'reid'); goto r7kbf; MuGCS: $psize = 10; goto mFO4S; SO34C: $params[':weid'] = $_W['uniacid']; goto J2Rxd; omowJ: $sql = 'SELECT * FROM ' . tablename($this->tb_info) . " WHERE reid=:reid {$where} ORDER BY createtime DESC,rerid DESC LIMIT " . ($pindex - 1) * $psize . ',' . $psize; goto qWi45; si00V: $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `reid`=:reid'; goto j4Eat; r7kbf: $pindex = max(1, intval($_GPC['page'])); goto MuGCS; nQXgm: foreach ($childlist as $reply => $r) { goto TzMH8; PokRB: Q_Ln4: goto NNqkd; kQYt1: $children[$r['rerid']][] = $r; goto INKhM; INKhM: unset($children[$reply]); goto PokRB; TzMH8: if (empty($r['rerid'])) { goto Q_Ln4; } goto kQYt1; NNqkd: zZVbG: goto egYZl; egYZl: } goto vQ9Ng; CHDz6: $activity = pdo_fetch($sql, $params); goto N6d51; vh9WO: $params[':reid'] = $reid; goto JSq25; fkOdd: $pager = $this->pagination($total, $pindex, $psize); goto GOgho; xrTtN: require MODULE_ROOT . '/fans.mobile.php'; goto JvfN3; HAm80: $state = !empty($activity['state3']) ? $activity['state3'] : '已完成'; goto XOm7l; yEAxZ: $childlist = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_data) . ' WHERE rerid IN (\'' . implode('\',\'', is_array($rerid) ? $rerid : array($rerid)) . "') AND `reid`=:reid AND `refid` IN ({$fids}) ORDER BY displayorder DESC,rerid DESC", array(":reid" => $reid)); goto nQXgm; Yhqou: $fids = implode(',', $record); goto DvakE; jdp0R: $this->showMessage('非法访问，主题不存在', '', 'error', '', ''); goto CMWam; Pwmra: foreach ($rows as $index => $row) { goto B35WE; laiWw: if (empty($row['rethumb'])) { goto cKE_7; } goto a7ur4; TzGsf: $rows[$index]['thumbs'] = !empty($row['rethumb']) ? iunserializer($row['thumb']) : ''; goto T9Nei; Yg98K: $rows[$index]['thumb'] = iunserializer($row['thumb']); goto TzGsf; zpnbe: cKE_7: goto OIEnu; ED7tp: JyMHW: goto snTel; OIEnu: $maps = $piclist; goto ED7tp; f__ut: foreach ($rows[$index]['thumbs'] as $p) { $piclist .= is_array($p) ? $p['attachment'] : tomedia($p) . ','; yDq3j: } goto suOux; j9eLS: $rows[$index]['rethumb'] = !empty($row['rethumb']) ? iunserializer($row['rethumb']) : ''; goto laiWw; a7ur4: foreach ($rows[$index]['rethumb'] as $p) { $piclist .= is_array($p) ? $p['attachment'] : tomedia($p) . ','; cYo5d: } goto uX04Y; uX04Y: bJrtn: goto zpnbe; T9Nei: if (empty($row['rethumb'])) { goto fMa1Z; } goto f__ut; Rts2B: fMa1Z: goto j9eLS; suOux: BsCah: goto Rts2B; B35WE: $rows[$index]['user'] = mc_fansinfo($row['openid'], $acid, $weid); goto Yg98K; snTel: } goto ILuYf; kIunM: global $_W, $_GPC; goto xrTtN; DvakE: $rerid = array_keys($rows); goto ResiK; mFO4S: $where .= ' and status=3'; goto omowJ; vQ9Ng: p2g9d: goto HAm80; J2Rxd: $params[':reid'] = $reid; goto CHDz6; CMWam: i3snT: goto XpX7C; JSq25: $total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_info) . " WHERE reid = :reid {$where} ", $params); goto fkOdd; j4Eat: $params = array(); goto SO34C; XOm7l: include $this->template('record'); goto oYNRg; ResiK: $children = array(); goto yEAxZ; ILuYf: AQc0f: goto Mo5uX; N6d51: if ($activity) { goto i3snT; } goto jdp0R; qWi45: $params = array(); goto vh9WO; oYNRg: } public function doMobileCheckOnly() { goto kADph; O1hzM: goto d_djq; goto XV38x; b4K7G: $result['msg'] = $_GPC['title'] . $msg; goto eNPzt; N6Wg5: $par = iunserializer($activity['par']); goto q8eq0; G7Y2n: $result['status'] = '1'; goto TFRWH; q2oU_: if ($_GPC['content'] == $data['data']) { goto nVy05; } goto G7Y2n; cFZto: $result['status'] = '0'; goto b4K7G; fhuXv: message($result, '', 'ajax'); goto Pu3I8; eNPzt: d_djq: goto fhuXv; kADph: global $_W, $_GPC; goto MQF7B; MQF7B: $activity = pdo_get($this->tb_form, array("weid" => $_W['uniacid'], "reid" => $_GPC['reid']), array("par")); goto N6Wg5; XV38x: nVy05: goto cFZto; q8eq0: $msg = !empty($par['onlytit']) ? $par['onlytit'] : '存在相同内容，请重新填写'; goto E3Jb9; TFRWH: $result['msg'] = '可使用'; goto O1hzM; E3Jb9: $data = pdo_fetch('SELECT * FROM ' . tablename($this->tb_data) . ' WHERE reid = :reid AND refid = :refid', array(":reid" => $_GPC['reid'], ":refid" => $_GPC['refid'])); goto q2oU_; Pu3I8: } public function doMobileEdit() { goto bq2pH; zadss: $_share['imgUrl'] = tomedia($activity['thumb']); goto BGMu3; Gp1wj: foreach ($field as $f) { goto DTxUf; DU9Ow: $ds[$f['refid']]['tixing'] = !empty($f['description']) ? urldecode($f['description']) : '请输入' . $f['title']; goto buVU4; S2oow: $ds[$f['refid']]['default'] = $f['default']; goto gkHEe; dabtq: if (!($f['type'] == 'image')) { goto VwR5O; } goto ygZcs; ohw6x: goto CImPS; goto se4Yv; c4E93: $reside = $f; goto sp_DD; gkHEe: $ds[$f['refid']]['loc'] = $f['loc']; goto kQT5l; maHlp: $ds[$f['refid']]['tixing'] = !empty($f['description']) ? urldecode($f['description']) : '请填写' . $f['title']; goto o7dQd; el0oa: $ds[$f['refid']]['type'] = $f['type']; goto GGzpF; McdHI: if (!(!empty($f['loc']) && pdo_tableexists('dayu_form_plugin_radio') && ($f['type'] == 'radio' || $f['type'] == 'checkbox'))) { goto SFM7s; } goto dj3C7; Py5MR: $ds[$f['refid']]['options'] = explode(',', $f['value']); goto vT0L7; kQT5l: $fids[] = $f['refid']; goto RtTxg; GGzpF: $ds[$f['refid']]['refid'] = $f['refid']; goto yK5s4; e9dPZ: $ds[$f['refid']]['tixing'] = !empty($f['description']) ? urldecode($f['description']) : '请选择' . $f['title']; goto ohw6x; buVU4: goto CImPS; goto f2GIs; cihEf: $f['default'] = $profile[$f['bind']]; goto jeO6E; f2GIs: F03h5: goto maHlp; DTxUf: if (!($f['type'] == 'reside')) { goto wa68y; } goto c4E93; xv4yg: if (in_array($f['type'], array("text", "number", "email"))) { goto ibXzk; } goto gsGhm; se4Yv: ibXzk: goto DU9Ow; Mcrio: $ds[$f['refid']]['photograph_url'] = $f['photograph_url']; goto S2oow; vT0L7: $ds[$f['refid']]['fid'] = $f['title']; goto el0oa; hAF9e: VwR5O: goto Py5MR; RtTxg: p2_01: goto ezGYz; dj3C7: $ds[$f['refid']]['dayu_radio'] = pdo_fetchall('SELECT * FROM ' . tablename('dayu_form_plugin_radio') . ' WHERE weid = :weid and cid = :cid and status=1 ORDER BY id DESC', array(":weid" => $weid, ":cid" => $f['loc'])); goto xRuQ_; yK5s4: $ds[$f['refid']]['essential'] = $f['essential']; goto Mcrio; gsGhm: if ($f['type'] == 'textarea') { goto F03h5; } goto e9dPZ; HfA6G: if (!$profile[$f['bind']]) { goto srudR; } goto cihEf; ygZcs: $ds[$f['refid']]['image'] = !empty($f['image']) ? $f['image'] : TEMPLATE_WEUI . 'images/nopic.jpg'; goto hAF9e; jeO6E: srudR: goto xv4yg; xRuQ_: SFM7s: goto dabtq; o7dQd: CImPS: goto McdHI; sp_DD: wa68y: goto HfA6G; ezGYz: } goto i68gE; b8MUL: if ($activity['custom_status'] == 0 && $staff) { goto A4dEG; } goto Zu5m1; e1m1i: $info = pdo_get($this->tb_info, array("rerid" => $rerid), array()); goto ScdGA; z31gG: $acc = WeAccount::create($_W['acid']); goto Gy6ne; uGvsd: $activity = pdo_get($this->tb_form, array("weid" => $weid, "reid" => $reid), array()); goto pB3aG; RCefv: exit; goto kpe1z; ZOoBs: $rerid = intval($_GPC['rerid']); goto uGvsd; Gy6ne: foreach ($staff as $s) { goto FooDY; FooDY: $url = $_W['siteroot'] . 'app/' . $this->createMobileUrl('manageform', array("name" => "dayu_form", "id" => $reid)); goto T7hpU; awkdr: $custom = array("msgtype" => "text", "text" => array("content" => urlencode($info)), "touser" => $s['openid']); goto V5Rs9; T7hpU: $info = '【您好，有新的消息】

'; goto laF0S; AOkX3: Rcpeu: goto dN48b; V5Rs9: $CustomNotice = $acc->sendCustomNotice($custom); goto AOkX3; KbWHQ: $info .= "<a href='{$url}'>点击查看详情</a>"; goto awkdr; laF0S: $info .= "姓名：{$_GPC['member']}
手机：{$_GPC['mobile']}
内容：{$bodym}

"; goto KbWHQ; dN48b: } goto j8rBJ; OgvMa: IInTh: goto rPDEe; IaiZn: $datas = array(); goto cKNPU; UFXal: ihttp_email($activity['noticeemail'], $activity['title'] . '的表单提醒', '<h4>姓名：' . $info['member'] . '</h4><h4>手机：' . $info['mobile'] . '</h4>' . $body); goto BDQ3p; zCq42: $row = array(); goto gjqKh; uEIo1: if (!is_array($staff)) { goto gh1Zw; } goto bYRPk; tfSh3: $this->showMessage($activity['information'], $outlink); goto hnkkh; nuFkl: foreach ($_GPC['thumb'] as $thumb) { $th[] = tomedia($thumb); Cxqty: } goto OgvMa; B3_X4: YKYBS: goto ncqQR; utk0j: $reid = intval($_GPC['reid']); goto ZOoBs; VdXaJ: $title = $par['header'] == 1 ? $activity['title'] : $activity['titles']; goto xppsF; YNC1u: load()->func('tpl'); goto VdXaJ; CcMyx: foreach ($datas as $row) { goto nbu8J; nbu8J: if (!strstr($row['content']['data'], 'images')) { goto IGi6v; } goto D36er; jSZv8: $body .= '<h4>' . $row['content']['data'] . '</h4>'; goto P5H0Q; Ts6c4: Lvz0S: goto HLd2d; P5H0Q: $smsbody .= $row['content']['data'] . '，'; goto v31gL; dE3lr: $bodym .= '\\n　' . $row['content']['data']; goto jSZv8; mfIAO: IGi6v: goto yKqjE; v31gL: $bodnew .= !empty($row['content']['data']) ? '\\n' . $row['fid']['title'] . '：' . $row['content']['data'] : ''; goto Ts6c4; MnUdM: $row['fid'] = $this->get_fields($field['refid']); goto dE3lr; yKqjE: $field = pdo_get($this->tb_data, array("redid" => $row['refid']), array()); goto MnUdM; D36er: $row['data'] = '有'; goto mfIAO; HLd2d: } goto DPjrq; bq2pH: global $_W, $_GPC; goto YKUFu; ncqQR: gh1Zw: goto JgpRc; j8rBJ: REuHU: goto xzMO0; gjqKh: if (!is_array($_GPC['thumb'])) { goto T5AlF; } goto nuFkl; Zu5m1: if (!is_array($staff)) { goto v1Qmi; } goto z31gG; fS9Es: $profile = mc_fetch($uid, $binds); goto SAlzK; LPCQj: if (!empty($datas)) { goto Nlg2R; } goto gmRng; DPjrq: Ybq_P: goto ob3fu; YkMPu: $binds = array(); goto fS9Es; xzMO0: v1Qmi: goto WWMz7; UsUBR: $info['fields'] = $info['redid'] = array(); goto KRUtR; RtCmH: load()->func('communication'); goto UFXal; oiwoH: exit; goto UzsF8; kpe1z: Nlg2R: goto bHiiR; bYRPk: foreach ($staff as $s) { goto R_sNc; J2gAG: ms168: goto u0Mg1; Cg9qj: $this->send_template_message(urldecode(json_encode($template))); goto J2gAG; R_sNc: $template = array("touser" => $s['openid'], "template_id" => $activity['k_templateid'], "url" => $_W['siteroot'] . 'app/' . $this->createMobileUrl('manageform', array("name" => "dayu_form", "id" => $reid)), "topcolor" => "#FF0000", "data" => array("first" => array("value" => urlencode($activity['kfirst'] . '\\n'), "color" => "#743A3A"), "keyword1" => array("value" => urlencode($info['member']), "color" => "#000000"), "keyword2" => array("value" => urlencode($info['mobile']), "color" => "#000000"), "keyword3" => array("value" => urlencode($ytime), "color" => "#000000"), "keyword4" => array("value" => urlencode($status['name'] . '\\n' . $bodnew . '\\n'), "color" => "#FF0000"), "remark" => array("value" => urlencode($activity['kfoot']), "color" => "#008000"))); goto Cg9qj; u0Mg1: } goto B3_X4; sZ6Bg: if (!($_W['ispost'] || checksubmit('submit'))) { goto n_3Jc; } goto IaiZn; B08Xa: $outlink = !empty($activity['outlink']) ? $activity['outlink'] : $this->createMobileUrl('mydayu_form', array("op" => "detail", "rerid" => $rerid, "id" => $reid)); goto tfSh3; hHUTP: zjJdF: goto zCq42; YKUFu: require MODULE_ROOT . '/fans.mobile.php'; goto nyUgI; gIdR9: $this->showMessage('非法访问.', '', '', '', ''); goto jHf3a; BGMu3: $picker = 1; goto mAtfZ; M5hUv: $status = $this->get_status($reid, '0'); goto oGoq9; i68gE: KEBKb: goto Njuyv; e05oE: $ytime = date('Y-m-d H:i:s', TIMESTAMP); goto M5hUv; M3uHB: $row['status'] = '0'; goto V4W55; Y1JGv: T5AlF: goto M3uHB; WWMz7: goto HodCA; goto EiwbG; xppsF: $_share['title'] = $activity['title']; goto utJQ7; TpzL6: $this->showMessage('记录不存在或是已经被删除！', '', '', '', ''); goto oiwoH; pB3aG: $par = iunserializer($activity['par']); goto Aatk7; cKNPU: foreach ($_POST as $key => $value) { goto mmxUx; FBUrH: $entry['data'] = strval($value); goto nixJo; lIke4: pdo_update($this->tb_data, $entry, array("redid" => $key)); goto cPcSv; mmxUx: $entry = array(); goto FBUrH; cPcSv: NPkqR: goto Y6wQz; nixJo: $datas[] = array("content" => $entry, "refid" => $key); goto lIke4; Y6wQz: } goto hHUTP; utJQ7: $_share['content'] = $activity['description']; goto zadss; bHiiR: if (empty($datas)) { goto a1h85; } goto CcMyx; Zy_i1: $ds = $fids = array(); goto Gp1wj; oGoq9: $staff = pdo_fetchall('SELECT `openid` FROM ' . tablename($this->tb_staff) . ' WHERE reid=:reid AND weid=:weid', array(":weid" => $_W['uniacid'], ":reid" => $reid)); goto b8MUL; M9DAK: TOHsR: goto Zy_i1; UzsF8: ECHAn: goto YkMPu; nyUgI: $returnUrl = urlencode($_W['siteurl']); goto utk0j; jdfFq: ytaG4: goto sZ6Bg; V4W55: pdo_update($this->tb_info, $row, array("rerid" => $rerid)); goto LPCQj; JgpRc: HodCA: goto Wh8TV; rPDEe: $row['thumb'] = iserializer($th); goto Y1JGv; EiwbG: A4dEG: goto uEIo1; gmRng: $this->showMessage('非法访问，提交数据不能为空', '', 'error'); goto RCefv; BDQ3p: E1JCQ: goto e05oE; ob3fu: if (empty($activity['noticeemail'])) { goto E1JCQ; } goto RtCmH; SAlzK: $field = pdo_getall($this->tb_field, array("reid" => $reid), array(), '', 'displayorder DESC,refid DESC', ''); goto QEvQd; Wh8TV: a1h85: goto B08Xa; mAtfZ: include $this->template('edit'); goto COTM9; YWqdy: $fdatas = pdo_fetchall($sql, $params); goto VPGr3; hnkkh: n_3Jc: goto YNC1u; Aatk7: $submitname = !empty($par['submitname']) ? $par['submitname'] : '立 即 提 交'; goto e1m1i; KRUtR: $sql = 'SELECT * FROM ' . tablename($this->tb_data) . " WHERE `reid`='{$reid}' AND `rerid`='{$rerid}' AND `refid` IN ({$fids})"; goto YWqdy; VPGr3: foreach ($fdatas as $fd) { goto d7s02; JzWtO: $info['redid'][$fd['refid']] = $fd['redid']; goto VYpDv; KpTtk: $info['fields'][$fd['refid']] = $fd['data']; goto IW4Go; HL8Ft: $info['fields'][$fd['refid']] = tomedia($fd['data']); goto HD6f5; HD6f5: yWeHL: goto JzWtO; d7s02: if (strstr($fd['data'], 'images')) { goto MdaSe; } goto KpTtk; IW4Go: goto yWeHL; goto eMkkd; eMkkd: MdaSe: goto HL8Ft; VYpDv: AKSFf: goto goGq5; goGq5: } goto jdfFq; V5Eqb: $this->showMessage('不能修改内容', 'error'); goto M9DAK; Njuyv: $fids = implode(',', $fids); goto UsUBR; fHVz4: if (!($par['edit'] != '1' && $info['status'] != '8')) { goto TOHsR; } goto V5Eqb; jHf3a: E7N0r: goto fHVz4; QEvQd: if (!empty($field)) { goto E7N0r; } goto gIdR9; ScdGA: if (!($info['openid'] != $openid)) { goto ECHAn; } goto TpzL6; COTM9: } public function doMobiledayu_form() { goto HM0ZN; ghQxu: HT1DO: goto rwSL4; KUTdT: $row['mobile'] = $_GPC['mobile']; goto iVTdg; o9ppK: $sms_data = array("mobile" => $activity['mobile'], "title" => $activity['title'], "mname" => $member, "mmobile" => $_GPC['mobile'], "openid" => $row['openid'], "status" => $state['name'], "ptname" => $_W['account']['name'], "addons" => $_W['current_module']['name'], "content" => $alldata); goto vKYSJ; pq8Y_: $btncolor = $btncolor['css']; goto fN8XT; Mqft9: ViXi7: goto e7KyW; BDQsG: $ds = pdo_fetchall($sql, $params); goto sc3Lt; FdJmL: load()->func('communication'); goto D3y9j; fu1ac: if (empty($datas)) { goto yvSb7; } goto RfrfH; kH1kY: $this->showMessage('表单不完整，缺少自定义项目，无法正常访问.'); goto A6nP0; mdwXk: TP_x9: goto RF9D6; QCvPe: if (!(!empty($par['comment']) && pdo_tableexists('dayu_comment'))) { goto x0zXP; } goto YyFZ0; JhcTp: goto L12N8; goto F8N3u; iVTdg: $row['address'] = $_GPC['address']; goto oYm0y; ODqxR: if (!empty($rerid)) { goto l1oMn; } goto csGYS; egX_6: $row['kid'] = $kami['id']; goto Iw7hS; fveNi: foreach ($staff as $s) { goto UAnKk; UAnKk: $url = $_W['siteroot'] . 'app/' . $this->createMobileUrl('manageform', array("name" => "dayu_form", "weid" => $row['weid'], "id" => $row['reid'])); goto G3gzj; BgHtp: $info .= "<a href='{$url}'>点击查看详情</a>"; goto KU7Yp; KU7Yp: $custom = array("msgtype" => "text", "text" => array("content" => urlencode($info)), "touser" => $s['openid']); goto WSYQa; suweX: xxNv7: goto XigMe; GAHjm: $info .= "姓名：{$member}
手机：{$_GPC['mobile']}
内容：{$bodym}{$voice}{$kamiinfo}

"; goto BgHtp; G3gzj: $info = '【您好，有新的消息】

'; goto GAHjm; WSYQa: $CustomNotice = $acc->sendCustomNotice($custom); goto suweX; XigMe: } goto eCyYh; ENW09: $member = !empty($row['member']) ? $row['member'] : $fans['user']['nickname']; goto arjCM; hW15A: if (!($allnum >= intval($par['allnum']))) { goto hR2d5; } goto L0DGk; TgbPL: load()->model('mc'); goto wYmLX; HpG7Z: $row['openid'] = $openid; goto SvmXB; m0BlL: suO2R: goto y_G0v; D9jU3: $update['realname'] = $_GPC['member']; goto zLYoQ; L0DGk: $this->showMessage('名额已满', '', 'info'); goto He_0n; CyLQs: if (empty($activity['noticeemail'])) { goto qzrob; } goto FdJmL; Rhw2_: if (!($par['follow'] == 1)) { goto P_K_l; } goto FuDmN; qkQU9: exit; goto w6ZwJ; F0CZ4: $set = $this->module['config']; goto o677x; wbtCu: v4a2M: goto KoNSs; zgKQE: BD75M: goto rPOG2; yv1Yd: if (!($activity['endtime'] < TIMESTAMP)) { goto Cr_iL; } goto ljZ5Q; M7j21: $row['var1'] = $_GPC[$par['var1']]; goto uA8AU; mVDAW: foreach ($formdata as $index => $v) { $alldata[] = $v['title'] . ':' . $v['data'] . ','; f1k2H: } goto BReBm; e7KyW: if (!$_FILES) { goto c2EqS; } goto kHBpO; HmTVm: $ds = pdo_fetchall($sql, $params); goto n7zmZ; H9zct: $params[':reid'] = $reid; goto HmTVm; Gz83z: $this->showMessage('保存失败.'); goto zhtwx; YWwCz: x0zXP: goto M1Cjv; H67h0: $sql = 'SELECT * FROM ' . tablename($this->tb_field) . ' WHERE `reid` = :reid ORDER BY `displayorder` DESC'; goto srjaI; HCFHv: $params[':reid'] = $reid; goto BDQsG; BQ2eb: $log = $activity['title'] . '-' . $activity['credit'] . '积分'; goto lHZil; NHt4V: cs6vX: goto YOe4X; tB4NX: if (!empty($repeat)) { goto W9BJK; } goto f6wT2; IuFh9: return error(-1, $acc['message']); goto dUDuy; uhDYl: $groupid = $group['groupid']; goto ouROi; Ga8E1: if ($activity['custom_status'] == 0 && $staff && $activity['k_templateid']) { goto DCYwH; } goto gioa9; Ic34E: $kami = pdo_get('dayu_kami', array("weid" => $weid, "number" => $_GPC['kami'], "cid" => $par['kami']), array("id", "status", "number", "password")); goto eUfiD; zXQvj: $repeat = $_COOKIE['r_submit']; goto bv_Fx; F8N3u: W9BJK: goto zkl8k; kCt42: ohpdk: goto fu1ac; Lzl0n: $par = iunserializer($activity['par']); goto CBMK4; uTxTB: $sendkamidata = array("reid" => $reid, "infoid" => $rerid, "addons" => "dayu_form", "openid" => $openid, "name" => $row['member'], "mobile" => $row['mobile'], "status" => 1, "updatetime" => TIMESTAMP); goto HI4sU; ZSGpU: $activity['smsid'] && empty($member['mobile']) && $par['smstype'] == '1' && $this->showMessage('请完善手机号', murl('entry', array("do" => "index", "id" => $activity['smsid'], "m" => "dayu_sms", "form" => $_W['current_module']['name'], "returnurl" => $returnUrl), true, true), 'info'); goto TuIPP; ur04f: zufKd: goto wbtCu; KAXfu: if (!($par['replace'] && !empty($update))) { goto Eap4Q; } goto TgbPL; U95CK: iXQnS: goto YUUra; gioa9: if (!is_array($staff)) { goto PNrC6; } goto sxRy4; zhtwx: rGZJa: goto T17SW; dPQHj: J8aL0: goto o_T8D; f0P4n: $behavior = $settings['creditbehaviors']; goto BESVD; onf0G: qzrob: goto olmZY; dhWFW: $lg = array("l1" => intval($_GPC['linkage1']), "l2" => intval($_GPC['linkage2'])); goto AZf_L; okX85: FoOsT: goto CyLQs; z9Njk: Dh9jA: goto c3Vrl; cKB6_: foreach ($binds as $key => $value) { goto nKjIn; uY6T0: V3YSM: goto ItSsh; zf2wU: $binds[] = 'residedist'; goto nacgv; H__yI: $binds[] = 'resideprovince'; goto CoUk8; WeRpP: $binds[] = 'birthmonth'; goto mWMQg; Xip71: goto cs6vX; goto uY6T0; nacgv: goto cs6vX; goto z7jxO; CoUk8: $binds[] = 'residecity'; goto zf2wU; z7jxO: bmcwT: goto EFeb9; EFeb9: if (!($value == 'birth')) { goto V3YSM; } goto DjOzE; pOR40: $binds[] = 'birthyear'; goto WeRpP; DjOzE: unset($binds[$key]); goto pOR40; qF99m: unset($binds[$key]); goto H__yI; nKjIn: if (!($value == 'reside')) { goto bmcwT; } goto qF99m; mWMQg: $binds[] = 'birthday'; goto Xip71; ItSsh: X6nub: goto Z0gZc; Z0gZc: } goto NHt4V; n3F3R: l1oMn: goto XFyH3; HI4sU: pdo_update('dayu_sendkami', $sendkamidata, array("weid" => $weid, "id" => $row['kid'])); goto xsMkN; tPuPR: $params = array(); goto Vnza7; r2jbZ: $binds = array(); goto S3Luc; ahzKi: $sql = 'SELECT * FROM ' . tablename($this->tb_field) . ' WHERE `reid` = :reid ORDER BY `displayorder` DESC'; goto yDLED; CEkNK: load()->func('communication'); goto R6Dsr; FuDmN: $this->getFollow(); goto IldGH; LflYq: $this->showMessage($dayu_check_category['reminder'], $check_url, 'info'); goto GzO_B; SGpZF: pdo_update('dayu_form', $record, array("reid" => $reid)); goto U95CK; kdLpT: $this->showMessage('活动还未开始！<br>开始时间：' . date('Y-m-d H:i:s', $activity['starttime'])); goto wbjWs; XK5TP: $this->showMessage('非法访问，主题不存在'); goto q8i59; n1tuc: fw8o5: goto gPywp; YyFZ0: $comment = pdo_fetchall('SELECT * FROM ' . tablename('dayu_comment') . ' WHERE weid = :weid and reid = :reid ORDER BY reid DESC', array(":weid" => $weid, ":reid" => $reid)); goto YWwCz; D09W1: $returnUrl = urlencode($_W['siteurl']); goto nwn8u; r45ko: gzE0F: goto qXKOT; HXeFK: goto c5UI7; goto dPQHj; LueO1: Cr_iL: goto BNaDc; bh_9p: if ($kami['password'] != $_GPC['pass']) { goto jkRWy; } goto etyA6; FWVr9: if (!empty($activity['starttime'])) { goto iXQnS; } goto Ykh5d; sc3Lt: if ($ds) { goto KujQI; } goto kH1kY; AZf_L: $row['linkage'] = iserializer($lg); goto SUgLD; rwSL4: L2EXL: goto fysmu; XWuY1: mc_credit_update($uid, $behavior['activity'], $activity['credit'], array(0, $activity['title'])); goto uH71z; GzO_B: I7ac4: goto qcvbR; Ou4vR: if (empty($_GPC['linkage2'])) { goto GMThw; } goto dhWFW; kHBpO: load()->func('file'); goto H9aHo; Vl1Pl: $initRange = $initCalendar = false; goto r2jbZ; kGWTU: AMZFF: goto yHxO_; MyTZF: ulQGs: goto e4kkG; pwCNX: qDODg: goto CnJbD; oYm0y: $row['lat'] = $_GPC['getlat']; goto sPEVe; PcfDg: $row['getip'] = $_GPC['getip']; goto ZEg14; AvDxN: $sql = 'SELECT * FROM ' . tablename($this->tb_field) . ' WHERE `reid` = :reid ORDER BY rand() limit ' . $par['randnum']; goto hWBps; HM0ZN: global $_W, $_GPC; goto F0CZ4; iTToF: !empty($activity['slide']) && ($slide = iunserializer($activity['slide'])); goto Rhw2_; Ru5Fz: $update['residedist'] = $_GPC['reside']['district']; goto Q08YG; rD6AG: $time = date('Y-m-d', time()); goto bxjV2; EaXzW: $submitname = !empty($par['submitname']) ? $par['submitname'] : '立 即 提 交'; goto MEeRt; S3Luc: $profile = mc_fetch($uid, $binds); goto cKB6_; CIpsb: DCYwH: goto zsnDI; fN8XT: $linkage = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_linkage) . ' WHERE reid = :reid and parentid = 0 ORDER BY displayorder desc, id DESC', array(":reid" => $reid)); goto Vl1Pl; Vnza7: $params[':weid'] = $weid; goto SounT; vKYSJ: ihttp_post(murl('entry', array("do" => "Notice", "id" => $activity['smsnotice'], "m" => "dayu_sms"), true, true), $sms_data); goto ls7Pk; o_T8D: $this->showMessage($_GPC['kami'] . ' 已使用', '', 'error'); goto C7T10; jGwCi: $_share['content'] = $activity['description']; goto k3AiV; dgDnp: foreach ($staff as $s) { goto BAEQV; mCpCq: $this->send_template_message(urldecode(json_encode($template))); goto BnLhx; BAEQV: $template = array("touser" => $s['openid'], "template_id" => $activity['k_templateid'], "url" => $_W['siteroot'] . 'app/' . $this->createMobileUrl('manageform', array("name" => "dayu_form", "weid" => $row['weid'], "id" => $row['reid'])), "topcolor" => "#FF0000", "data" => array("first" => array("value" => urlencode($activity['kfirst'] . '\\n'), "color" => "#743A3A"), "keyword1" => array("value" => urlencode($member), "color" => "#000000"), "keyword2" => array("value" => urlencode($_GPC['mobile']), "color" => "#000000"), "keyword3" => array("value" => urlencode($ytime), "color" => "#000000"), "keyword4" => array("value" => urlencode($bodym . $voice . $kamiinfo . '\\n'), "color" => "#FF0000"), "remark" => array("value" => urlencode($activity['kfoot']), "color" => "#008000"))); goto mCpCq; BnLhx: ooHVZ: goto qsR7n; qsR7n: } goto H8lXE; D3y9j: ihttp_email($activity['noticeemail'], $activity['title'] . '的表单提醒', '<h4>姓名：' . $member . '</h4><h4>手机：' . $_GPC['mobile'] . '</h4>' . $body . $voice . $kamiinfo); goto onf0G; f6wT2: setcookie('r_submit', $_GPC['repeat']); goto JhcTp; srjaI: goto ckPGX; goto oyW6J; JrnRy: $sendkami = pdo_get('dayu_sendkami', array("weid" => $weid, "cid" => $par['sendkami'], "status" => "0"), array("id", "number", "password")); goto UMJud; hcm1k: $ycredit = $credits[$behavior['activity']] + $activity['credit']; goto Tdyce; CPR2N: $kamiinfo = !empty($row['kid']) ? '\\n　卡号：' . $kami['number'] : ''; goto Jw6r0; lHZil: mc_notice_credit1($openid, $uid, $activity['credit'], $log); goto kGWTU; J3muz: JGFyW: goto KAXfu; wYmLX: $uid && mc_update($uid, $update); goto tSHBO; iQJ8R: if (!(intval($par['daynum']) != 0)) { goto OrKY9; } goto DaAC_; oyW6J: x_dFp: goto AvDxN; S60GK: $template = array("touser" => $row['openid'], "template_id" => $activity['k_templateid'], "url" => murl('entry', array("do" => "mydayu_form", "op" => "detail", "id" => $row['reid'], "rerid" => $rerid, "m" => "dayu_form"), true, true), "topcolor" => "#FF0000", "data" => array("first" => array("value" => urlencode($activity['mfirst']), "color" => "#743A3A"), "keyword1" => array("value" => urlencode($member), "color" => "#000000"), "keyword2" => array("value" => urlencode($row['mobile']), "color" => "#000000"), "keyword3" => array("value" => urlencode($ytime), "color" => "#000000"), "keyword4" => array("value" => urlencode($kamiinfo), "color" => "#FF0000"), "remark" => array("value" => urlencode($activity['mfoot']), "color" => "#008000"))); goto sHnLN; O9Khg: if (!(pdo_insert('dayu_form_info', $row) != 1)) { goto rGZJa; } goto Gz83z; yXPSL: sfuVE: goto LNHts; etyA6: goto c5UI7; goto kMXKf; tSHBO: Eap4Q: goto VaRCX; TuIPP: $mname = !empty($par['mname']) ? $par['mname'] : '往期记录'; goto EaXzW; w6ZwJ: c5UI7: goto egX_6; VaRCX: if (!empty($datas)) { goto UAs2b; } goto Wsnq0; sHnLN: $this->send_template_message(urldecode(json_encode($template))); goto kCt42; C7T10: exit; goto RIFWI; SounT: $params[':reid'] = $reid; goto P9Gd5; H9aHo: foreach ($_FILES as $key => $file) { goto JhmdV; jUEdZ: $error = $upfile['error']; goto BPoZD; RiH0i: $type = $upfile['type']; goto iBew5; GUEeC: @mkdirs($upload_path); goto imVj2; iU8fe: $desfile = $upload_path . $target_filename; goto vGW5_; VEZDn: imagejpeg($image, $srcfile); goto r3lgo; D6PkB: $upfilesize = !empty($activity['filesize']) ? $activity['filesize'] : 12; goto a4UfQ; guSNa: $entry['data'] = $upload_path . $target_filename; goto L1mSH; FTtCa: IBICH: goto E0l05; j9X00: IsOd3: goto edXwm; B25el: if (is_array($ret)) { goto VtmYi; } goto guSNa; Usnk6: RuH76: goto udcI3; aXLFZ: $tmp_name = $upfile['tmp_name']; goto jUEdZ; eGRuM: message('移动文件失败，请检查服务器权限', referer(), 'error'); goto qkNPo; t8_VB: mkdir($upload_path); goto j181g; BPoZD: $upload_path = ATTACHMENT_ROOT . '/dayu_form/' . $weid . '/'; goto AMUZd; L1mSH: VtmYi: goto K_8r0; tmZOe: $target_filename = 'form' . $reid . '_' . date('YmdHis') . mt_rand(10, 99) . '.thumb.jpg'; goto BlkMR; gogJs: if (!($refid && $field && $file['name'] && $field['type'] == 'image')) { goto IBICH; } goto VUDsR; PCi8s: $entry['rerid'] = 0; goto u_GIu; fu3WK: if (!($maxfilesize > 0)) { goto dwc_f; } goto DoKO3; eVK7y: $content = date('Y-m-d H:i:s', TIMESTAMP); goto dr9eg; v9LE0: $entry = array(); goto gbKd5; edXwm: dwc_f: goto iVioT; iVioT: $uptypes = array("image/jpg", "image/png", "image/jpeg"); goto Dy07I; cAgpz: unlink($srcfile); goto Usnk6; euZCR: eGsgq: goto lwZv2; gbKd5: $entry['reid'] = $reid; goto PCi8s; vGW5_: $imginfo = getimagesize($srcfile); goto Nz6z1; LdltK: goto z4WJS; goto w75d6; DoKO3: if (!($size > $maxfilesize * 1024 * 1024)) { goto IsOd3; } goto X3L6C; imVj2: if (intval($error) > 0) { goto f9kVV; } goto D6PkB; a4UfQ: $maxfilesize = $upfilesize; goto fu3WK; j181g: NJocK: goto RzBKt; RzBKt: $source_filename = 'form' . $reid . '_' . date('YmdHis') . mt_rand(10, 99) . '.jpg'; goto tmZOe; UlSbw: $refid = intval(str_replace('field_', '', $key)); goto MpaAB; w75d6: f9kVV: goto TfAo5; K_8r0: $datas[] = $entry; goto LdltK; M8bdN: message('上传文件类型不符：' . $type, referer(), 'error'); goto euZCR; qkNPo: p1XXF: goto Abnqx; E0l05: txNRH: goto cAgpz; Abnqx: $srcfile = $upload_path . $source_filename; goto iU8fe; tsAML: $ret = file_image_thumb($srcfile, $desfile, $avatarsize); goto v9LE0; BlkMR: if (move_uploaded_file($tmp_name, $upload_path . $source_filename)) { goto p1XXF; } goto eGRuM; yVNIR: $name = $upfile['name']; goto RiH0i; VUDsR: $upfile = $file; goto yVNIR; u_GIu: $entry['refid'] = $refid; goto B25el; X3L6C: message('上传文件过大' . $_FILES['file']['error'], referer(), 'error'); goto j9X00; Dy07I: if (in_array($type, $uptypes)) { goto eGsgq; } goto M8bdN; Ck9oM: $color = imagecolorallocatealpha($image, 255, 0, 0, 50); goto eVK7y; MpaAB: $field = $fields[$refid]; goto gogJs; AMUZd: load()->func('file'); goto GUEeC; iBew5: $size = $upfile['size']; goto aXLFZ; GTJRC: $image = $fun($srcfile); goto Ck9oM; Nz6z1: $imgtype = image_type_to_extension($imginfo[2], false); goto Cy55n; JhmdV: if (!strexists($key, 'field_')) { goto txNRH; } goto UlSbw; r3lgo: $avatarsize = !empty($activity['upsize']) ? $activity['upsize'] : 640; goto tsAML; Cy55n: $fun = 'imagecreatefrom' . $imgtype; goto GTJRC; IxBmT: z4WJS: goto FTtCa; dr9eg: imagestring($image, 5, 10, 10, $content, $color); goto VEZDn; TfAo5: message('上传错误：错误代码：' . $error, referer(), 'error'); goto IxBmT; lwZv2: if (file_exists($upload_path)) { goto NJocK; } goto t8_VB; udcI3: } goto MyTZF; naBm0: if ($kami['status'] == 1) { goto J8aL0; } goto bh_9p; e4kkG: c2EqS: goto fXmJa; DJF1N: EtjW9: goto iQJ8R; YUUra: if (!(pdo_tableexists('dayu_kami') && !empty($par['kami']))) { goto h7bqL; } goto faNco; zkl8k: if ($repeat == $_GPC['repeat']) { goto fw8o5; } goto wFGza; vOzme: include $this->template('skins/' . $activity['skins']); goto vuj65; wdLrG: $picker = 1; goto vOzme; yHxO_: $outlink = !empty($activity['outlink']) ? $activity['outlink'] : $this->createMobileUrl('mydayu_form', array("name" => "dayu_form", "weid" => $row['weid'], "id" => $row['reid'])); goto O0_3z; T17SW: $rerid = pdo_insertid(); goto ODqxR; arjCM: $ytime = date('Y-m-d H:i:s', TIMESTAMP); goto zzoGk; Ydb1O: hbzy2: goto IqBX9; hX8Jp: if (!($groupid != $activity['mbgroup'])) { goto HT1DO; } goto iQEUi; Jw6r0: h7bqL: goto hMPY_; V7c7K: $alldata = array(); goto NpsYv; GOFfh: if ($activity) { goto dfSAp; } goto XK5TP; WrerB: rtYyK: goto DwyGl; hWBps: ckPGX: goto t4meQ; lbFzl: $this->showMessage('您还不是会员,请先领取您的会员卡.', $to_card, 'info'); goto m0BlL; kLsq8: foreach ($_GPC['thumb'] as $thumb) { $th[] = tomedia($thumb); NnRGU: } goto WrerB; LqCKq: if (!($activity['status'] == 0)) { goto v0uUZ; } goto D_B8e; vaeiT: PNrC6: goto phEy9; CnJbD: if (!($_W['ispost'] || checksubmit('submit'))) { goto Dh9jA; } goto bC9m7; zLYoQ: $update['mobile'] = $_GPC['mobile']; goto J3muz; j5lCa: dssAv: goto LqCKq; qcvbR: E3j3h: goto UqHwO; IqBX9: yvSb7: goto L0_pU; wGTQt: $datas = $fields = $update = array(); goto ahzKi; He_0n: hR2d5: goto CqgSJ; jxgjL: if (!is_error($acc)) { goto A9jrs; } goto IuFh9; RIFWI: goto c5UI7; goto TiiTn; HivFO: if (!(intval($par['pretotal']) != 0)) { goto EtjW9; } goto u1nH3; DG_WL: $tomorrow = strtotime('tomorrow'); goto ULkYV; hMPY_: $fans['user'] = mc_fansinfo($openid, $acid, $weid); goto ENW09; a4Idx: $credits = mc_credit_fetch($_W['member']['uid'], '*'); goto hcm1k; R6Dsr: $state = $this->get_status($row['reid'], 1); goto V7c7K; ZEg14: $row['createtime'] = TIMESTAMP; goto wGTQt; phEy9: goto hbzy2; goto CIpsb; H8lXE: jfHVM: goto mfXaf; DaAC_: $today = strtotime('today'); goto DG_WL; fYn8Q: $this->showMessage('抱歉,每人只能提交' . intval($par['pretotal']) . '次！', '', 'info'); goto m_NN0; eUfiD: if (empty($kami)) { goto zRGZm; } goto naBm0; rz71h: $to_card = $par['card'] == 1 ? murl('entry', array("do" => "card", "m" => "we7_coupon", "returnurl" => $returnUrl), true, true) : murl('entry', array("do" => "index", "m" => "dayu_card", "returnurl" => $returnUrl), true, true); goto RdyQu; sdO6Z: $this->showMessage('卡号不存在', '', 'error'); goto BeiFw; wUbhJ: OrKY9: goto ZSGpU; sPEVe: $row['lng'] = $_GPC['getlng']; goto HpG7Z; mfXaf: w0aE9: goto Ydb1O; qXKOT: foreach ($_GPC as $key => $value) { goto Ro5xN; YOAPZ: if (!in_array($field['type'], array("checkbox"))) { goto zfLM6; } goto q3Yjo; eLK9O: tS8eA: goto MYrDz; Bn84R: OY0Bj: goto BuhVq; A7VEE: $entry['refid'] = $refid; goto ImZgY; RemjR: $refid = intval(str_replace('field_', '', $key)); goto M_xPN; zbQVs: goto OY0Bj; goto eLK9O; BuhVq: zfLM6: goto RXwVt; f3WR5: $update[$bindFiled] = $value; goto tePu7; UxPkD: $entry = array(); goto zbegU; ImZgY: $entry['displayorder'] = $field['displayorder']; goto UKdh4; IVz9Z: $entry['data'] = implode(',', $value); goto Bn84R; Cx64X: $entry['rerid'] = 0; goto A7VEE; q3Yjo: if ($field['loc'] == '-1') { goto tS8eA; } goto i2JUL; Ro5xN: if (!strexists($key, 'field_')) { goto HBGnD; } goto lKydw; xvZGo: r6w9T: goto ADzVN; Fqbev: Kpgqp: goto IVz9Z; zbegU: $entry['reid'] = $reid; goto Cx64X; qnmII: if (empty($bindFiled)) { goto HqY_8; } goto f3WR5; h5ax2: goto a0Aa2; goto Fqbev; lKydw: $bindFiled = substr(strrchr($key, '_'), 1); goto qnmII; sEIiG: $entry['data'] = trim($value); goto qWtrl; ADzVN: HBGnD: goto Sk5bX; UKdh4: if (!in_array($field['type'], array("number", "text", "calendar", "email", "radio", "textarea", "range", "select", "image", "reside", "photograph", "tingli", "phone", "tel", "idcard"))) { goto scTtU; } goto sEIiG; rC0Eu: if (!($refid && $field)) { goto r6w9T; } goto UxPkD; i2JUL: $entry['data'] = implode(',', $value); goto zbQVs; RXwVt: $datas[] = $entry; goto xvZGo; qWtrl: scTtU: goto YOAPZ; Sk5bX: a0Aa2: goto uDj8S; M_xPN: $field = $fields[$refid]; goto rC0Eu; MYrDz: if (is_array($value)) { goto Kpgqp; } goto h5ax2; tePu7: HqY_8: goto RemjR; uDj8S: } goto Mqft9; XFyH3: foreach ($datas as &$r) { goto ASuJI; b3P1Z: pdo_insert('dayu_form_data', $r); goto FDtTa; FDtTa: YppXU: goto LHHmi; ASuJI: $r['rerid'] = $rerid; goto b3P1Z; LHHmi: } goto fKAUh; T_ExX: if (!($par['card'] == 1 || $par['card'] == 2)) { goto KCnPg; } goto pyg30; uA8AU: $row['var2'] = $_GPC[$par['var2']]; goto cJVXT; q8i59: dfSAp: goto LUDhH; faNco: $kamidata = array("reid" => $reid, "infoid" => $rerid, "addons" => "dayu_form", "openid" => $openid, "name" => $row['member'], "mobile" => $row['mobile'], "status" => 1, "updatetime" => TIMESTAMP); goto NuE0T; NuE0T: pdo_update('dayu_kami', $kamidata, array("weid" => $weid, "id" => $row['kid'])); goto CPR2N; sxRy4: $acc = WeAccount::create($_W['acid']); goto fveNi; ls7Pk: UrT63: goto L6_ru; aLljw: foreach ($_GPC['reside'] as $key => $value) { goto y8kTL; y8kTL: $resideData = array("reid" => $reside['reid']); goto BEDQx; BEDQx: $resideData['rerid'] = 0; goto vRyrg; R2X7P: kK5na: goto WbXIb; Ln8tq: $resideData['data'] = $value; goto qOgM_; qOgM_: $datas[] = $resideData; goto R2X7P; vRyrg: $resideData['refid'] = $reside['refid']; goto Ln8tq; WbXIb: } goto ur04f; csGYS: $this->showMessage('保存失败。'); goto n3F3R; ylPnN: $qqkey = $set['qqkey']; goto zXQvj; rPOG2: L12N8: goto j5lCa; YOe4X: foreach ($ds as &$r) { goto jsdBd; wXU6B: V43Z3: goto qhyQO; Bxprd: Nrb3Y: goto pp7Gq; WxFkF: EQz0s: goto Or5Kw; wRT2i: e2VLM: goto e_I_y; LKAZS: if ($r['bind'] == 'email' && strexists($profile[$r['bind']], 'we7.cc')) { goto xtA0H; } goto mBIry; hc8QO: goto knZzq; goto wRT2i; YVVOA: $r['tixing'] = !empty($r['description']) ? urldecode($r['description']) : '请选择' . $r['title']; goto VVEC5; KyIuR: $isloc = $r; goto HwGuD; DvcQk: k3M2Z: goto x6Iyq; JXU4o: if (!$profile['gender']) { goto JkAfk; } goto OeeAO; VVEC5: knZzq: goto JXU4o; W3ZoU: AgoKf: goto nt2Yu; eLY9k: $binds[$r['type']] = $r['bind']; goto jvm3j; YanQC: if (!$r['bind']) { goto MxlVQ; } goto eLY9k; aYeUF: Nj32n: goto Ys6jG; f25LS: $activity['smsid'] && $r['bind'] == 'mobile' && empty($profile[$r['bind']]) && $this->showMessage('请完善手机号', murl('entry', array("do" => "index", "id" => $activity['smsid'], "m" => "dayu_sms", "form" => $_W['current_module']['name'], "returnurl" => $returnUrl), true, true), 'info'); goto oWjPc; wz3lO: scBKK: goto ZePuO; EhTbN: goto k3M2Z; goto Iw68f; mM4zg: jPzXE: goto qRqLO; B8CMa: $r['default'] = ''; goto DvcQk; UfoFy: $r['options'] = explode(',', $r['value']); goto mz5xI; qhyQO: if (!(!empty($r['loc']) && pdo_tableexists('dayu_form_plugin_radio') && ($r['type'] == 'radio' || $r['type'] == 'checkbox'))) { goto Nj32n; } goto flQp1; LosD1: $profile['gender'] = '女'; goto rSgN7; Hssjd: $initRange = true; goto Uw7wl; jvm3j: MxlVQ: goto uZGZD; pgyn6: JxcgQ: goto YVVOA; U0yAh: goto knZzq; goto pgyn6; uaV1R: $profile['gender'] = '男'; goto wz3lO; Iw68f: xtA0H: goto B8CMa; rSgN7: sUCIb: goto NVvcG; mBIry: $r['default'] = $profile[$r['bind']]; goto EhTbN; bvT5M: pdo_tableexists('dayu_photograph_fields') && ($r['photograph_url'] = murl('entry', array("do" => "index", "f" => $r['bind'], "m" => "dayu_photograph", "returnurl" => $returnUrl), true, true)); goto W3ZoU; cFJXd: $initCalendar = true; goto mM4zg; qRqLO: if (!$r['value']) { goto TqBij; } goto UfoFy; x6Iyq: glBqv: goto f25LS; mz5xI: TqBij: goto YanQC; Uw7wl: AgHL9: goto vByN1; HwGuD: Dqtg_: goto Tf5_4; ZePuO: if (!($profile['gender'] == '2')) { goto sUCIb; } goto LosD1; Y5DKH: $r['image'] = !empty($r['image']) ? $r['image'] : TEMPLATE_WEUI . 'images/nopic.jpg'; goto wXU6B; pp7Gq: if (!($r['type'] == 'text' && $r['loc'] > 0)) { goto Dqtg_; } goto KyIuR; ylhRr: if (in_array($r['type'], array("radio", "checkbox"))) { goto JxcgQ; } goto hc8QO; flQp1: $r['dayu_radio'] = pdo_fetchall('SELECT * FROM ' . tablename('dayu_form_plugin_radio') . ' WHERE weid = :weid and cid = :cid and status=1 ORDER BY id DESC', array(":weid" => $weid, ":cid" => $r['loc'])); goto aYeUF; XBgZC: $reside = $r; goto Bxprd; Or5Kw: if (!($profile['gender'] == '1')) { goto scBKK; } goto uaV1R; OeeAO: if (!($profile['gender'] == '0')) { goto EQz0s; } goto a0wuV; uZGZD: if (!($r['type'] == 'reside')) { goto Nrb3Y; } goto XBgZC; NVvcG: JkAfk: goto qrgH0; e_I_y: $r['tixing'] = !empty($r['description']) ? urldecode($r['description']) : '请填写' . $r['title']; goto U0yAh; jsdBd: if (!($r['type'] == 'range')) { goto AgHL9; } goto Hssjd; oWjPc: $r['type'] == 'photograph' && empty($profile[$r['bind']]) && $this->showMessage('请完善' . $r['title'], murl('entry', array("do" => "index", "f" => $r['bind'], "m" => "dayu_photograph", "returnurl" => $returnUrl), true, true), 'info'); goto bvT5M; vByN1: if (!($r['type'] == 'calendar')) { goto jPzXE; } goto cFJXd; a0wuV: $profile['gender'] = '保密'; goto WxFkF; qrgH0: if (!$profile[$r['bind']]) { goto glBqv; } goto LKAZS; Ys6jG: if (in_array($r['type'], array("text", "number", "email", "textarea", "idcard", "phone", "tel"))) { goto e2VLM; } goto ylhRr; Tf5_4: if (!($r['type'] == 'image')) { goto V43Z3; } goto Y5DKH; nt2Yu: } goto pwCNX; yDLED: $params = array(); goto H9zct; eQKUJ: if (!($lognum >= intval($par['daynum']))) { goto kHx6c; } goto GxeNm; O0_3z: $this->showMessage($activity['information'], $outlink); goto z9Njk; p81Lx: $settings = uni_setting($_W['uniacid'], array("creditnames", "creditbehaviors", "uc", "payment", "passport")); goto f0P4n; TiiTn: jkRWy: goto tbo_k; Iw7hS: T3d94: goto VAkJp; gPywp: $this->showMessage($activity['information'], $this->createMobileUrl('mydayu_form', array("id" => $reid))); goto zgKQE; GxeNm: $this->showMessage('抱歉,每天只能提交' . intval($par['daynum']) . '次！', $this->createMobileUrl('mydayu_form', array("name" => "dayu_form", "weid" => $weid, "id" => $reid)), 'info'); goto OS6Gf; J3m9z: if (!(pdo_tableexists('dayu_kami') && !empty($par['kami']))) { goto T3d94; } goto Ic34E; m_NN0: WZjgg: goto DJF1N; fXmJa: if (empty($_GPC['reside'])) { goto v4a2M; } goto EIjvc; EH2Vn: if (!(intval($par['allnum']) != 0)) { goto gnvKx; } goto xNHPt; LUDhH: if (!($activity['starttime'] > TIMESTAMP)) { goto HNt80; } goto kdLpT; ZGNBK: $dayu_check = pdo_get('dayu_check', array("weid" => $weid, "openid" => $openid, "cid" => $dayu_check_category['id'], "status" => 1), array("id")); goto u0nyc; k3AiV: $_share['imgUrl'] = tomedia($activity['thumb']); goto wdLrG; uH71z: mc_group_update($uid); goto BQ2eb; UqHwO: $group = pdo_fetch('SELECT * FROM ' . tablename('mc_members') . " WHERE uniacid = '{$weid}' AND uid = '{$uid}'"); goto uhDYl; RdyQu: if (!($ishy == false)) { goto suO2R; } goto lbFzl; AH5ab: $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `reid`=:reid'; goto tPuPR; A6nP0: KujQI: goto EH2Vn; olmZY: if (!(!empty($activity['mobile']) && !empty($activity['smsnotice']))) { goto UrT63; } goto CEkNK; jvwM0: if (!(pdo_tableexists('dayu_check') && $par['dayu_check'])) { goto E3j3h; } goto f8blP; ULkYV: $lognum = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_info) . " WHERE openid = '{$openid}' AND reid = '{$reid}' AND createtime > " . $today . ' AND createtime < ' . $tomorrow); goto eQKUJ; P9Gd5: $activity = pdo_fetch($sql, $params); goto Lzl0n; n7zmZ: foreach ($ds as $value) { $fields[$value['refid']] = $value; ZwqFM: } goto r45ko; bxjV2: $yuyuetime = date('Y-m-d H:i', time() + 3600); goto T_ExX; UMJud: $row['kid'] = $sendkami['id']; goto VdwQo; SUgLD: GMThw: goto IX6rG; hcNDz: if (!($pretotal >= intval($par['pretotal']))) { goto WZjgg; } goto fYn8Q; Ykh5d: $record = array(); goto yMy9r; M1Cjv: $title = $par['title'] ? $par['title'] : $activity['title']; goto r6P5a; tbo_k: $this->showMessage($kami['password'] . '卡号与密码不匹配', '', 'error'); goto qkQU9; YafqA: ihttp_post(murl('entry', array("do" => "print", "printid" => $par['print'], "m" => "dayu_print"), true, true), array("title" => $activity['title'], "realname" => $member, "mobile" => $_GPC['mobile'], "info" => $bodyp, "createtime" => $row['createtime'])); goto okX85; xNHPt: $allnum = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_info) . ' WHERE reid = :reid and (status=0 or status=1 or status=3)', array(":reid" => $reid)); goto hW15A; r6P5a: $_share['title'] = $title; goto jGwCi; RF9D6: $row['voice'] = !empty($_GPC['voice']) ? $setting['qiniu']['host'] . '/' . $_GPC['voice'] : ''; goto M7j21; CqgSJ: gnvKx: goto HivFO; VAkJp: if (!(pdo_tableexists('dayu_sendkami') && !empty($par['sendkami']))) { goto V27Or; } goto JrnRy; IX6rG: $row['reid'] = $reid; goto AEZAb; bC9m7: $row = array(); goto J3m9z; L0_pU: if (!($activity['credit'] != '0.00' && $par['icredit'] == '0')) { goto AMZFF; } goto XWuY1; CBMK4: !empty($activity['linkage']) && ($la = iunserializer($activity['linkage'])); goto iTToF; VdwQo: V27Or: goto Ou4vR; VkUrj: v0uUZ: goto GOFfh; wFGza: setcookie('r_submit', $_GPC['repeat']); goto Byxk2; y_G0v: KCnPg: goto jvwM0; AEZAb: $row['member'] = $_GPC['member']; goto KUTdT; xNyXv: $row['getadd'] = $_GPC['getadd']; goto PcfDg; NpsYv: $formdata = $this->order_foreach($row['reid'], $rerid); goto mVDAW; mBqFN: UAs2b: goto O9Khg; wbjWs: HNt80: goto yv1Yd; OFY9j: $update['residecity'] = $_GPC['reside']['city']; goto Ru5Fz; Wsnq0: $this->showMessage('非法访问，提交数据不能为空', '', 'error'); goto mBqFN; EIjvc: if (!in_array('reside', $binds)) { goto YN3gP; } goto Rol4n; BReBm: dOo2Y: goto o9ppK; Q08YG: YN3gP: goto aLljw; fKAUh: b0DKx: goto FWVr9; luZ5j: $kamiinfo = !empty($row['kid']) ? '\\n　卡号：' . $sendkami['number'] . '\\n　密码：' . $sendkami['password'] : ''; goto S60GK; t4meQ: $params = array(); goto HCFHv; f8blP: $dayu_check_category = pdo_get('dayu_check_category', array("weid" => $weid, "id" => $par['dayu_check']), array("id", "reminder")); goto ZGNBK; u1nH3: $pretotal = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_info) . ' WHERE reid = :reid AND openid = :openid', array(":reid" => $reid, ":openid" => $openid)); goto hcNDz; u0nyc: if ($dayu_check) { goto I7ac4; } goto U8gLC; U8gLC: $check_url = murl('entry', array("do" => "index", "id" => $par['dayu_check'], "reid" => $reid, "formdo" => $_GPC['do'], "form" => $_W['current_module']['name'], "m" => "dayu_check", "returnurl" => $returnUrl), true, true); goto LflYq; OS6Gf: kHx6c: goto wUbhJ; cJVXT: $row['var3'] = $_GPC[$par['var3']]; goto xNyXv; BESVD: $creditnames = $settings['creditnames']; goto a4Idx; iQEUi: $this->showMessage('您所在会员组没有相关的操作权限！', '', 'info'); goto ghQxu; RfrfH: foreach ($datas as $d) { goto GtMxN; T3s0n: $smsbody .= $fields[$d['refid']]['title'] . ':' . $d['data'] . '，'; goto SXw6F; IBedJ: $bodym .= '\\n　' . $fields[$d['refid']]['title'] . ':' . $d['data']; goto MvdnC; wRfGb: $d['data'] = '有'; goto cPOcY; GtMxN: if (!strstr($d['data'], 'images')) { goto h4pQn; } goto wRfGb; cPOcY: h4pQn: goto IBedJ; MvdnC: $body .= '<h4>' . $fields[$d['refid']]['title'] . '：' . $d['data'] . '</h4>'; goto olNPq; olNPq: $bodyp .= $fields[$d['refid']]['title'] . '：' . $d['data'] . '|'; goto T3s0n; SXw6F: nit1t: goto A0tXn; A0tXn: } goto yXPSL; ljZ5Q: $this->showMessage('活动已经结束！<br>截至时间：' . date('Y-m-d H:i:s', $activity['endtime'])); goto LueO1; yMy9r: $record['starttime'] = TIMESTAMP; goto SGpZF; pyg30: $ishy = $this->isHy($openid); goto rz71h; zzoGk: $voice = !empty($_GPC['voice']) ? '\\n　有' . $activity['voice'] : ''; goto Q9r1A; Rol4n: $update['resideprovince'] = $_GPC['reside']['province']; goto OFY9j; KoNSs: if (!($activity['paixu'] != '2')) { goto JGFyW; } goto D9jU3; LNHts: if (!(pdo_tableexists('dayu_print') && !empty($par['print']))) { goto FoOsT; } goto YafqA; BeiFw: exit; goto HXeFK; bv_Fx: if (empty($_GPC['repeat'])) { goto dssAv; } goto tB4NX; MEeRt: $btncolor = $this->get_color($reid, $par['btncolor']); goto pq8Y_; fysmu: if ($par['isrand'] == 1 && !empty($par['randnum'])) { goto x_dFp; } goto H67h0; o677x: require MODULE_ROOT . '/fans.mobile.php'; goto D09W1; ouROi: if (!($activity['mbgroup'] != 0)) { goto L2EXL; } goto hX8Jp; SvmXB: if (!is_array($_GPC['thumb'])) { goto TP_x9; } goto kLsq8; L6_ru: $staff = pdo_fetchall('SELECT `openid` FROM ' . tablename($this->tb_staff) . ' WHERE reid=:reid AND weid=:weid', array(":weid" => $_W['uniacid'], ":reid" => $row['reid'])); goto Ga8E1; Tdyce: $activity['thumb'] = tomedia($activity['thumb']); goto rD6AG; Q9r1A: if (!(pdo_tableexists('dayu_sendkami') && !empty($par['sendkami']))) { goto ohpdk; } goto uTxTB; c3Vrl: load()->func('tpl'); goto QCvPe; Byxk2: goto BD75M; goto n1tuc; D_B8e: $this->showMessage('活动已经停止.'); goto VkUrj; kMXKf: zRGZm: goto sdO6Z; zsnDI: if (!is_array($staff)) { goto w0aE9; } goto dgDnp; eCyYh: H58MT: goto vaeiT; dUDuy: A9jrs: goto p81Lx; xsMkN: $sendkami = pdo_get('dayu_sendkami', array("weid" => $weid, "cid" => $par['sendkami'], "id" => $row['kid']), array("id", "number", "password")); goto luZ5j; nwn8u: $reid = intval($_GPC['id']); goto AH5ab; DwyGl: $row['thumb'] = iserializer($th); goto mdwXk; IldGH: P_K_l: goto ylPnN; BNaDc: $acc = notice_init(); goto jxgjL; vuj65: } public function doMobileConsult() { goto yxqP_; fJooG: if ($check['status'] == '3') { goto yM48q; } goto ry3mI; y5TAv: goto sOZ_o; goto PQMOy; WfB6k: $cid = intval($_GPC['cid']); goto j_Bqg; bZRIW: $this->showMessage('已完成，关闭咨询.', referer(), 'error'); goto oYBTX; EBhBc: IRJWq: goto yoJ90; ZzwAf: $title = '在线咨询'; goto zfJJE; cVSx7: sHN7p: goto v2_DB; p1WMQ: FToYT: goto YFfCW; JUcPb: $check = pdo_get($this->tb_info, array("rerid" => $rerid), array()); goto fJooG; WwDpT: $this->showMessage('系统错误.', referer(), 'error'); goto DScKP; VWEIt: $rerid = intval($_GPC['rerid']); goto WfB6k; YFfCW: $staff = '1'; goto pekbx; pekbx: $back_url = $this->createMobileUrl('manageform', array("op" => "detail", "rerid" => $rerid, "id" => $reid)); goto CJt79; s7seO: $back_url = $this->createMobileUrl('mydayu_form', array("op" => "detail", "rerid" => $rerid, "id" => $reid)); goto ZzwAf; reuNg: if (!(empty($cid) || empty($reid) || empty($rerid) || empty($weid) || empty($openid))) { goto Z6i3d; } goto WwDpT; PQMOy: yM48q: goto bZRIW; yxqP_: global $_W, $_GPC; goto LlYp3; Q_OPH: $staff = '0'; goto s7seO; yoJ90: include $this->template('consult'); goto wHNcs; DScKP: Z6i3d: goto JUcPb; YZ3gl: $reid = intval($_GPC['reid']); goto VWEIt; ry3mI: if ($check['openid'] != $openid && !$isstaff) { goto sHN7p; } goto y5TAv; zfJJE: goto IRJWq; goto p1WMQ; oYBTX: goto sOZ_o; goto cVSx7; LlYp3: require MODULE_ROOT . '/fans.mobile.php'; goto YZ3gl; va1xS: if ($form == '1' && $isstaff) { goto FToYT; } goto Q_OPH; j_Bqg: $form = intval($_GPC['form']); goto Jv5EB; CJt79: $title = '在线咨询管理'; goto EBhBc; v2_DB: $this->showMessage('非法访问.', referer(), 'error'); goto uvBm3; Jv5EB: $isstaff = $this->get_staff($openid, $reid); goto reuNg; uvBm3: sOZ_o: goto va1xS; wHNcs: } public function get_staff($openid, $reid) { global $_GPC, $_W; return pdo_get($this->tb_staff, array("weid" => $_W['uniacid'], "openid" => $openid, "reid" => $reid), array()); } public function doMobileUploads() { goto txqGg; jmM4H: if (!($type == 'image')) { goto sL31z; } goto MZbIA; YCIvu: $result['localId'] = $localId; goto YvEHG; Oc4ai: $result['path'] = $filename; goto Ihfvq; txqGg: global $_W, $_GPC; goto Bu1cX; Ad_SB: goto d_p_8; goto g3VWh; Bu1cX: load()->classs('account'); goto nj9gU; MZbIA: $serverId = trim($_GPC['serverId']); goto SWts6; WSFUM: $acid = $_W['acid']; goto iWeSw; vRYuZ: $result['status'] = 'success'; goto G_QFZ; kXzBt: d_p_8: goto CI3Hc; WR7Db: $type = !empty($_GPC['type']) ? $_GPC['type'] : 'image'; goto NQhHl; irN5q: exit(json_encode(array("status" => true))); goto kXzBt; uA5cz: GvReF: goto INBjH; SWts6: $localId = trim($_GPC['localId']); goto jZUSm; nSoTU: $result['imgurl'] = $_W['attachurl'] . $filename; goto Oc4ai; iWeSw: $acc = WeAccount::create($acid); goto OxdwR; htI0t: $params = array(":openid" => $_W['openid'], ":uniacid" => $_W['uniacid']); goto EGGLl; EGGLl: $_W['acid'] = pdo_fetchcolumn($sql, $params); goto Raua7; qVwUE: if (!empty($_W['acid'])) { goto k4Jcd; } goto U52Cc; yAy4D: file_delete($file); goto irN5q; jZUSm: $media = array(); goto cjHeI; WyWIL: $media['type'] = $type; goto uPoQg; g3VWh: AnLw8: goto cVUEQ; LjqcW: $sql = 'SELECT acid FROM ' . tablename('mc_mapping_fans') . ' WHERE openid = :openid AND uniacid = :uniacid limit 1'; goto htI0t; wVPVp: if (!empty($_W['acid'])) { goto ICvh8; } goto LjqcW; OxdwR: $operation = !empty($_GPC['op']) ? $_GPC['op'] : 'upload'; goto WR7Db; oq3Dd: $result['message'] = '上传失败'; goto uA5cz; cVUEQ: $file = $_GPC['file']; goto yAy4D; YvEHG: $filename = $acc->downloadMedia($media, true); goto CkrcC; CkrcC: if (is_error($filename)) { goto jfIBU; } goto vRYuZ; q_de3: goto d_p_8; goto EUlwz; U52Cc: $result['status'] = 'error'; goto e8YtW; EUlwz: ImQWp: goto jmM4H; cjHeI: $media['media_id'] = $serverId; goto WyWIL; e8YtW: $result['message'] = '没有找到相关公众账号'; goto srfDo; NQhHl: if ($operation == 'upload') { goto ImQWp; } goto MWJ_O; MWJ_O: if ($operation == 'remove') { goto AnLw8; } goto q_de3; srfDo: k4Jcd: goto WSFUM; SctYQ: jfIBU: goto xH7jp; nj9gU: $result = array("status" => "error", "message" => "123", "data" => ""); goto wVPVp; Ihfvq: goto GvReF; goto SctYQ; IpYOa: die(json_encode($result)); goto Ad_SB; G_QFZ: $result['message'] = '上传成功'; goto nSoTU; Raua7: ICvh8: goto qVwUE; xH7jp: $result['status'] = 'error'; goto oq3Dd; INBjH: sL31z: goto IpYOa; uPoQg: $result['serverId'] = $serverId; goto YCIvu; CI3Hc: } public function doMobileUpload() { goto qH3s2; fzjj6: unlink($upload_path . $pathname); goto oRbue; HU6KW: mc_update($_W['member']['uid'], $data); goto Swiku; eJTR5: message('远程附件上传失败，请检查配置并重新上传'); goto o2Tno; WP1iR: load()->func('file'); goto ny2fl; s9YvW: $pathname = $images_path . $pic; goto RiC8W; wikNR: $picurl = $upload_path . $images_path . $pic; goto UaWIs; sj4gB: $params[':reid'] = $reid; goto yhaOO; CNRmq: $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `reid`=:reid'; goto toeka; Swiku: goto gOwbg; goto Weobn; snjH0: $targetName = $picurl; goto gSbec; Jtfjf: load()->classs('weixin.account'); goto s_4aQ; qH3s2: global $_W, $_GPC; goto Jtfjf; FwBPn: load()->func('tpl'); goto Q_cVG; UVA3I: if (file_exists($upload_path . $images_path)) { goto D2oZF; } goto Dy75G; RiC8W: if (empty($_W['setting']['remote']['type'])) { goto JAW13; } goto WP1iR; toeka: $params = array(); goto hsb3n; Q_cVG: $reid = intval($_GPC['id']); goto CNRmq; kD273: $data = array("avatar" => $images_path . $pic); goto waaH2; rSbhX: $picurl = $upload_path . $images_path . $pic; goto kD273; ny2fl: $remotestatus = file_remote_upload($pathname); goto xjGWa; Qmz_u: $access_token = $accObj->fetch_token(); goto qCeW1; P2k8a: if ($reid) { goto xvG3K; } goto rM_NC; Weobn: xvG3K: goto Iz_zF; gSbec: $ch = curl_init($url); goto WXo8K; bi8LY: curl_setopt($ch, CURLOPT_HEADER, 0); goto EgBog; FoSKx: curl_close($ch); goto kv0_9; rM_NC: goto gOwbg; goto N0X0l; qg2y9: @mkdirs($upload_path . $images_path); goto UVA3I; U5uo4: $pic = 'avatar_' . date('YmdHis') . mt_rand(1000, 9999) . '.jpg'; goto rSbhX; vRAvQ: curl_setopt($ch, CURLOPT_FILE, $fp); goto bi8LY; OX__Y: D2oZF: goto kp3n1; qCeW1: $media_id = $_GET['media_id']; goto UdPnV; N0X0l: wvlZu: goto U5uo4; oRbue: goto nMrpO; goto Qk3AM; KWi5b: load()->func('file'); goto qg2y9; aZ5pM: $upload_path = ATTACHMENT_ROOT; goto paYS4; o2Tno: nMrpO: goto hJML_; Qk3AM: gBXwy: goto eJTR5; kp3n1: if ($_GPC['type'] == 2) { goto wvlZu; } goto P2k8a; kv0_9: fclose($fp); goto s9YvW; WXo8K: $fp = fopen($targetName, 'wb'); goto vRAvQ; UaWIs: gOwbg: goto snjH0; UdPnV: $url = 'http://file.api.weixin.qq.com/cgi-bin/media/get?access_token=' . $access_token . '&media_id=' . $media_id; goto FwBPn; Iz_zF: $pic = 'form' . $reid . '_' . date('YmdHis') . mt_rand(1000, 9999) . '.jpg'; goto wikNR; paYS4: $images_path = 'dayu_form/' . $_W['uniacid'] . '/'; goto KWi5b; WTNvw: $remoteurl = $pathname; goto fzjj6; hJML_: JAW13: goto irbN_; Dy75G: mkdir($upload_path . $images_path); goto OX__Y; irbN_: echo $pathname; goto kCp32; yhaOO: $activity = pdo_fetch($sql, $params); goto aZ5pM; EgBog: curl_exec($ch); goto FoSKx; hsb3n: $params[':weid'] = $weid; goto sj4gB; xjGWa: if (is_error($remotestatus)) { goto gBXwy; } goto WTNvw; s_4aQ: $accObj = WeixinAccount::create($_W['uniacid']); goto Qmz_u; waaH2: load()->model('mc'); goto HU6KW; kCp32: } public function download_voice($media_id, $retry = 0) { goto VrSUp; VrSUp: global $_W, $_GPC; goto lyxdA; JoSKG: load()->func('communication'); goto NuUPf; kx5Nf: Bf5bf: goto H5kTM; BmHU1: mdMgc: goto Ms3Cm; wq0r4: EqE90: goto TWao0; qKa_q: $this->download_voice($media_id, 1); goto wq0r4; WLSWr: $access_token = WeAccount::token(); goto JoSKG; NuUPf: $voice = ihttp_get('http://file.api.weixin.qq.com/cgi-bin/media/get?access_token=' . $access_token . '&media_id=' . $media_id); goto oTsAc; H5kTM: qhWmK: goto yIyqS; TWao0: return false; goto iDVB1; iDVB1: goto Bf5bf; goto BmHU1; Ms3Cm: return $voice['content']; goto kx5Nf; oTsAc: if (isset($voice['headers']['Content-disposition'])) { goto mdMgc; } goto XENHw; XENHw: if (!($retry === 0)) { goto EqE90; } goto qKa_q; lyxdA: if (!$media_id) { goto qhWmK; } goto WLSWr; yIyqS: } public function upload_qiniu_voice($filename, $content) { goto a3GqT; dd8sa: WWe7W: goto ZSOAO; YzPd2: if (isset($r['persistentId']) && !empty($r['persistentId'])) { goto CJ7_Z; } goto z9R3P; jgH98: wjx0X: goto NoO2u; vqr0v: jL6Ad: goto t7q1i; hnTjU: $setting = $this->module['config']; goto NURNa; z9R3P: return ''; goto QQlfn; YDzKK: $qiniu = new Qiniu($setting['qiniu']); goto bxBIC; t7q1i: goto WWe7W; goto jgH98; QQlfn: goto jL6Ad; goto FgaDf; NoO2u: return ''; goto dd8sa; oxWIU: return $r['persistentId']; goto vqr0v; bxBIC: $pipeline = $setting['qiniu']['pipeline']; goto MZV1i; ZSOAO: aC_Tw: goto dFzMY; MZV1i: $r = $qiniu->putContent($filename, $content, $pipeline); goto nrXkq; nrXkq: if ($r === false) { goto wjx0X; } goto YzPd2; NURNa: if (empty($setting['qiniu'])) { goto aC_Tw; } goto YDzKK; FgaDf: CJ7_Z: goto oxWIU; a3GqT: require MODULE_ROOT . '/Qiniu.class.php'; goto hnTjU; dFzMY: } public function doMobileUploadvoice() { goto ZXLMb; ZXLMb: global $_W, $_GPC; goto dpXCb; Thncu: beZlV: goto Dkry0; DTyQK: $this->showMessage('serverId为空'); goto Thncu; gjrOq: if (!$content) { goto LggXo; } goto gxdpB; gxdpB: $filename = 'dayu_form_' . $_W['uniacid'] . '_' . $_GPC['serverId'] . '.mp3'; goto rsElS; T9E50: $content = $this->download_voice($_GPC['serverId'], ''); goto gjrOq; rsElS: $r = $this->upload_qiniu_voice($filename, $content); goto Nddb8; dpXCb: if (!empty($_GPC['serverId'])) { goto beZlV; } goto DTyQK; Nddb8: LggXo: goto pbCDj; Dkry0: $setting = $this->module['config']; goto T9E50; pbCDj: } public function doMobileGetprefop() { goto u2XHo; WRi6L: vKYKP: goto l1vrJ; QcFxd: echo '1'; goto H3qvv; yidyX: if (!($r['code'] == 0)) { goto bsJE8; } goto QcFxd; l1vrJ: jA_1z: goto JOGsm; u2XHo: global $_W, $_GPC; goto bS2JH; bS2JH: if (!$_GPC['persistentId']) { goto jA_1z; } goto U06u0; KEj60: $r = json_decode($r, true); goto Vgh1D; Vgh1D: if (!isset($r['code'])) { goto vKYKP; } goto yidyX; U06u0: $r = file_get_contents('http://api.qiniu.com/status/get/prefop?id=' . $_GPC['persistentId']); goto KEj60; H3qvv: bsJE8: goto WRi6L; JOGsm: } public function doMobiletest() { global $_W, $_GPC; include $this->template('test'); } public function doMobileUploadVideo() { goto hrvP1; Me__x: $filedata = array("media" => '@' . $filename); goto vvh8h; vj531: $access_token = WeAccount::token(); goto C_AiA; K1w1N: var_dump($result); goto meLsR; BsDTY: $filename = 'bbb.mp4'; goto Me__x; C_AiA: $url = 'https://api.weixin.qq.com/cgi-bin/media/upload?type=video&access_token=' . $access_token; goto BsDTY; vvh8h: $result = ihttp_request($url, json_encode($filedata)); goto K1w1N; hrvP1: global $_W, $_GPC; goto GuIPX; meLsR: die; goto Ceied; GuIPX: load()->func('communication'); goto vj531; Ceied: } public function curl_post($sucai, $img) { goto M7nmI; M7nmI: $ch = curl_init(); goto cklGZ; GkGSe: goto At3EI; goto p8Chj; EnQ48: At3EI: goto NsRHZ; FgG84: curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE); goto symFC; LtESZ: return $output; goto Yw6fy; qxKn4: curl_setopt($ch, CURLOPT_SAFE_UPLOAD, true); goto ziE1l; ub0aR: curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1); goto FgG84; xfazL: curl_setopt($ch, CURLOPT_POSTFIELDS, $data); goto Ek0vk; symFC: curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE); goto ctQCL; OY3Ql: MlbYv: goto s5Nsk; ctQCL: curl_setopt($ch, CURLOPT_POST, 1); goto xfazL; cIHFM: curl_setopt($ch, CURLOPT_SAFE_UPLOAD, false); goto OY3Ql; cklGZ: if (class_exists('CURLFile')) { goto Idrwr; } goto nFaWX; ziE1l: $data = array("media" => new \CURLFile(realpath($img))); goto EnQ48; A6tk0: curl_close($ch); goto LtESZ; s5Nsk: $data = array("media" => '@' . realpath($img)); goto GkGSe; p8Chj: Idrwr: goto qxKn4; nFaWX: if (!defined('CURLOPT_SAFE_UPLOAD')) { goto MlbYv; } goto cIHFM; NsRHZ: curl_setopt($ch, CURLOPT_URL, $sucai); goto ub0aR; Ek0vk: $output = curl_exec($ch); goto A6tk0; Yw6fy: } function curl_post2($url, $data = null) { goto AgPFn; KQDnD: return $output; goto sRuhf; UlGkQ: curl_setopt($curl, CURLOPT_POSTFIELDS, $data); goto yT1t4; qfVpx: curl_close($curl); goto KQDnD; p680O: curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1); goto ma_0Y; cEIln: if (empty($data)) { goto W91OB; } goto xQDQj; xQDQj: curl_setopt($curl, CURLOPT_POST, 1); goto UlGkQ; ma_0Y: $output = curl_exec($curl); goto qfVpx; AgPFn: $curl = curl_init(); goto ITuQ4; yT1t4: W91OB: goto p680O; ITuQ4: curl_setopt($curl, CURLOPT_URL, $url); goto cEIln; sRuhf: } public function doMobileMydayu_form() { goto eAceS; auh_7: $total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_info) . " WHERE openid = :openid and reid = :reid {$where}", array(":openid" => $_W['openid'], ":reid" => $reid)); goto llvce; y_CGj: $ds = $fids = array(); goto ngIFn; tTWq_: goto kHdKb; goto UvyYU; ivuWA: mJMR8: goto rbzuf; LRcIE: $consult = pdo_fetch('SELECT id,createtime FROM ' . tablename('dayu_consult') . ' WHERE infoid = :infoid and openid = :openid and rid = \'0\' ORDER BY createtime DESC', array(":infoid" => $row['rerid'], ":openid" => $openid)); goto tLQQI; WKGav: WHOAG: goto OqGYQ; TPkfR: $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `reid`=:reid'; goto PBYWh; FC9FH: if (empty($row['rethumb'])) { goto tnHZi; } goto kN4ke; r234I: z12_G: goto Tupm_; Rfl_p: $pindex = max(1, intval($_GPC['page'])); goto JwJpn; pBZfG: $this->getFollow(); goto V3IDa; kqzvO: goto kHdKb; goto pm4C3; MeS5t: $row = pdo_fetch('SELECT * FROM ' . tablename($this->tb_info) . ' WHERE openid = :openid AND rerid = :rerid', array(":openid" => $_W['openid'], ":rerid" => $rerid)); goto tj_9u; LuWVs: bpffA: goto KP3Fs; fBf01: if (!is_array($linkage)) { goto bpffA; } goto S3x2j; ifnjS: $new_array = array(); goto IM1CY; xhSh1: goto dC7K3; goto GeVWY; kiuCg: tklnG: goto U_EpL; FEPCE: foreach ($new_array as $u => $v) { $last[] = $u; KT0ap: } goto WKGav; oOzUV: $status = intval($_GPC['status']); goto KSAsW; ueow1: $mname = !empty($par['mname']) ? $par['mname'] : '往期记录'; goto j30DQ; Y545x: $dayu_form['content'] = htmlspecialchars_decode($dayu_form['content']); goto yrYSj; RRWf3: $user_footer = 1; goto tTWq_; YdLmg: CUQrV: goto ReMBY; hGsUD: $setting = $this->module['config']; goto RQ8JX; Hczc2: $status = intval($_GPC['status']); goto c7v_P; ZqjlB: $pindex = max(1, intval($_GPC['page'])); goto eyuD3; cnbBj: $this->showMessage('非法访问.'); goto MdrN9; Rr7K2: goto CUQrV; goto ddmYs; G5ySY: $rerid = array_keys($rows); goto H_gY2; GTxPL: $total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_info) . " WHERE openid = :openid {$where} ", array(":openid" => $_W['openid'])); goto F7RiY; GjBIS: $member = mc_oauth_userinfo($_W['acid']); goto r234I; pm4C3: Gjm3J: goto hRvxR; S3x2j: $linkage['l1'] = $this->get_linkage($linkage['l1'], ''); goto zKLZk; CHCcS: WwlK7: goto S2eqI; llvce: foreach ($rows as $key => $val) { goto BHkVh; FjBzu: $rows[$key]['user'] = mc_fansinfo($val['openid'], $acid, $weid); goto Tkcx1; Tkcx1: $rows[$key]['kf'] = mc_fansinfo($val['kf'], $acid, $weid); goto Vs7Q8; Vs7Q8: $rows[$key]['comment'] = !empty($par['comment']) && pdo_tableexists('dayu_comment') && !empty($val['commentid']) ? $this->get_comment($val['commentid']) : ''; goto h3yS5; BHkVh: $rows[$key]['status'] = $this->get_status($val['reid'], $val['status']); goto FjBzu; h3yS5: nOds6: goto dYaqa; dYaqa: } goto pR7fc; pdYmV: $linkage = iunserializer($row['linkage']); goto fBf01; s5FQx: foreach ($fdatas as $fd) { $row['fields'][$fd['refid']] = $fd['data']; GAOpv: } goto jLn2O; F7RiY: rpXP_: goto Rr7K2; pR7fc: XsFmm: goto G5ySY; nCJoc: if (!$fids) { goto rpXP_; } goto W5xE3; PE2aH: $params = array(); goto Tkupl; F9ILw: $operation = !empty($_GPC['op']) ? $_GPC['op'] : 'display'; goto kvlLW; S9sav: $par = iunserializer($activity['par']); goto ueow1; qlmJ9: dC7K3: goto SwzMt; XFkN0: zSZbt: goto F9ILw; S2eqI: EHOUO: goto vSN_V; Cs46v: tnHZi: goto AKxZJ; v9FFb: if (!empty($member['avatar'])) { goto UtKF_; } goto MdTbu; tLQQI: $consultr = pdo_fetch('SELECT id,createtime FROM ' . tablename('dayu_consult') . ' WHERE infoid = :infoid and rid = \'1\' ORDER BY createtime DESC', array(":infoid" => $row['rerid'])); goto U4pcu; tj_9u: if (!empty($row)) { goto b_Y2E; } goto fqplq; GvdAJ: $fdatas = pdo_fetchall($sql, $params); goto s5FQx; j30DQ: if ($par['follow'] == 1) { goto aeESj; } goto BkXzS; hp6s4: $sql = 'SELECT * FROM ' . tablename($this->tb_field) . ' WHERE `reid`=:reid ORDER BY displayorder DESC, refid DESC'; goto vnIe7; KP3Fs: $row['createtime'] = !empty($row['createtime']) ? date('Y-m-d H:i', $row['createtime']) : '时间丢失'; goto jN1W_; YbOL4: foreach ($ds as $value) { goto MY0im; MY0im: if (!($value['type'] == 'reside')) { goto z6I6W; } goto mxfUU; maMpi: z6I6W: goto pMnH1; LLqDP: nv2Qf: goto w9WJE; mxfUU: $row['fields'][$value['refid']] = ''; goto O1j7X; O1j7X: foreach ($fdatas as $fdata) { goto G3pqF; G3pqF: if (!($fdata['refid'] == $value['refid'])) { goto ireVx; } goto Spxve; DonkA: ET8V6: goto y1GXf; ctcEh: ireVx: goto DonkA; Spxve: $row['fields'][$value['refid']] .= $fdata['data']; goto ctcEh; y1GXf: } goto LLqDP; pMnH1: YaVwC: goto KOgNg; w9WJE: goto nTnwT; goto maMpi; KOgNg: } goto L3h_0; KSAsW: if (!($_GPC['status'] != '')) { goto TMf4w; } goto LV6Mh; IO3uF: if (!(pdo_tableexists('dayu_kami') && !empty($par['kami']) && $row['kid'])) { goto Qr93W; } goto DV14n; N5cvZ: $row['voices'] = $row['voice']; goto y9rA_; xPMrn: $row['thumb'] = iunserializer($row['thumb']); goto N5cvZ; kGwjp: if (!(pdo_tableexists('dayu_sendkami') && !empty($par['sendkami']) && $row['kid'])) { goto mJMR8; } goto ApKEO; U4pcu: if (!(!empty($consult['id']) && $consult['createtime'] < $consultr['createtime'])) { goto tklnG; } goto MZc0j; F_Bq5: if ($openid) { goto zSZbt; } goto n7JPe; hRvxR: if ($reid) { goto BWNyf; } goto xKuHl; eyuD3: $psize = 10; goto oOzUV; HZjuj: include $this->template('dayu_form'); goto mmqFV; iRdZJ: $row['fields'] = array(); goto Aykgy; AKxZJ: $row['file'] = iunserializer($row['file']); goto dg95Y; dg95Y: $row['user'] = mc_fansinfo($row['openid'], $acid, $weid); goto Qxdmz; aBFPB: $where .= ' and ( status=2 or status=-1 )'; goto CHCcS; ufWmS: ODit7: goto aBFPB; wIKQv: $rows = pdo_fetchall($sql, $params); goto ifnjS; ReMBY: $pager = $this->pagination($total, $pindex, $psize); goto RRWf3; IM1CY: foreach ($rows as $v) { $new_array[$v['reid']] = 1; NZFyI: } goto zbVlV; ddmYs: BWNyf: goto V2PUo; ApKEO: $kami = pdo_get('dayu_sendkami', array("weid" => $weid, "id" => $row['kid']), array()); goto ivuWA; IsHkj: goto fDuOb; goto tPs21; ltc03: $params[':reid'] = $row['reid']; goto D4wsF; H_gY2: $children = array(); goto RpUoe; D4wsF: $fields = pdo_fetchall($sql, $params); goto wOBo0; oLW2E: Jh_Wb: goto GjBIS; W5xE3: $list = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_form) . " WHERE weid = :weid and reid in({$fids}) and status = 1 ORDER BY reid DESC", array(":weid" => $weid), 'reid'); goto Rfl_p; MdrN9: Etf_y: goto y_CGj; DV14n: $kami = pdo_get('dayu_kami', array("weid" => $weid, "id" => $row['kid']), array()); goto ZmQCm; xKuHl: $sql = 'SELECT `reid` FROM ' . tablename($this->tb_info) . ' WHERE openid = :openid ORDER BY rerid DESC'; goto PE2aH; kN4ke: $row['rethumb'] = iunserializer($row['rethumb']); goto Cs46v; hI9Wd: goto WwlK7; goto ufWmS; hh0Mq: WmA2w: goto YdLmg; zKLZk: $linkage['l2'] = $this->get_linkage($linkage['l2'], ''); goto LuWVs; C0GEE: $where .= " and status={$status}"; goto xhSh1; Qxdmz: $status = $this->get_status($row['reid'], $row['status']); goto IO3uF; ngIFn: foreach ($fields as $f) { goto ZnkRa; ZnkRa: $ds[$f['refid']]['fid'] = $f['title']; goto nwDUG; Hws4t: g0ft3: goto n0TMg; EiiJK: $ds[$f['refid']]['refid'] = $f['refid']; goto mkXdP; mkXdP: $ds[$f['refid']]['loc'] = $f['loc']; goto k4jJ4; k4jJ4: $fids[] = $f['refid']; goto Hws4t; nwDUG: $ds[$f['refid']]['type'] = $f['type']; goto EiiJK; n0TMg: } goto yMExJ; wN4nJ: b_Y2E: goto nWWMR; n7JPe: $this->showMessage('非法访问'); goto XFkN0; Tupm_: UtKF_: goto IsHkj; SwzMt: TMf4w: goto v5Ji9; rbzuf: if (!(!empty($par['comment']) && pdo_tableexists('dayu_comment') && !empty($row['commentid']))) { goto kgCja; } goto fRGcz; mpihQ: require MODULE_ROOT . '/fans.mobile.php'; goto F_Bq5; nWWMR: $la = iunserializer($activity['linkage']); goto pdYmV; uTeq0: if ($status == 2) { goto ODit7; } goto rQkfC; U_EpL: rYFtU: goto hp6s4; miWZN: $member = mc_oauth_fans($openid, $_W['acid']); goto oO0TP; vnIe7: $params = array(); goto ltc03; Xo66q: foreach ($childlist as $reply => $r) { goto BbXt1; BzJcF: $children[$r['rerid']][] = $r; goto zYq2W; zYq2W: unset($children[$reply]); goto CLPpQ; BbXt1: if (empty($r['rerid'])) { goto uLnAR; } goto BzJcF; CLPpQ: uLnAR: goto P50H2; P50H2: HRbyS: goto TpN8Z; TpN8Z: } goto hh0Mq; V4PGJ: $params[':reid'] = $reid; goto ADeU8; yMExJ: EQ7mF: goto zmoRp; zbVlV: Z3AYb: goto wUQnt; LV6Mh: if ($status == 2) { goto ATnsw; } goto C0GEE; UvyYU: tjhyD: goto hGsUD; dgkbn: $where .= ' and ( status=2 or status=-1 )'; goto qlmJ9; V3IDa: fDuOb: goto RBejj; fAKJV: $params[':weid'] = $_W['uniacid']; goto V4PGJ; RQ8JX: $rerid = intval($_GPC['rerid']); goto MeS5t; vSN_V: $rows = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_info) . " WHERE openid = :openid {$where} ORDER BY rerid DESC LIMIT " . ($pindex - 1) * $psize . ",{$psize}", array(":openid" => $_W['openid'])); goto GTxPL; ADeU8: $activity = pdo_fetch($sql, $params); goto S9sav; kACHJ: if ($operation == 'detail') { goto tjhyD; } goto kqzvO; wUQnt: $last = array(); goto FEPCE; Tkupl: $params[':openid'] = $openid; goto wIKQv; kvlLW: $reid = intval($_GPC['id']); goto TPkfR; Aykgy: $sql = 'SELECT * FROM ' . tablename($this->tb_data) . " WHERE `reid`=:reid AND `rerid`='{$row['rerid']}' AND `refid` IN ({$fids})"; goto GvdAJ; wOBo0: if (!empty($fields)) { goto Etf_y; } goto cnbBj; rQkfC: $where .= " and status={$status}"; goto hI9Wd; fqplq: $this->showMessage('记录不存在或是已经被删除！'); goto wN4nJ; OqGYQ: $fids = implode(',', $last); goto nCJoc; V2PUo: $list = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_form) . ' WHERE weid = :weid and status = 1 ORDER BY reid DESC', array(":weid" => $weid), 'reid'); goto ZqjlB; v5Ji9: $rows = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_info) . " WHERE openid = :openid and reid = :reid {$where} ORDER BY rerid DESC LIMIT " . ($pindex - 1) * $psize . ",{$psize}", array(":openid" => $_W['openid'], ":reid" => $reid), 'rerid'); goto auh_7; L3h_0: nTnwT: goto Y545x; MZc0j: $c_tishi = '<span class="weui-badge right" style="margin-left: 5px;position: absolute;top:5px;right:5px;">有新回复</span>'; goto kiuCg; RBejj: if ($operation == 'display') { goto Gjm3J; } goto kACHJ; y9rA_: $row['revoices'] = $row['revoice']; goto FC9FH; zmoRp: $fids = implode(',', $fids); goto iRdZJ; BkXzS: $member = !empty($member) ? $member : $_SESSION['userinfo']; goto v9FFb; eAceS: global $_W, $_GPC; goto mpihQ; fRGcz: $comment = pdo_get('dayu_comment', array("weid" => $weid, "id" => $row['commentid']), array()); goto QzaDb; ZmQCm: Qr93W: goto kGwjp; LEpJL: if (!pdo_tableexists('dayu_consult')) { goto rYFtU; } goto LRcIE; jN1W_: $row['yuyuetime'] = !empty($row['yuyuetime']) ? date('Y-m-d H:i', $row['yuyuetime']) : '请等待客服受理'; goto xPMrn; RpUoe: $childlist = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_data) . ' WHERE rerid IN (\'' . implode('\',\'', is_array($rerid) ? $rerid : array($rerid)) . '\') AND `reid`=:reid', array(":reid" => $reid)); goto Xo66q; JheiN: $title = $activity['title']; goto HZjuj; yrYSj: kHdKb: goto JheiN; oO0TP: goto z12_G; goto oLW2E; PBYWh: $params = array(); goto fAKJV; QzaDb: kgCja: goto LEpJL; GeVWY: ATnsw: goto dgkbn; tPs21: aeESj: goto pBZfG; MdTbu: if ($_W['account']['level'] > 3) { goto Jh_Wb; } goto miWZN; jLn2O: B8xOy: goto YbOL4; c7v_P: if (!($_GPC['status'] != '')) { goto EHOUO; } goto uTeq0; JwJpn: $psize = 10; goto Hczc2; mmqFV: } public function doMobileGetForm() { goto a_2K3; FqyYx: $mylink = $this->createMobileUrl('mydayu_form', array("id" => $form['reid'])); goto l7z4P; l7z4P: $result['id'] = $form['reid']; goto OWkQH; wOrIv: $result['html2'] = $html2; goto er9eN; qD47f: $par = iunserializer($form['par']); goto ii2l_; YZIt0: $form = pdo_get($this->tb_form, array("weid" => $weid, "reid" => $_GPC['id']), array()); goto qD47f; zw_Rx: $weid = $_W['uniacid']; goto YZIt0; NlHNW: $result['html'] = $html; goto wOrIv; Fl_2E: $html2 = '
	<div class="weui_tabbar tab-bottom">
		<a href="javascript:;" class="weui_tabbar_item close-popup">
			<div class="weui_tabbar_icon">
				<svg class="icon" aria-hidden="true">
					<use xlink:href="#icon-close"></use>
				</svg>
			</div>
			<p class="weui_tabbar_label">关闭</p>
		</a>
		<a href="' . $link . '" class="weui_tabbar_item">
			<div class="weui_tabbar_icon">
				<svg class="icon" aria-hidden="true">
					<use xlink:href="#icon-xinzeng"></use>
				</svg>
			</div>
			<p class="weui_tabbar_label">' . $form['title'] . '</p>
		</a>
		<a href="' . $mylink . '" class="weui_tabbar_item">
			<div class="weui_tabbar_icon">
				<svg class="icon" aria-hidden="true">
					<use xlink:href="#icon-jihuajindu"></use>
				</svg>
			</div>
			<p class="weui_tabbar_label">' . $par['mname'] . '</p>
		</a>
	</div>
		'; goto NlHNW; IfPCe: $thumb = tomedia($form['thumb']); goto ecxXO; ii2l_: $link = $this->createMobileUrl('dayu_form', array("id" => $form['reid'])); goto FqyYx; OWkQH: $result['mname'] = $form['mname']; goto IfPCe; a_2K3: global $_GPC, $_W; goto zw_Rx; ecxXO: $html = '
		 <div class="weui-header bg-blue">
			<div class="weui-header-left">
				<a href="javascript:;" class="icon icon-109 f-white close-popup">
					<svg class="icon" aria-hidden="true">
						<use xlink:href="#icon-left"></use>
					</svg>
				</a>
			</div>
			<h1 class="weui-header-title">' . $form['title'] . '</h1>
		</div>
		<div class="weui-weixin">
			<div class="weui-weixin-ui">
				<div class="weui-weixin-page">
					<div class="weui-weixin-img text-center"><img src="' . $thumb . '" id="image" class="center" style="width:100%;"></div>                                        
					<div class="weui-weixin-content">' . htmlspecialchars_decode($form['content']) . '</div>
				</div>
			</div>
		</div>
		'; goto Fl_2E; er9eN: message($result, '', 'ajax'); goto zCAvf; zCAvf: } public function doMobilelist() { goto hWm3z; hWm3z: global $_W, $_GPC; goto TJugh; Sx9hH: include $this->template('list'); goto ZvgHI; TJugh: $list = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_form) . ' WHERE weid = :weid and status = 1 ORDER BY reid DESC', array(":weid" => $_W['uniacid']), 'reid'); goto Sx9hH; ZvgHI: } public function doMobilePower() { goto aFk3M; GRSP0: $result['status'] = 0; goto rpgQA; Njp_e: U6jSt: goto hY7S1; f8ZUR: $result['status'] = 1; goto Naq5F; zIzeI: $result['msg'] = '派单成功'; goto TS6BS; nyb3t: HhE4H: goto cYKy4; RmDTh: $info .= "<a href='{$url}'>点击查看详情</a>"; goto lT5YZ; Naq5F: $result['msg'] = '转移成功'; goto c0QQr; yrF2E: mRqtW: goto Guk1v; hY7S1: message($result, '', 'ajax'); goto nyb3t; Xbbcs: $data = array("kfid" => $_GPC['openid']); goto wKzcY; KS8XW: $info .= "姓名：{$content['member']}
手机：{$content['mobile']}
管理员派单

"; goto RmDTh; auomW: $data = array("kf" => $_GPC['openid']); goto qFLSu; JK8p3: $CustomNotice = $acc->sendCustomNotice($custom); goto CQ3j2; YpSRq: if (!($_GPC['table'] == 'manage')) { goto FgEIn; } goto Xbbcs; CQ3j2: goto mRqtW; goto r6DnN; b6iRP: message($result, '', 'ajax'); goto g1itJ; g1itJ: FgEIn: goto Brq2R; ntqid: F2idM: goto n3spu; HdhKS: $activity = pdo_get($this->tb_form, array("weid" => $_W['uniacid'], "reid" => $reid), array("title", "k_templateid", "kfirst", "kfoot")); goto voh06; RCCW_: if ($_W['account']['level'] == ACCOUNT_SERVICE_VERIFY && !empty($activity['k_templateid'])) { goto czfa4; } goto EFYZ9; k6HUc: $result['msg'] = '派单失败'; goto Njp_e; gLTkY: $this->send_template_message(urldecode(json_encode($template))); goto yrF2E; MD5H6: $reid = $_GPC['reid']; goto pgvId; DSrrG: shySg: goto b6iRP; rpgQA: $result['msg'] = '转移失败'; goto DSrrG; c0QQr: goto shySg; goto i3Odg; i3Odg: RZQsi: goto GRSP0; wKzcY: if (pdo_update($this->tb_form, $data, array("reid" => $reid, "weid" => $_W['uniacid'])) === false) { goto RZQsi; } goto f8ZUR; TS6BS: goto U6jSt; goto ntqid; YgdGA: $template = array("touser" => $_GPC['openid'], "template_id" => $activity['k_templateid'], "url" => $_W['siteroot'] . 'app/' . $this->createMobileUrl('manageform', array("name" => "dayu_form", "weid" => $_W['uniacid'], "op" => "detail", "id" => $reid, "rerid" => $rerid)), "topcolor" => "#FF0000", "data" => array("first" => array("value" => urlencode($activity['kfirst'] . '\\n'), "color" => "#743A3A"), "keyword1" => array("value" => urlencode($content['member']), "color" => "#000000"), "keyword2" => array("value" => urlencode($content['mobile']), "color" => "#000000"), "keyword3" => array("value" => urlencode(date('Y-m-d H:i:s', $content['createtime'])), "color" => "#000000"), "keyword4" => array("value" => urlencode('管理员派单\\n'), "color" => "#FF0000"), "remark" => array("value" => urlencode($activity['kfoot']), "color" => "#008000"))); goto gLTkY; Brq2R: if (!($_GPC['table'] == 'case')) { goto HhE4H; } goto auomW; aFk3M: global $_GPC, $_W; goto MD5H6; r6DnN: czfa4: goto YgdGA; lT5YZ: $custom = array("msgtype" => "text", "text" => array("content" => urlencode($info)), "touser" => $_GPC['openid']); goto CYF01; voh06: $content = pdo_get($this->tb_info, array("reid" => $reid, "rerid" => $rerid), array("member", "mobile", "createtime")); goto RCCW_; pgvId: $rerid = $_GPC['rerid']; goto YpSRq; YsZ6n: $info = "【您好，{$activity['title']} 通知】

"; goto KS8XW; qFLSu: if (pdo_update($this->tb_info, $data, array("reid" => $reid, "rerid" => $rerid)) === false) { goto F2idM; } goto HdhKS; n3spu: $result['status'] = 0; goto k6HUc; CYF01: $acc = WeAccount::create($_W['acid']); goto JK8p3; Guk1v: $result['status'] = 1; goto zIzeI; EFYZ9: $url = $_W['siteroot'] . 'app/' . $this->createMobileUrl('manageform', array("name" => "dayu_form", "weid" => $_W['uniacid'], "op" => "detail", "id" => $reid, "rerid" => $rerid)); goto YsZ6n; cYKy4: } public function doMobilemanageform() { goto nGUBN; TmQeY: goto OygxN; goto y3fho; xZfT6: zKLpd: goto GX0Js; I1mgH: igrJW: goto Usggj; ZX4ZS: dNgAf: goto dIXl9; LwX1l: $pindex = max(1, intval($_GPC['page'])); goto NMPmt; zKoyZ: qszmf: goto S0kbv; QyMek: $fids = implode(',', $fids); goto omGSR; rUoC7: $record['status'] = intval($_GPC['status']); goto ulSh0; x20Io: $log = $activity['title'] . '-' . $activity['credit'] . '积分'; goto Ws0iU; B4q0U: F3xVh: goto udSfS; XjjxQ: $rerid = array_keys($rows); goto OQs3r; lN_K7: $par = iunserializer($activity['par']); goto siCVF; XGH6f: XTOxE: goto m8bQu; MN__m: if (!(pdo_tableexists('dayu_sendkami') && !empty($par['sendkami']) && $row['kid'])) { goto njEp1; } goto Hixoq; E9KPj: if ($repeat == $_GPC['repeat']) { goto e4yoO; } goto RnlHW; GLJGO: $row['thumb'] = iunserializer($row['thumb']); goto slZMs; eanFm: $info = $activity['title'] . ' 处理结果 ' . $status['name'] . '

'; goto tvhsx; wrdeR: $pager = $this->pagination($total, $pindex, $psize); goto l1UXu; GX0Js: $manage_footer = 1; goto WcDLP; Ya0Lg: if (!$_W['ispost']) { goto pslzp; } goto HplvK; vja1t: oXKOi: goto AuyuG; GNQ1a: e4yoO: goto BIrw2; ZVQ_a: $title = $activity['title']; goto LwX1l; kF3w_: load()->func('communication'); goto J1Lq5; aNSp7: $ytime = date('Y-m-d H:i:s', $yuyuetime); goto Ioo0L; mP3hM: $info = '【您好，受理结果通知】

'; goto IqT0M; l3FT6: $params2 = array(); goto QEGrh; dIXl9: if (!($_GPC['status'] == '3' && $par['icredit'] == '1')) { goto PfJzA; } goto PasI2; Iz71M: if ($openid == $activity['kfid'] || $openid == $row['kf'] || $activity['guanli'] == '1' && $isstaff) { goto qpAny; } goto lr9ww; koUe7: $sql = 'SELECT * FROM ' . tablename($this->tb_field) . ' WHERE `reid`=:reid ORDER BY displayorder DESC, refid DESC'; goto TnsKw; StI9L: $sms_data = array("mobile" => $row['mobile'], "title" => $activity['title'], "mname" => $row['member'], "mmobile" => $row['mobile'], "openid" => $row['openid'], "status" => $status['name'], "ptname" => $_W['account']['name'], "addons" => $_W['current_module']['name'], "content" => $alldata); goto Znkak; FxaMJ: dVkK8: goto zD8Vg; hkf65: n0dtT: goto HMkCg; KKnxz: if (!empty($row)) { goto o8ENS; } goto LMBHI; eDKJe: $consultr = pdo_fetch('SELECT id,createtime FROM ' . tablename('dayu_consult') . ' WHERE infoid = :infoid AND rid = \'1\' ORDER BY createtime DESC', array(":infoid" => $row['rerid'])); goto r6CEE; feIn3: $row = pdo_fetch('SELECT * FROM ' . tablename($this->tb_info) . ' WHERE rerid = :rerid', array(":rerid" => $rerid)); goto B_auk; vmtR0: $msg = ''; goto laAV4; um6gb: $record['rethumb'] = iserializer($th); goto ZX4ZS; BIrw2: $this->showMessage($activity['information'], $this->createMobileUrl('mydayu_form', array("id" => $reid))); goto I1mgH; QiYh8: if (!($par['sms'] != '0' && $par['paixu'] != '2' && !empty($activity['smstype']))) { goto WtTzv; } goto kF3w_; nGUBN: global $_W, $_GPC; goto kVULv; wExEJ: if (!(!empty($par['comment']) && pdo_tableexists('dayu_comment') && !empty($row['commentid']))) { goto qszmf; } goto qGX8M; wrqx5: $list = pdo_fetchall('SELECT s.reid, y.* FROM ' . tablename($this->tb_staff) . ' s left join ' . tablename($this->tb_form) . ' y on y.reid=s.reid WHERE y.weid=:weid AND y.status=1 AND s.openid=:openid ORDER BY y.reid DESC', array(":weid" => $weid, ":openid" => $openid), 'reid'); goto Tl0VD; bCO0S: $row['rethumb'] = iunserializer($row['rethumb']); goto iFXY_; W1s4r: if ($operation == 'detail') { goto BujQo; } goto TmQeY; Znkak: ihttp_post(murl('entry', array("do" => "Notice", "id" => $activity['smstype'], "m" => "dayu_sms"), true, true), $sms_data); goto HdnjE; gcNsM: DgwoT: goto CeN9C; FbmmE: dsuMD: goto zf4a0; Yl8Cg: $sql = 'SELECT * FROM ' . tablename($this->tb_info) . " WHERE reid=:reid {$where} ORDER BY createtime DESC,rerid DESC LIMIT " . ($pindex - 1) * $psize . ',' . $psize; goto LH7Mf; F1gzv: CHbMp: goto QiYh8; VlYa8: qpAny: goto CZ1ay; Ws0iU: mc_notice_credit1($row['openid'], mc_openid2uid($row['openid']), $activity['credit'], $log); goto XGH6f; x5CPZ: $c_tishi = '<span class="weui-badge right" style="margin-left: 5px;position: absolute;top:5px;right:5px;">有新咨询</span>'; goto nCEbM; XkDCC: $params[':reid'] = $reid; goto mFnt3; N7ApB: $info .= "<a href='{$url}'>现在去评价</a>"; goto olZXb; JA3KJ: jzlfj: goto BZdwg; R5X6M: if ($openid) { goto LPzq3; } goto p_G_L; xRi2k: $fields = pdo_fetchall($sql, $params); goto aDMVF; olZXb: $custom = array("msgtype" => "text", "text" => array("content" => urlencode($info)), "touser" => $row['openid']); goto qUnQh; H2EKE: PfJzA: goto wmb_G; Z4i7h: $childlist = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_data) . ' WHERE rerid IN (\'' . implode('\',\'', is_array($rerid) ? $rerid : array($rerid)) . '\') AND `reid`=:reid', array(":reid" => $reid)); goto rs7n8; sKFzC: $settings = uni_setting($_W['uniacid'], array("creditnames", "creditbehaviors")); goto DLLtJ; SJCGM: $activity = $this->get_form($reid); goto lN_K7; pDMX9: $where .= " and status={$status}"; goto QqdIL; SnRs9: $acc = WeAccount::create($_W['acid']); goto Oxcjz; kAtSA: $custom = array("msgtype" => "text", "text" => array("content" => urlencode($info)), "touser" => $row['openid']); goto SnRs9; vBWx_: $sql = 'SELECT * FROM ' . tablename($this->tb_data) . " WHERE `reid`=:reid AND `rerid`='{$row['rerid']}' AND `refid` IN ({$fids})"; goto gK0iP; fErc6: $huifu = $status['name'] . $kfinfo . $revoice; goto aNSp7; FPnHJ: o8ENS: goto PZCf5; IqT0M: $info .= "姓名：{$row['member']}
手机：{$row['mobile']}
受理结果：{$huifu}

"; goto SRwyL; HhzVB: YgCjq: goto pcI_T; r6CEE: if (!(!empty($consult['id']) && $consult['createtime'] > $consultr['createtime'])) { goto hL7qx; } goto x5CPZ; X7u0o: N3yX5: goto StI9L; Ioo0L: $outurl = !empty($par['noticeurl']) ? $par['noticeurl'] : $_W['siteroot'] . 'app/' . $this->createMobileUrl('mydayu_form', array("op" => "detail", "rerid" => $rerid, "id" => $row['reid'])); goto vmtR0; WOhKU: if (!is_error($acc)) { goto abEbn; } goto j0iLN; eq47R: $dayu_form = pdo_fetch('SELECT * FROM ' . tablename($this->tb_form) . ' WHERE reid = :reid', array(":reid" => $row['reid'])); goto U0Cj5; gJ2gw: BujQo: goto hIOUl; omGSR: $row['fields'] = array(); goto vBWx_; LGPaJ: $linkage = iunserializer($row['linkage']); goto WPTvW; kPdVD: tjyB1: goto QyMek; vpjSU: $isstaff = pdo_get($this->tb_staff, array("weid" => $weid, "openid" => $openid), array("id")); goto eRvSL; HplvK: $record = array(); goto rUoC7; dZmNy: foreach ($rows as $key => $val) { goto JHLZE; PvfPM: VuTJC: goto b1Awr; u5mbN: $rows[$key]['kf'] = mc_fansinfo($val['kf'], $acid, $weid); goto sNQpt; JHLZE: $rows[$key]['status'] = $this->get_status($val['reid'], $val['status']); goto hSJJM; hSJJM: $rows[$key]['user'] = mc_fansinfo($val['openid'], $acid, $weid); goto u5mbN; sNQpt: $rows[$key]['comment'] = !empty($par['comment']) && pdo_tableexists('dayu_comment') && !empty($val['commentid']) ? $this->get_comment($val['commentid']) : ''; goto PvfPM; b1Awr: } goto eDaXS; QqdIL: goto wALdA; goto vja1t; aNDpf: $formdata = $this->order_foreach($row['reid'], $rerid); goto VMIqo; rs7n8: foreach ($childlist as $reply => $r) { goto YPMXs; FGY8M: $children[$r['rerid']][] = $r; goto fDtF8; u34L5: OO4Qi: goto ajndp; YPMXs: if (empty($r['rerid'])) { goto yWrwF; } goto FGY8M; Bs_Uh: yWrwF: goto u34L5; fDtF8: unset($children[$reply]); goto Bs_Uh; ajndp: } goto xZfT6; Fdl0w: load()->func('tpl'); goto piQ1p; iFXY_: TS2Kq: goto eQ2px; mzJSR: if (!($activity['guanli'] == '0')) { goto S7PxR; } goto AaxeA; eRvSL: if ($isstaff) { goto XNaoe; } goto T66px; Ri_iP: XNaoe: goto Na4gp; u1YMp: $state = array(); goto ypSap; TzmKP: $params2[':openid'] = $openid; goto oUm9G; t4bin: $record['kf'] = $openid; goto om3QC; vcUr0: abEbn: goto GjUzh; CZ1ay: if (!pdo_tableexists('dayu_consult')) { goto dsuMD; } goto Zwo1v; UpHpo: if ($dayu_form['custom_status'] == 1) { goto YgCjq; } goto S0EB3; uKeJN: OaMcW: goto H7ihU; DLLtJ: $behavior = $settings['creditbehaviors']; goto S6qyN; LMBHI: $this->showMessage('记录不存在或是已经被删除！'); goto FPnHJ; UsWp9: XPxMd: goto UpHpo; UEUv5: $revoice = !empty($_GPC['revoice']) ? '\\n有语音答复' : ''; goto qADJM; bT9CT: foreach ($fields as $f) { goto sZdC9; uaJzz: $ds[$f['refid']]['loc'] = $f['loc']; goto zfpVI; A5aYY: $ds[$f['refid']]['type'] = $f['type']; goto K1g8V; sZdC9: $ds[$f['refid']]['fid'] = $f['title']; goto A5aYY; K1g8V: $ds[$f['refid']]['refid'] = $f['refid']; goto uaJzz; kJdxb: JRmij: goto JXe3_; zfpVI: $fids[] = $f['refid']; goto kJdxb; JXe3_: } goto kPdVD; HdnjE: WtTzv: goto JF0n9; Na4gp: $where2 = 'weid=:weid and status = 1'; goto l3FT6; GjUzh: $url = $outurl; goto mP3hM; qUnQh: $acc = WeAccount::create($_W['acid']); goto zl_95; slZMs: $row['voices'] = $row['voice']; goto xsf19; ulSh0: $record['yuyuetime'] = TIMESTAMP; goto t4bin; kVULv: require MODULE_ROOT . '/fans.mobile.php'; goto R5X6M; cVJ1J: $status = $this->get_status($row['reid'], $row['status']); goto u1YMp; s1T6k: LPzq3: goto Fdl0w; OQs3r: $children = array(); goto Z4i7h; HMkCg: qy1oK: goto L6_WA; FX6ry: $params[':reid'] = $row['reid']; goto xRi2k; S0EB3: $template = array("touser" => $row['openid'], "template_id" => $dayu_form['m_templateid'], "url" => $outurl, "topcolor" => "#FF0000", "data" => array("first" => array("value" => urlencode($dayu_form['mfirst']), "color" => "#743A3A"), "keyword1" => array("value" => urlencode($row['member']), "color" => "#000000"), "keyword2" => array("value" => urlencode($row['mobile']), "color" => "#000000"), "keyword3" => array("value" => urlencode(date('Y-m-d H:i:s', TIMESTAMP)), "color" => "#000000"), "keyword4" => array("value" => urlencode($huifu), "color" => "#FF0000"), "remark" => array("value" => urlencode($dayu_form['mfoot']), "color" => "#008000"))); goto zTXJQ; zD8Vg: foreach ($ds as $value) { goto oDcsp; LW36x: htJ1I: goto dz2mw; LpwND: lAnBA: goto mB7Ob; BEuJk: foreach ($fdatas as $fdata) { goto o3_vq; YLpDp: $row['fields'][$value['refid']] .= $fdata['data']; goto ZI4hP; o3_vq: if (!($fdata['refid'] == $value['refid'])) { goto A3axq; } goto YLpDp; CVHlK: HYKSf: goto QvwCd; ZI4hP: A3axq: goto CVHlK; QvwCd: } goto LW36x; dz2mw: goto OaMcW; goto LpwND; mB7Ob: MyxLM: goto k6a9O; oDcsp: if (!($value['type'] == 'reside')) { goto lAnBA; } goto TVi1c; TVi1c: $row['fields'][$value['refid']] = ''; goto BEuJk; k6a9O: } goto uKeJN; udSfS: if (!($row['icredit'] != '1' && $par['icredit'] == '1' && $activity['credit'] != '0.00' && $_GPC['status'] == '3')) { goto XTOxE; } goto sKFzC; oJbXN: QKp40: goto gcNsM; qu1yb: if (!empty($repeat)) { goto XJrHV; } goto Z13iQ; tvhsx: $info .= "{$par['commenttitle']}

"; goto N7ApB; kC3Ol: goto PMaeM; goto v43oS; xsf19: if (empty($row['rethumb'])) { goto TS2Kq; } goto bCO0S; p_G_L: $this->showMessage('非法访问，空'); goto s1T6k; WcDLP: goto OygxN; goto gJ2gw; JF0n9: pdo_update('dayu_form_info', $record, array("rerid" => $rerid)); goto fRdSh; ERvAc: goto igrJW; goto GNQ1a; aDMVF: if (!empty($fields)) { goto C22YC; } goto JeF_A; G301c: $status = $_GPC['status']; goto dkxEy; gRwlv: include $this->template('manage_form'); goto oulT2; RnlHW: setcookie('r_submit', $_GPC['repeat']); goto ERvAc; PbRj9: ghBt5: goto Yl8Cg; ntPsc: $linkage['l2'] = $this->get_linkage($linkage['l2'], ''); goto SK8d4; qJouj: return error(-1, $acc['message']); goto TvliR; TvliR: IvVmv: goto D0lNc; VMIqo: foreach ($formdata as $index => $v) { $alldata[] = $v['title'] . ':' . $v['data'] . ','; qym8n: } goto X7u0o; DWwHK: C22YC: goto klc0t; om3QC: $record['kfinfo'] = $_GPC['kfinfo']; goto ESfjX; AaxeA: $where2 .= ' and kfid = :openid'; goto TzmKP; SRwyL: $info .= "<a href='{$url}'>点击查看详情</a>"; goto kAtSA; L6_WA: if ($operation == 'display') { goto FFOIg; } goto W1s4r; T66px: $this->showMessage('非法访问！你不是管理员。', $this->createMobileUrl('index'), 'info'); goto Ri_iP; eOS0V: pslzp: goto JA3KJ; laAV4: if (!(!empty($par['wxcard']) && !empty($_GPC['wxcard']) && pdo_tableexists('dayu_wxcard'))) { goto XPxMd; } goto n05Nw; eDaXS: NPPmu: goto XjjxQ; wmb_G: $kfinfo = !empty($record['kfinfo']) ? '\\n客服回复：' . $record['kfinfo'] : ''; goto UEUv5; S0kbv: if (!(!empty($par['wxcard']) && pdo_tableexists('dayu_wxcard_activity'))) { goto N8fl1; } goto ZAyfU; ZRwef: b3sNT: goto Ya0Lg; BAh6w: mc_group_update(mc_openid2uid($row['openid'])); goto x20Io; V1Ypl: $repeat = $_COOKIE['r_submit']; goto KsCOR; H7ihU: $yuyuetime = !empty($row['yuyuetime']) ? date('Y-m-d H:i', $row['yuyuetime']) : date('Y-m-d H:i', TIMESTAMP); goto KZ34G; WdsMs: $linkage['l1'] = $this->get_linkage($linkage['l1'], ''); goto ntPsc; zTXJQ: $this->send_template_message(urldecode(json_encode($template))); goto ksqtX; PZCf5: $face = mc_fansinfo($row['openid'], $acid, $weid); goto eq47R; oUm9G: S7PxR: goto S2M85; i8jnh: if (!is_array($linkage)) { goto kMmPe; } goto WdsMs; AuyuG: $where .= ' and ( status=2 or status=-1 )'; goto pi6MH; PasI2: $record['icredit'] = 1; goto H2EKE; Hixoq: $kami = pdo_get('dayu_sendkami', array("weid" => $weid, "id" => $row['kid']), array()); goto vDIF2; Nj027: $row['yuyuetime'] = !empty($row['yuyuetime']) ? date('Y年m月d日 H:i', $row['yuyuetime']) : '客服尚未受理'; goto GLJGO; SK8d4: kMmPe: goto Iz71M; zf4a0: $custom = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_custom) . ' WHERE weid = :weid ORDER BY displayorder DESC', array(":weid" => $weid)); goto KKnxz; B_auk: $la = iunserializer($activity['linkage']); goto LGPaJ; x8i85: if (!($openid != $activity['kfid'] && $activity['guanli'] == '0')) { goto ghBt5; } goto vOnmx; Tl0VD: foreach ($list as $key => $val) { $list[$key]['count'] = $this->count_form($val['reid'], 2); jefYi: } goto hkf65; rKEZP: goto jzlfj; goto VlYa8; q12DT: if (!is_error($acc)) { goto IvVmv; } goto qJouj; WPTvW: if (!(pdo_tableexists('dayu_kami') && $row['kid'])) { goto SygdE; } goto vwjiA; piQ1p: $operation = !empty($_GPC['op']) ? $_GPC['op'] : 'display'; goto OOOke; O4sWc: OygxN: goto I6fWE; ZAyfU: $wxcard = pdo_fetchall('SELECT * FROM ' . tablename('dayu_wxcard') . ' WHERE weid = :weid AND cid=:cid AND status=1', array(":weid" => $_W['uniacid'], ":cid" => $par['wxcard'])); goto afw8D; dAtxh: yT5EW: goto um6gb; Z62xo: $rerid = intval($_GPC['rerid']); goto feIn3; qGX8M: $comment = pdo_get('dayu_comment', array("weid" => $weid, "id" => $row['commentid']), array()); goto zKoyZ; BZdwg: $titles = $activity['title']; goto O4sWc; LBGtw: SygdE: goto MN__m; l1UXu: $rows = pdo_fetchall($sql, $params, 'rerid'); goto dZmNy; Z13iQ: setcookie('r_submit', $_GPC['repeat']); goto kC3Ol; CeN9C: N8fl1: goto i8jnh; LH7Mf: $params = array(); goto XkDCC; dkxEy: if (!($status != '')) { goto SqKNb; } goto ciE6V; afw8D: if (!is_array($wxcard)) { goto DgwoT; } goto ShLQE; pcI_T: $acc = notice_init(); goto WOhKU; CnqSH: $row['user'] = mc_fansinfo($row['openid'], $acid, $weid); goto qpA7B; lr9ww: $this->showMessage('非法访问！你不是管理员。'); goto rKEZP; tu7oP: if ($reid) { goto qy1oK; } goto wrqx5; y3fho: FFOIg: goto ZVQ_a; fRdSh: $this->showMessage('修改成功' . $msg, referer(), 'success'); goto eOS0V; zl_95: $CustomNotice = $acc->sendCustomNotice($custom); goto F1gzv; n05Nw: $wxcard_post = ihttp_post(murl('entry', array("do" => "recorded", "couponid" => $_GPC['wxcard'], "m" => "dayu_wxcard"), true, true), array("addons" => $_W['current_module']['name'], "openid" => $row['openid'], "infoid" => $rerid)); goto kCYdt; klc0t: $ds = $fids = array(); goto bT9CT; KsCOR: if (empty($_GPC['repeat'])) { goto b3sNT; } goto qu1yb; ciE6V: if ($status == 2) { goto oXKOi; } goto pDMX9; J1Lq5: $alldata = array(); goto aNDpf; Wlkit: $acc = notice_init(); goto q12DT; siCVF: $staff = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_staff) . ' WHERE reid = :reid ORDER BY `id` DESC', array(":reid" => $reid)); goto vpjSU; vDIF2: njEp1: goto wExEJ; mFnt3: $total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_info) . " WHERE reid = :reid {$where} ", $params); goto wrdeR; m8bQu: if (!(pdo_tableexists('dayu_comment') && !empty($par['comment']) && empty($row['commentid']) && $_GPC['status'] == '3')) { goto CHbMp; } goto Wlkit; Usggj: PMaeM: goto ZRwef; pOtcq: foreach ($_GPC['thumb'] as $thumb) { $th[] = tomedia($thumb); DIqen: } goto dAtxh; p2MdJ: foreach ($arr2 as $index => $v) { $state[$v][] = $this->get_status($reid, $v); BZz3t: } goto a5gPe; qADJM: $status = $this->get_status($reid, $_GPC['status']); goto fErc6; D0lNc: $url = $outurl; goto eanFm; ksqtX: goto F3xVh; goto HhzVB; hIOUl: $setting = $this->module['config']; goto Z62xo; v43oS: XJrHV: goto E9KPj; OOOke: $reid = intval($_GPC['id']); goto SJCGM; QEGrh: $params2[':weid'] = $weid; goto mzJSR; S6qyN: mc_credit_update(mc_openid2uid($row['openid']), $behavior['activity'], $activity['credit'], array(0, $activity['title'])); goto BAh6w; U0Cj5: $dayu_form['content'] = htmlspecialchars_decode($dayu_form['content']); goto koUe7; ypSap: $arr2 = array("0", "1", "2", "3", "8"); goto p2MdJ; I6fWE: $picker = 1; goto gRwlv; gK0iP: $fdatas = pdo_fetchall($sql, $params); goto pNksi; vOnmx: $where .= " and kf='{$openid}'"; goto PbRj9; eQ2px: $row['file'] = iunserializer($row['file']); goto CnqSH; pi6MH: wALdA: goto U8jCM; j0iLN: return error(-1, $acc['message']); goto vcUr0; kCYdt: $msg .= $wxcard_post['msg']; goto UsWp9; nCEbM: hL7qx: goto FbmmE; TnsKw: $params = array(); goto FX6ry; Zwo1v: $consult = pdo_fetch('SELECT id,createtime FROM ' . tablename('dayu_consult') . ' WHERE infoid = :infoid AND rid = \'0\' ORDER BY createtime DESC', array(":infoid" => $row['rerid'])); goto eDKJe; U8jCM: SqKNb: goto x8i85; NMPmt: $psize = 10; goto G301c; ShLQE: foreach ($wxcard as &$val) { $val['coupon'] = pdo_get('coupon', array("uniacid" => $_W['uniacid'], "card_id" => $val['cardid']), array("id", "title")); s3aGw: } goto oJbXN; ESfjX: $record['revoice'] = empty($row['revoice']) ? $_GPC['revoice'] : $row['revoice']; goto u_CZE; a5gPe: vQYbg: goto V1Ypl; vwjiA: $kami = pdo_get('dayu_kami', array("weid" => $_W['uniacid'], "id" => $row['kid']), array()); goto LBGtw; S2M85: $list = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_form) . " WHERE {$where2} ORDER BY reid DESC", $params2, 'reid'); goto tu7oP; JeF_A: $this->showMessage('非法访问.'); goto DWwHK; Oxcjz: $CustomNotice = $acc->sendCustomNotice($custom); goto B4q0U; u_CZE: if (!is_array($_GPC['thumb'])) { goto dNgAf; } goto pOtcq; KZ34G: $row['createtime'] = !empty($row['createtime']) ? date('Y年m月d日 H:i', $row['createtime']) : '时间丢失'; goto Nj027; qpA7B: $row['member'] = !empty($row['member']) ? $row['member'] : $row['user']['nickname']; goto cVJ1J; pNksi: foreach ($fdatas as $fd) { $row['fields'][$fd['refid']] = $fd['data']; ZpsY0: } goto FxaMJ; oulT2: } public function isHy($openid) { goto mgPsY; MTNMu: goto PvL_T; goto JaNTE; kogYe: PvL_T: goto Vfyh5; mgPsY: global $_W; goto pwDAD; Enzlq: if (empty($card)) { goto NDEpG; } goto f9bXO; pwDAD: load()->model('mc'); goto Omg7U; WMlu7: return false; goto kogYe; Omg7U: $card = pdo_fetch('SELECT * FROM ' . tablename('mc_card_members') . ' WHERE uniacid=:uniacid AND openid = :openid ', array(":uniacid" => $_W['uniacid'], ":openid" => $openid)); goto Enzlq; JaNTE: NDEpG: goto WMlu7; f9bXO: return true; goto MTNMu; Vfyh5: } public function send_template_message($data) { goto mxbF5; Nt30q: load()->func('communication'); goto BkQwT; wxhuG: $url = 'https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=' . $access_token; goto cZcy3; lhjlz: return error(-1, "访问公众平台接口失败, 错误: {$response['message']}"); goto x9Dp1; Gmzm0: KBn11: goto glC_2; OXLsb: return error(-1, "访问微信接口错误, 错误代码: {$result['errcode']}, 错误信息: {$result['errmsg']},信息详情：{$this->error_code($result['errcode'], $result['errmsg'])}"); goto Gmzm0; m5k0U: dm2b6: goto OXLsb; ay27J: if (empty($result)) { goto crgsA; } goto Jp00o; EawOn: goto KBn11; goto m5k0U; Jp00o: if (!empty($result['errcode'])) { goto dm2b6; } goto Pabor; FoNFc: crgsA: goto UtA5u; fqLaG: load()->classs('weixin.account'); goto Nt30q; x9Dp1: afPGd: goto ixtgc; Pabor: goto KBn11; goto FoNFc; UtA5u: return error(-1, "接口调用失败, 原数据: {$response['meta']}"); goto EawOn; mxbF5: global $_W, $_GPC; goto fqLaG; glC_2: return true; goto x4MB4; ixtgc: $result = @json_decode($response['content'], true); goto ay27J; ZQ3LS: if (!is_error($response)) { goto afPGd; } goto lhjlz; cZcy3: $response = ihttp_request($url, $data); goto ZQ3LS; BkQwT: $access_token = WeAccount::token(); goto wxhuG; x4MB4: } public function AjaxMessage($msg, $status = 0) { goto rCFwb; rCFwb: $result = array("message" => $msg, "status" => $status); goto yoXW1; yoXW1: echo json_encode($result); goto uZ_zu; uZ_zu: exit; goto KMzOw; KMzOw: } public function doMobilechangeAjax() { goto cYFsb; eXuyL: $params[':reid'] = $reid; goto DsaFb; QvZeu: $params = array(); goto JmUCi; jn710: $id = intval($_GPC['id']); goto R6QQq; ZOSC0: $acc = WeAccount::create($_W['acid']); goto amtlL; oTKmJ: pdo_update('dayu_form_info', $data, array("rerid" => $id)); goto L2uxz; baBCs: $data = array("status" => $status); goto LJfRT; jjOOA: $data = array("first" => array("value" => $activity['mfirst'] . '
', "color" => "#743A3A"), "keyword1" => array("value" => $row['member']), "keyword2" => array("value" => $row['mobile']), "keyword3" => array("value" => date('Y-m-d H:i:s', TIMESTAMP)), "keyword4" => array("value" => $activity['state3']), "remark" => array("value" => '
' . $activity['mfoot'], "color" => "#008000")); goto ZOSC0; LJfRT: if (!empty($id)) { goto QsKqQ; } goto jFaDQ; RoPw5: $url = !empty($par['noticeurl']) ? $par['noticeurl'] : $_W['siteroot'] . 'app/' . $this->createMobileUrl('mydayu_form', array("op" => "detail", "rerid" => $id, "id" => $reid)); goto jjOOA; amtlL: $acc->sendTplNotice($row['openid'], $activity['m_templateid'], $data, $url, '#FF0000'); goto oTKmJ; OOVpQ: $status = $_GPC['status']; goto baBCs; L2uxz: $this->AjaxMessage('更新成功!', 1); goto PZSjx; DsaFb: $activity = pdo_fetch($sql, $params); goto hFCz_; R6QQq: $reid = intval($_GPC['reid']); goto vUzhg; PZSjx: LtGdl: goto HPv1Z; hFCz_: $par = iunserializer($activity['par']); goto lTE_M; lTE_M: $row = pdo_fetch('SELECT * FROM ' . tablename($this->tb_info) . ' WHERE rerid = :rerid', array(":rerid" => $id)); goto OOVpQ; jFaDQ: $this->AjaxMessage('更新失败!', 0); goto HxOVU; HxOVU: goto LtGdl; goto r4Sly; r4Sly: QsKqQ: goto RoPw5; cYFsb: global $_W, $_GPC; goto jn710; JmUCi: $params[':weid'] = $_W['uniacid']; goto eXuyL; vUzhg: $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `reid`=:reid'; goto QvZeu; HPv1Z: } public function doMobileLocate() { goto ypmKa; UI3m6: O9k_V: goto ymVtN; QXRHL: $result['message'] = ''; goto lfHLp; ggU9b: $result['lat'] = $setting['contact']['lat']; goto EEQjK; cqLGk: $result['name'] = $setting['contact']['company']; goto Klk2G; EEQjK: $result['lng'] = $setting['contact']['lng']; goto QXRHL; oAggv: if (!($_GPC['op'] == 'contact')) { goto O9k_V; } goto cqLGk; QG1qu: require MODULE_ROOT . '/fans.mobile.php'; goto lW8Fl; ypmKa: global $_W, $_GPC; goto QG1qu; lW8Fl: $result = array("error" => "error", "message" => "", "data" => ""); goto oAggv; Luyg5: $result['mobile'] = $setting['contact']['mobile']; goto ggU9b; lfHLp: die(json_encode($result)); goto UI3m6; Klk2G: $result['address'] = $setting['contact']['province'] . $setting['contact']['city'] . $setting['contact']['district'] . $setting['contact']['address']; goto Luyg5; ymVtN: } function pagination($tcount, $pindex, $psize = 15, $url = "", $context = array("before" => 5, "after" => 4, "ajaxcallback" => "")) { goto ljsrl; cu7Ga: $html .= "<div class=\"pager-first\"><a {$pdata['faa']} class=\"pager-nav\">首页</a></div>"; goto IY16y; oI9Ov: $pdata['tcount'] = $tcount; goto Py3Xy; PMF_Y: $html .= "<div class=\"pager-next\"><a {$pdata['naa']} class=\"pager-nav\">下一页</a></div>"; goto UbVyH; ANjce: H1bCE: goto pmPCU; pq8Ba: $html .= '<div class="pager-left">'; goto ZcXhl; mW9pD: $_GET['page'] = $pdata['lindex']; goto h_FDh; HGrg7: if ($pdata['cindex'] < $pdata['tpage']) { goto xCDj2; } goto x3qkk; DS8HH: $pdata['naa'] = 'href="?' . str_replace('*', $pdata['nindex'], $url) . '"'; goto L3Vq3; ZInK3: xCDj2: goto Tfwid; amzV1: $html = '<div class="pager">'; goto XGQ2u; qz8KL: $pdata['paa'] = 'href="javascript:;" onclick="p(\'' . $_W['script_name'] . $url . '\', \'' . $pdata['pindex'] . '\', ' . $context['ajaxcallback'] . ')"'; goto ytilz; IzvYQ: $pdata['cindex'] = $cindex; goto FLeE9; mUnRq: nSq2I: goto YQHoL; IY16y: $html .= "<div class=\"pager-pre\"><a {$pdata['paa']}>上一页</a></div>"; goto l4Cr1; FiW63: $pdata['nindex'] = $cindex < $pdata['tpage'] ? $cindex + 1 : $pdata['tpage']; goto EquOx; Jcz7n: if ($context['isajax']) { goto H1bCE; } goto bBBA6; ljsrl: global $_W; goto Z0gQ2; Fqdou: $cindex = min($cindex, $pdata['tpage']); goto zRZ8c; EWZt2: $pdata['faa'] = 'href="javascript:;" onclick="p(\'' . $_W['script_name'] . $url . '\', \'' . $pdata['findex'] . '\', ' . $context['ajaxcallback'] . ')"'; goto qz8KL; oEgn1: HJzT8: goto HrKf7; PlTa_: $pdata['paa'] = 'href="?' . str_replace('*', $pdata['pindex'], $url) . '"'; goto DS8HH; L3Vq3: $pdata['laa'] = 'href="?' . str_replace('*', $pdata['lindex'], $url) . '"'; goto yrBEa; N3o0f: if (!($pdata['tpage'] <= 1)) { goto HJzT8; } goto pEyXH; pmPCU: if ($url) { goto BBJQ1; } goto LRrJf; rzOSO: goto vXrGi; goto ANjce; S6bmd: return $html; goto lyMYd; HrKf7: $cindex = $pindex; goto Fqdou; dw9Zc: $pdata['pindex'] = $cindex > 1 ? $cindex - 1 : 1; goto FiW63; OwJld: $pdata['laa'] = 'href="javascript:;" onclick="p(\'' . $_W['script_name'] . $url . '\', \'' . $pdata['lindex'] . '\', ' . $context['ajaxcallback'] . ')"'; goto T2nnS; pizJ9: s45hh: goto oI9Ov; I1sej: BBJQ1: goto EWZt2; ZcXhl: $html .= '<div class="pager-pre" style="width:100%"><a href="###">第一页</a></div>'; goto HqKu0; d7VNS: goto nSq2I; goto ZInK3; yrBEa: RGtoG: goto rzOSO; FLeE9: $pdata['findex'] = 1; goto dw9Zc; XECrq: $pdata['naa'] = 'href="' . $_W['script_name'] . '?' . http_build_query($_GET) . '"'; goto mW9pD; XGzMJ: goto brVuA; goto tp7JA; oasAJ: $html .= '<div class="pager-left">'; goto cu7Ga; pEyXH: return ''; goto oEgn1; x3qkk: $html .= '<div class="pager-right">'; goto CoHBg; bBBA6: if ($url) { goto b3dDn; } goto Qz4CF; LRrJf: $url = $_W['script_name'] . '?' . http_build_query($_GET); goto I1sej; X7E4F: goto RGtoG; goto KcvuK; hCazh: $html .= "<div class=\"pager-cen\">{$pindex} / " . $pdata['tpage'] . '</div>'; goto HGrg7; E5Bwd: if (!$context['ajaxcallback']) { goto s45hh; } goto azxjN; I3mQ6: brVuA: goto hCazh; ytilz: $pdata['naa'] = 'href="javascript:;" onclick="p(\'' . $_W['script_name'] . $url . '\', \'' . $pdata['nindex'] . '\', ' . $context['ajaxcallback'] . ')"'; goto OwJld; KcvuK: b3dDn: goto gBx7u; h_FDh: $pdata['laa'] = 'href="' . $_W['script_name'] . '?' . http_build_query($_GET) . '"'; goto X7E4F; HqKu0: $html .= '</div>'; goto XGzMJ; Qz4CF: $_GET['page'] = $pdata['findex']; goto RzTVX; RzTVX: $pdata['faa'] = 'href="' . $_W['script_name'] . '?' . http_build_query($_GET) . '"'; goto oq3dW; zRZ8c: $cindex = max($cindex, 1); goto IzvYQ; gBx7u: $pdata['faa'] = 'href="?' . str_replace('*', $pdata['findex'], $url) . '"'; goto PlTa_; GN62i: $pdata['paa'] = 'href="' . $_W['script_name'] . '?' . http_build_query($_GET) . '"'; goto Ih29c; XGQ2u: if ($pdata['cindex'] > 1) { goto IAHtl; } goto pq8Ba; Ih29c: $_GET['page'] = $pdata['nindex']; goto XECrq; Xayv7: $html .= '</div>'; goto mUnRq; qxpdF: $html .= '</div>'; goto d7VNS; Z0gQ2: $pdata = array("tcount" => 0, "tpage" => 0, "cindex" => 0, "findex" => 0, "pindex" => 0, "nindex" => 0, "lindex" => 0, "options" => ""); goto E5Bwd; YQHoL: $html .= '<div class="clr"></div></div>'; goto S6bmd; CoHBg: $html .= '<div class="pager-next" style="width:100%"><a href="###">尾页</a></div>'; goto qxpdF; oq3dW: $_GET['page'] = $pdata['pindex']; goto GN62i; Tfwid: $html .= '<div class="pager-right">'; goto PMF_Y; azxjN: $context['isajax'] = true; goto pizJ9; tp7JA: IAHtl: goto oasAJ; Py3Xy: $pdata['tpage'] = ceil($tcount / $psize); goto N3o0f; T2nnS: vXrGi: goto amzV1; UbVyH: $html .= "<div class=\"pager-end\"><a {$pdata['laa']} class=\"pager-nav\">尾页</a></div>"; goto Xayv7; l4Cr1: $html .= '</div>'; goto I3mQ6; EquOx: $pdata['lindex'] = $pdata['tpage']; goto Jcz7n; lyMYd: } public function doMobileFansUs() { goto ECwd0; ESsuI: include $this->template('fans_us'); goto eYx9N; AjW12: $qrcodesrc = tomedia('qrcode_' . $_W['acid'] . '.jpg'); goto ESsuI; ECwd0: global $_W, $_GPC; goto cDIbL; cDIbL: require MODULE_ROOT . '/fans.mobile.php'; goto AjW12; eYx9N: } public function getFollow() { goto p1uHB; VERmT: if (intval($p['follow']) == 0) { goto RQyA1; } goto EdABR; Y3FM5: require MODULE_ROOT . '/fans.mobile.php'; goto dSmqy; Azcmy: header('Location: ' . $this->createMobileUrl('FansUs'), true, 301); goto HjzqW; dSmqy: $p = pdo_fetch('SELECT follow FROM ' . tablename('mc_mapping_fans') . ' WHERE uniacid = :weid AND openid = :openid LIMIT 1', array(":weid" => $_W['uniacid'], ":openid" => $_W['openid'])); goto VERmT; EdABR: return true; goto B9bfv; SWTfM: RQyA1: goto Azcmy; p1uHB: global $_GPC, $_W; goto Y3FM5; B9bfv: goto oBBfV; goto SWTfM; HjzqW: oBBfV: goto HF2HG; HF2HG: } private function checkauth3($openid, $nickname, $headimgurl) { goto b2VEt; d4hjD: $default_groupid = pdo_fetchcolumn('SELECT groupid FROM ' . tablename('mc_groups') . ' WHERE uniacid = :uniacid AND isdefault = 1', array(":uniacid" => $_W['uniacid'])); goto M97oS; Z1hLr: $fanid = pdo_insertid(); goto FL4pU; RXYx9: $post = array("acid" => $_W['acid'], "uniacid" => $_W['uniacid'], "nickname" => $nickname, "openid" => $_W['fans']['openid'], "salt" => random(8), "follow" => 0, "updatetime" => TIMESTAMP, "tag" => base64_encode(iserializer($_W['fans']))); goto cUkBo; fiwSF: nKScv: goto GWKs2; FL4pU: goto aWAXE; goto P5khR; IqMmc: if (empty($fan['uid'])) { goto Rq6Cb; } goto bfpYc; KjKlb: if (empty($_W['member']['uid']) && empty($settings['passport']['focusreg'])) { goto rMx9R; } goto etuNK; bfpYc: $_W['member']['uid'] = $fan['uid']; goto chM2f; SyrFs: $fanid = $fan['fanid']; goto hBMfy; P5khR: a56Lu: goto SyrFs; kHUL9: Rq6Cb: goto rhdKe; WIWum: $uid = pdo_insertid(); goto ZNrc3; cUkBo: pdo_insert('mc_mapping_fans', $post); goto Z1hLr; M97oS: $data = array("uniacid" => $_W['uniacid'], "email" => $email, "salt" => random(8), "groupid" => $default_groupid, "createtime" => TIMESTAMP, "password" => md5($message['from'] . $data['salt'] . $_W['config']['setting']['authkey']), "avatar" => $headimgurl, "nickname" => $nickname); goto WJaPH; UAKoo: $fan = pdo_get('mc_mapping_fans', array("uniacid" => $_W['uniacid'], "openid" => $openid)); goto uYi4s; b2VEt: global $_W, $engine; goto Uz_dv; Uz_dv: $settings = cache_load('unisetting:' . $_W['uniacid']); goto KjKlb; SmaxQ: goto WK6VG; goto CYKgw; CYKgw: rMx9R: goto UAKoo; chM2f: $_W['fans']['uid'] = $fan['uid']; goto MdLvY; rhdKe: $email = md5($oauth['openid']) . '@vqiyi.cn'; goto d4hjD; hBMfy: aWAXE: goto IqMmc; X5F3S: pdo_update('mc_mapping_fans', array("uid" => $uid), array("fanid" => $fanid)); goto fiwSF; GWKs2: WK6VG: goto z7y21; ZDEIC: $_W['fans']['uid'] = $uid; goto X5F3S; etuNK: checkauth(); goto SmaxQ; uYi4s: if (!empty($fan)) { goto a56Lu; } goto RXYx9; WJaPH: pdo_insert('mc_members', $data); goto WIWum; MdLvY: goto nKScv; goto kHUL9; ZNrc3: $_W['member']['uid'] = $uid; goto ZDEIC; z7y21: } private function checkAuth2() { goto osh_v; Zo6eI: goto r1u5Q; goto qTLi6; gk2fY: $_W['member']['uid'] = $fan['uid']; goto nuF2z; KRADB: $_W['member']['uid'] = $uid; goto PFE9n; tl1Uz: CqWby: goto I8cQo; aXx0w: $uid = pdo_insertid(); goto KRADB; E5AlX: weKuR: goto iB9P_; VuRO8: goto Pon7q; goto kbFrd; Y9cEw: $setting = cache_load('unisetting:' . $_W['uniacid']); goto zwySq; PFE9n: $_W['fans']['uid'] = $uid; goto HE1QO; zwySq: if (empty($_W['member']['uid']) && empty($setting['passport']['focusreg'])) { goto isnxQ; } goto lzXG3; iB9P_: pdo_insert('mc_members', array("uniacid" => $_W['uniacid'])); goto aXx0w; HE1QO: pdo_update('mc_mapping_fans', array("uid" => $uid), array("fanid" => $fanid)); goto tl1Uz; kbFrd: isnxQ: goto QG2mQ; QG2mQ: $fan = pdo_get('mc_mapping_fans', array("uniacid" => $_W['uniacid'], "openid" => $_W['openid'])); goto AExW8; qTLi6: oc3zR: goto W6Lo_; nuF2z: $_W['fans']['uid'] = $fan['uid']; goto ie4Kq; ZwKsN: $post = array("acid" => $_W['acid'], "uniacid" => $_W['uniacid'], "openid" => $_W['openid'], "updatetime" => time(), "follow" => 0); goto Zo6eI; KFSKr: if (empty($fan['uid'])) { goto weKuR; } goto gk2fY; AExW8: if (!empty($fan)) { goto oc3zR; } goto ZwKsN; ie4Kq: goto CqWby; goto E5AlX; lzXG3: checkauth(); goto VuRO8; DiqeL: r1u5Q: goto KFSKr; I8cQo: Pon7q: goto GtRRS; W6Lo_: $fanid = $fan['fanid']; goto DiqeL; osh_v: global $_W; goto Y9cEw; GtRRS: } public function get_fields($fid) { global $_GPC, $_W; return pdo_get($this->tb_field, array("refid" => $fid), array()); } public function get_comment($commentid) { global $_GPC, $_W; return pdo_get('dayu_comment', array("weid" => $_W['uniacid'], "id" => $commentid), array()); } public function get_linkage($id, $type) { goto dK3Db; dK3Db: if ($type == 1) { goto bU3C9; } goto l1wZT; el2vx: goto RoT_Z; goto Q9yMv; Q9yMv: bU3C9: goto fpOHg; l1wZT: return pdo_fetch('SELECT * FROM ' . tablename($this->tb_linkage) . ' WHERE id = :id LIMIT 1', array(":id" => $id)); goto el2vx; vj6Fv: RoT_Z: goto SO0xh; fpOHg: return pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_linkage) . ' WHERE reid = :reid', array(":reid" => $id)); goto vj6Fv; SO0xh: } public function get_role($uid) { global $_GPC, $_W; return pdo_fetch('SELECT username, uid FROM ' . tablename('users') . ' WHERE uid = :uid LIMIT 1', array(":uid" => $uid)); } public function get_isrole($reid, $uid) { global $_GPC, $_W; return pdo_fetch('SELECT * FROM ' . tablename($this->tb_role) . ' WHERE weid = :weid AND reid = :reid AND roleid = :uid LIMIT 1', array(":weid" => $_W['uniacid'], ":reid" => $reid, ":uid" => $uid)); } public function doMobileUpthumb() { goto koLQp; BAWSz: $result['msg'] = '更新头像失败'; goto bRUDx; bRUDx: qvmqS: goto KIxhd; KIxhd: bmPL4: goto Xygwz; Xygwz: message($result, '', 'ajax'); goto bif2h; XhrDI: $result['msg'] = '更新头像成功'; goto sjT3g; VCFN0: $result['status'] = 'error'; goto BAWSz; sjT3g: goto qvmqS; goto jAT_9; h4HH3: if (!($mode == 'member')) { goto bmPL4; } goto n3jX1; koLQp: global $_W, $_GPC; goto ofokQ; vFbz1: $result['status'] = 'success'; goto XhrDI; JW6iL: if (mc_update($_GPC['uid'], $data) === false) { goto jcf36; } goto vFbz1; ofokQ: $mode = $_GPC['mode']; goto h4HH3; jAT_9: jcf36: goto VCFN0; Ptc2d: load()->model('mc'); goto JW6iL; n3jX1: $data = array("avatar" => $_GPC['thumb']); goto Ptc2d; bif2h: } public function doMobileUploadFiles() { goto g9Atr; g9Atr: global $_GPC, $_W; goto TljGt; FcZ6C: message($result, '', 'ajax'); goto j3h0k; Pn1dU: $result['msg'] = $pathname . '上传成功'; goto FcZ6C; TljGt: load()->func('file'); goto A67hy; A67hy: foreach ($_FILES as $key => $files) { goto DWTQA; R_7PA: $size = intval($files['size']); goto aEILk; vAHgM: $result['status'] = '0'; goto idQvl; syM2u: if (!($files['error'] != 0)) { goto qMHzF; } goto XEj58; yLUN1: message($result, '', 'ajax'); goto rNmC3; idQvl: $result['msg'] = '上传失败, 请选择要上传的文件！'; goto yLUN1; rNmC3: exit; goto KcfQZ; goTOg: $ext = strtolower($ext); goto R_7PA; jvu4L: if (!is_error($file)) { goto Qv3PD; } goto k7qPI; KcfQZ: kRJU2: goto syM2u; ePSv8: $pathname = $file['path']; goto C4W6s; dinV8: $file = file_upload($files); goto jvu4L; ukA2R: message($result, '', 'ajax'); goto KQdfC; k7qPI: $result['message'] = $file['message']; goto aqxq9; DWTQA: if (!empty($files['name'])) { goto kRJU2; } goto vAHgM; aEILk: $originname = $files['name']; goto dinV8; KQdfC: exit; goto ZMyXP; mxwxT: $result['msg'] = '上传失败, 请重试.'; goto ukA2R; C4W6s: PY5Nj: goto fDRe5; l4PHp: Qv3PD: goto ePSv8; XEj58: $result['status'] = '0'; goto mxwxT; aqxq9: die(json_encode($result)); goto l4PHp; j0K5h: $ext = pathinfo($files['name'], PATHINFO_EXTENSION); goto goTOg; ZMyXP: qMHzF: goto j0K5h; fDRe5: } goto EETdn; EETdn: XgTlW: goto vklie; vklie: $result['status'] = '1'; goto Pn1dU; j3h0k: } } goto VmX7D; e_1fv: function tpl_form_field_images($name, $value = "", $default = "", $options = array()) { goto V9POD; hZhD1: if (empty($value)) { goto nHvo9; } goto uQh6K; PbA1_: YzOSn: goto OXMPz; DVRGd: if (!empty($options['class_extra'])) { goto ofmDI; } goto QDRzE; SBgME: zAR1C: goto xrYhp; McTGD: exit('图片上传目录错误,只能指定最多两级目录,如: "store","store/d1"'); goto RvBXM; VcE7k: $options['global'] = false; goto RIkrd; RIkrd: goto hOdOu; goto PbA1_; LhZjv: if (!(isset($options['dest_dir']) && !empty($options['dest_dir']))) { goto ACICD; } goto ji97n; RvBXM: l4Yct: goto Ki3cL; of0fB: if (!empty($default)) { goto ovtem; } goto I7bdq; QDRzE: $options['class_extra'] = ''; goto qzz9K; Ki3cL: ACICD: goto YXQYd; Vy4Xz: $s .= '
		<div class="input-group ' . $options['class_extra'] . '">
			<input type="text" name="' . $name . '" value="' . $value . '"' . ($options['extras']['text'] ? $options['extras']['text'] : '') . ' id="re-image" class="form-control" autocomplete="off">
			<span class="input-group-btn">
				<button class="btn btn-default" type="button" onclick="showImageDialog(this);">选择图片</button>
			</span>
		</div>
		<div class="col-xs-12 ' . $options['class_extra'] . '" style="margin-top:.5em;">
			<em class="close" style="position:absolute; top: 0px; right: -14px;font-size:18px;color:#333;" title="删除这张图片" onclick="deleteImage(this)">× 删除</em>
		</div>'; goto pQtSa; uQh6K: $val = tomedia($value); goto nD8Dw; LRj_i: $options['thumb'] = !empty($options['thumb']); goto SBgME; pQtSa: return $s; goto kMfDD; OXMPz: $options['global'] = true; goto Ra9g3; r08fA: $val = $default; goto hZhD1; YXQYd: $options['direct'] = true; goto y5Ymi; ji97n: if (preg_match('/^\\w+([\\/]\\w+)?$/i', $options['dest_dir'])) { goto l4Yct; } goto McTGD; YHi6Z: ovtem: goto r08fA; DZlc9: define('TPL_INIT_IMAGE', true); goto eTvKr; qzz9K: ofmDI: goto LhZjv; nD8Dw: nHvo9: goto AV044; qgM85: $s = '
		<script type=\"text/javascript\">
			function showImageDialog(elm, opts, options) {
				require([\"util\"], function(util){
					var btn = $(elm);
					var ipt = btn.parent().prev();
					var val = ipt.val();
					var img = ipt.parent().next().children();
					options = ' . str_replace('"', '\'', json_encode($options)) . ';
					util.image(val, function(url){
						if(url.url){
							if(img.length > 0){
								img.get(0).src = url.url;
							}
							ipt.val(url.attachment);
							ipt.attr(\"filename\",url.filename);
							ipt.attr(\"url\",url.url);
						}
						if(url.media_id){
							if(img.length > 0){
								img.get(0).src = \"\";
							}
							ipt.val(url.media_id);
						}
					}, null, options);
				});
			}
			function deleteImage(elm){
				require([\"jquery\"], function($){
					$(elm).prev().attr(\"src\", \"./resource/images/nopic.jpg\");
					$(elm).parent().prev().find(\"input\").val(\"\");
				});
			}
		</script>'; goto DZlc9; AV044: if (!empty($options['global'])) { goto YzOSn; } goto VcE7k; I7bdq: $default = './resource/images/nopic.jpg'; goto YHi6Z; xrYhp: $s = ''; goto qnWuE; hsXA7: if (!isset($options['thumb'])) { goto zAR1C; } goto LRj_i; V9POD: global $_W; goto of0fB; y5Ymi: $options['multiple'] = false; goto hsXA7; Ra9g3: hOdOu: goto DVRGd; eTvKr: tyVyI: goto Vy4Xz; qnWuE: if (defined('TPL_INIT_IMAGE')) { goto tyVyI; } goto qgM85; kMfDD: } ?>