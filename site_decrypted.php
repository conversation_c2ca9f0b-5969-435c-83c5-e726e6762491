<?php
DOjgm: defined('IN_IA') or exit('Access Denied');pfjif: function dayu_form_field_district($name, $values = array()) {N7F__: ssKR9:hY2X3: CuKdn:XSzCt: $html .= '
<div class="tpl-district-container" style="display: block;">
<div class="col-lg-4">
<select name="' . $name . '[province]" data-value="' . $values['province'] . '" class="tpl-province">
</select><i></i>
</div>
<div class="col-lg-4">
<select name="' . $name . '[city]" data-value="' . $values['city'] . '" class="tpl-city">
</select><i></i>
</div>
<div class="col-lg-4">
<select name="' . $name . '[district]" data-value="' . $values['district'] . '" class="tpl-district">
</select><i></i>
</div>
</div>';GC2eX: $values = array("province" => "", "city" => "", "district" => "");GwTlp: $values['city'] = '';gyVvU: if (!empty($values['province'])) {}Tmob9: if (!empty($values['city'])) {}zdNB7: $html .= '
<script type=\"text/javascript\">
require([\"jquery\", \"district\"], function($, dis){
    $(\".tpl-district-container\").each(function(){
        var elms = {};
        elms.province = $(this).find(\".tpl-province\")[0];
        elms.city = $(this).find(\".tpl-city\")[0];
        elms.district = $(this).find(\".tpl-district\")[0];
        var vals = {};
        vals.province = $(elms.province).attr(\"data-value\");
        vals.city = $(elms.city).attr(\"data-value\");
        vals.district = $(elms.district).attr(\"data-value\");
        dis.render(elms, vals, {withTitle: true});
    });
});
</script>';HO0zO: define('TPL_INIT_DISTRICT', true);ZepCg: qn8Yb:xlvPz: if (defined('TPL_INIT_DISTRICT')) {}UHmnS: $values['province'] = '';qoSSl: $values['district'] = '';QaFqG: if (!empty($values['district'])) {}RasYa: if (!(empty($values) || !is_array($values))) {}AI3hM: return $html;lk9eZ: $html = '';h4jEE: kUoq0:ABbff: Ja3Nu:KTF52: }jgy4g: function tpl_form_field_images2($name, $value, $title) {I77Mf: $thumb = empty($value) ? 'images/global/nopic.jpg' : $value;UAGHo: return $html;yx5EC: $thumb = tomedia($thumb);k0fKS: $html = "	<li class=\"mui-table-view-cell mui-media mui-col-xs-6\">
<a href=\"javascript:;\" class=\"js-image-{$name}\">
<span class=\"js-image-{$name}s\"><img class=\"mui-media-object\" src=\"{$thumb}\"></span>
<div class=\"mui-media-body\">
<input type=\"hidden\" id=\"{$name}\">
<input class=\"weui_uploader_input\" type=\"file\" name=\"{$name}\" accept=\"image/*\" capture=\"camera\" value=\"{$title}\"></div>
</a>
</li>
<script>
util.image(\$('.js-image-{$name}'), function(url){
    \$('.js-image-{$name}').prev().val(url.attachment);
    \$('.js-image-{$name}s').find('img').attr('src',url.url);
}, {
    crop : false,
    multiple : false
});
</script>";EF1yy: }g_ZGE: define('TEMPLATE_WEUI', '../addons/dayu_form/template/weui/');NDRzF: define('TEMPLATE_PATH', '../addons/dayu_form/template/style/');abxj_: define('MODULE_NAME', 'dayu_form');K5UGc: require IA_ROOT . '/addons/dayu_form/inc/func/core.php';VPssP: function notice_init() {cox_d: hbXuR:lswEb: return error(-1, '创建公众号操作对象失败');xanLw: if (!is_null($acc)) {}xISUU: $acc = WeAccount::create();gv3fk: global $_W;lTyXO: return $acc;QOVxS: }VmX7D: function dayu_fans_form($field, $value = "") {RSsTT: switch ($field) { case 'reside': case 'resideprovince': case 'residecity': case 'residedist': $html = dayu_form_field_district('reside', $value);}ucoQR: RBBxQ:M8cZe: return $html;ie3Rk: kTJda:W4uYv: }n8l9n: class dayu_formModuleSite extends Core { function __construct() {QM0EC: $this->_openid = $_COOKIE[$this->_auth2_openid];XdQBX: NyO3a:mqLVW: $this->_appid = $this->_account['key'];LHxju: $this->_appid = $_W['account']['key'];J6IVY: $oauth = $settings['oauth'];qm3Bx: $this->_appsecret = $_W['account']['secret'];JDiZA: if (!(!empty($oauth) && !empty($oauth['account']))) {}N01b6: $this->_appid = $_W['account']['key'];kkObs: $this->_accountlevel = $account['level'];CJ3KY: QMNpf:WF_jD: $this->_auth2_headimgurl = 'auth2_headimgurl_' . $_W['uniacid'];IAkmL: $this->_appsecret = $_W['account']['secret'];yHL13: H6Wu5:XJvAs: $this->_auth2_openid = 'auth2_openid_' . $_W['uniacid'];b9uu3: load()->model('mc');qw6B2: $this->_appsecret = $this->_account['secret'];YW4wT: $this->_auth2_nickname = 'auth2_nickname_' . $_W['uniacid'];HFXH7: $account = $_W['account'];tz30x: TkrEQ:iD42H: global $_W, $_GPC;F09Cd: $this->_weid = $_W['uniacid'];nKC7X: $this->_account = account_fetch($oauth['account']);Z8e_s: if (!isset($_COOKIE[$this->_auth2_openid])) {}xZVGX: $this->_openid = $_W['openid'];S3D0Y: if ($this->_accountlevel < 4) {}P2HJZ:VfHKo: $settings = uni_setting($this->_weid);hMg_t: } public function oauth2($url) {uJMxU: header("location:{$oauth2_code}");edor3: lShLL:yfPzC: global $_GPC, $_W;h1kU3: $userinfo = $this->get_User_Info($from_user);qxSua: $code = $_GPC['code'];pkOFe: if (!(empty($userinfo) || !is_array($userinfo) || empty($userinfo['openid']) || empty($userinfo['nickname']))) {}WThdN: return $userinfo;qBYxA: $this->showMessage('code获取失败.', '', '', '', '');bx4F0: setcookie($this->_auth2_nickname, $userinfo['nickname'], time() + 3600 * 24);j5wk9: mTL4n:D_54K: $state = 0;QhWJe: $from_user = $token['openid'];cqiX_: $state = 1;r3XX5: jkBf1:u71oP: $token = $this->get_Authorization_Code($code, $url);bnmKN: echo '<h1>获取微信公众号授权失败[无法取得粉丝信息], 请稍后重试！ 公众平台返回原始数据: <br />' . $state . $userinfo['meta'] . '<h1>';AXOCg: setcookie($this->_auth2_headimgurl, $userinfo['headimgurl'], time() + 3600 * 24);PopJa: setcookie($this->_auth2_sex, $userinfo['sex'], time() + 3600 * 24);u_PMU: $oauth2_code = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' . $this->_appid . '&redirect_uri=' . urlencode($url) . '&response_type=code&scope=snsapi_userinfo&state=0#wechat_redirect';iCZI_: exit;U7rZY: if (!($authkey == 0)) {}u78Mh: $userinfo = $this->get_User_Info($from_user, $token['access_token']);bT7ks: setcookie($this->_auth2_openid, $from_user, time() + 3600 * 24);fMwqa: if (!empty($code)) {}pQvzD: load()->func('communication');aZpgI: Hr04I:Cv3gH: $authkey = intval($_GPC['authkey']);SfTPk: if (!($userinfo['subscribe'] == 0)) {}AoOju: } public function get_Access_Token() {MKZfn: load()->classs('weixin.account');MMvzX: return $access_token;ddHGj: $account = $_W['account'];qTN6N: $account = $this->_account;NooK1: global $_W;ZsIHe: $access_token = $accObj->fetch_token();Yug3D: if (!($this->_accountlevel < 4)) {}e0zEs: if (empty($this->_account)) {}E8BTV: qn0sx:vcvd1: $accObj = WeixinAccount::create($account['acid']);IYxYD: Yxjye:NYCiv: } public function get_Authorization_Code($code, $url) {qFpRt: echo '微信授权失败! 公众平台返回原始数据: <br>' . $error['meta'];jC4RK: $oauth2_code = "https://api.weixin.qq.com/sns/oauth2/access_token?appid={$this->_appid}&secret={$this->_appsecret}&code={$code}&grant_type=authorization_code";nly5e: wb7MD:x8XcK: header("location:{$oauth2_code}");EF1b6: $error = ihttp_get($oauth2_code);BRnEj: $token = @json_decode($error['content'], true);xh8Dr: exit;SIYSD: if (!(empty($token) || !is_array($token) || empty($token['access_token']) || empty($token['openid']))) {}Khoxt: return $token;mMwRp: $oauth2_code = $url;boaWc: } public function get_User_Info($from_user, $ACCESS_TOKEN = "") {kEA3T: $json = ihttp_get($url);Flyxf: $url = "https://api.weixin.qq.com/sns/userinfo?access_token={$ACCESS_TOKEN}&openid={$from_user}&lang=zh_CN";KGJ1P: if ($ACCESS_TOKEN == '') {}vPQpB: fzOue:B0Rto:J3PFF: return $userinfo;OHXu8: $userinfo = @json_decode($json['content'], true);stkQ6: $url = "https://api.weixin.qq.com/cgi-bin/user/info?access_token={$ACCESS_TOKEN}&openid={$from_user}&lang=zh_CN";pD1rS: JyyC0:gYeCQ: $ACCESS_TOKEN = $this->get_Access_Token();Fb1hF: } public function get_Code($url) {t2fG5: global $_W;eOXn5: header("location:{$oauth2_code}");FB_a9: $url = urlencode($url);XYg5M: $oauth2_code = "https://open.weixin.qq.com/connect/oauth2/authorize?appid={$this->_appid}&redirect_uri={$url}&response_type=code&scope=snsapi_base&state=0#wechat_redirect";M4W4K: } public function getHomeTiles() {Gdt2A: if (empty($list)) {}W6nmX: global $_W;ULHkK: return $urls;hV2xE: XrTIs:pLT5s: $list = pdo_fetchall('SELECT title, reid FROM ' . tablename($this->tb_form) . " WHERE weid = '{$_W['uniacid']}'");zlLn_: $urls = array();OsJ_h: foreach ($list as $row) { $urls[] = array("title" => $row['title'], "url" => $_W['siteroot'] . 'app/' . $this->createMobileUrl('dayu_form', array("id" => $row['reid']))); qKCWT: }ZHXEi: D9FCt:eeyCz: } public function __call($name, $arguments) {Eqv4Y: CEm3f:Ox3ry: $fromurl = urldecode($_GPC['fromurl']);EH6jC: avFb2:BodXx: Eqt0J:iEdNI: $avatar = $_W['attachurl'] . 'images/global/noavatar_middle.gif';VEigm: $isMobile = stripos($name, 'doMobile') === 0;TYrk2: if (!empty($profile['avatar'])) {}ndvL1: $fun = strtolower(substr($name, 5));xFvpp: $id = $_GPC['id'];RvKQo: $dir .= 'web/';obLGJ: $op = $operation = trim($_GPC['op']) ? trim($_GPC['op']) : 'display';pmHpW:MULPF: require MODULE_ROOT . '/fans.web.php';EQJCd: $dayuset = $this->module['config'];HdOJa: $avatar = $fans['tag']['avatar'];x8E1f: if (tomedia('headimg_' . $_W['acid'] . '.jpg')) {}J2HIi: if (!empty($fans['tag']['avatar'])) {}q103D: $file = $dir . $fun . '.php';Fx0B4: return null;tURsu: if (!$isMobile) {}Ur9WS: iHosc:oBQPw: if (!file_exists($file)) {}MX_53: $fun = strtolower(substr($name, 8));PfL63: PCp8T:LQtI_: $dir = MODULE_ROOT . '/inc/';NU01K: require MODULE_ROOT . '/fans.mobile.php';L1omZ: if (!$isWeb) {}YK01T: exit;hVDpR: $avatar = $profile['avatar'];g4_Z9: $avatar = tomedia('headimg_' . $_W['acid'] . '.jpg');Vuzst: if (!($isWeb || $isMobile)) {}qfjBc:t2fSh: $dir .= 'mobile/';GtQah: load()->func('tpl');O02Vv:iQFH5: z610u:hjgrW: require $file;GxWzj: trigger_error("访问的方法 {$name} 不存在.", E_USER_WARNING);vQsZY: AzwdZ:v155O: $weid = $_W['uniacid'];BsduP: G9c22:d_Tc3: $isWeb = stripos($name, 'doWeb') === 0;oagLP: $returnUrl = urlencode($_W['siteurl']);cjoQH: uPGza:IW3BF: global $_W, $_GPC;cQinK: } public function doWebFansSearch() {r6zm2: $params[':nickname'] = "%{$kwd}%";mNSle: include $this->template('fanssearch');Xt9bW: $sql = 'SELECT * FROM ' . tablename('mc_mapping_fans') . " WHERE {$where} ORDER BY fanid DESC LIMIT 20";chyP5: $pindex = max(1, intval($_GPC['page']));uSV_0: $psize = 20;RFGv2: $params[':uniacid'] = $_W['uniacid'];D8ez0: $where = 'uniacid = :uniacid AND `nickname` LIKE :nickname';vB101: $boss = pdo_fetchall($sql, $params);lAVH7: load()->model('mc');XCFlc: global $_W, $_GPC;pj4FH: foreach ($boss as &$row) {SVWSQ: $row['fans'] = mc_fansinfo($row['openid'], $_W['uniacid']);yd1ta: $r['openid'] = $row['openid'];G54AO: $row['entry'] = $r;j6Bp2: $r['follow'] = $row['follow'];GTAex: $r['fanid'] = $row['fanid'];rxqwp: ixkS7:TqNVI: $r = array();OYSb1: $r['nickname'] = $row['nickname'];GALxx: }JpOLY: ifDRH:ZKQu4: $kwd = $_GPC['keyword'];EZ7_U: } public function doWebQuery() {NEgHR: $ds = pdo_fetchall($sql, $params);Zo0b6: $kwd = $_GPC['keyword'];UIlTs: $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `title` LIKE :title ORDER BY reid DESC LIMIT 0,8';y8wTs: global $_W, $_GPC;FGAV9: foreach ($ds as &$row) {KtdZL: $r['thumb'] = $row['thumb'];Eng7o: $r = array();EIVus: $row['entry'] = $r;MSACE: $r['title'] = $row['title'];A0tW7: $r['description'] = cutstr(strip_tags($row['description']), 50);qbnqd: c6J0K:dpXwQ: $r['reid'] = $row['reid'];DKJE1: }jKb1o: $params[':weid'] = $_W['uniacid'];onJS6: CG7Xc:ancYI: $params[':title'] = "%{$kwd}%";jF2l0: $params = array();tZPX2: include $this->template('query');dMwlz: } public function doWebLinkage() {KPV2y: $linkage = pdo_fetch('SELECT * FROM ' . tablename($this->tb_linkage) . " WHERE id = '{$id}'");CFQLI: message('更新联动成功！', $this->createWebUrl('linkage', array("op" => "display", "reid" => $reid)), 'success');xcD3_: if (!empty($parent)) {}RKRkU: $id = intval($_GPC['id']);bwQWb: $children = array();JMigh: aawpw:KTz1s: $data = array("reid" => $reid, "title" => $_GPC['title'], "parentid" => intval($parentid), "displayorder" => intval($_GPC['displayorder']));OZxJR: Ww6Pd:K5KVU: $record['linkage'] = iserializer($data);ZL4zJ:aTblx: message('您没有权限进行该操作.');ULCXn: foreach ($_GPC['displayorder'] as $id => $displayorder) { pdo_update($this->tb_linkage, array("displayorder" => $displayorder), array("id" => $id)); XyPAM: }Qpysq: TNEFW:yCEfH: if ($operation == 'post') {}XRA20: $linkage = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_linkage) . " WHERE reid = '{$reid}' ORDER BY parentid ASC, displayorder desc");zlV98: message('保存成功', $this->createWebUrl('linkage', array("op" => "display", "reid" => $reid)), 'success');vCVRS: pdo_update($this->tb_linkage, $data, array("id" => $id));DnqiF: FDLje:k5_1F: F7mLC:wFSzY: itoast('联动删除成功！', referer(), 'success');KN3D6: Ado1Q:y6Jzh: message('抱歉，请输入联动标题！');TuF99: $linkage = array("displayorder" => 0);nyg5b: hzDLw:ymeio: message('联动排序更新成功！', $this->createWebUrl('linkage', array("op" => "display", "reid" => $reid)), 'success');ij8uL:AV6Tr: $reid = intval($_GPC['reid']);B9lzW:bilrt: eJy_x:S7bNp: C1_Kw:hLFce: $parent = pdo_fetch('SELECT id, title FROM ' . tablename($this->tb_linkage) . " WHERE id = '{$parentid}'");ClSaU: if (!empty($linkage)) {}BoCgx: global $_GPC, $_W;CNR3t: SFMda:t1u9j: $id = intval($_GPC['id']);j_pVy: if (!empty($id)) {}VCyUt:EOB_e: $id = pdo_insertid();Cv1zm: pdo_delete($this->tb_linkage, array("id" => $id));UBguX: if ($operation == 'delete') {}sQXt_: xqEXr:syU2M: Iniz9:s4IHY: if (!checksubmit('paixu')) {}PZRpP: foreach ($linkage as $index => $item) {VodSO: $children[$item['parentid']][] = $item;m0y3e: ANk71:dZGin: Qhufw:Gyh82: if (empty($item['parentid'])) {}mKvbD: unset($linkage[$index]);zamr5: }Xik5L: $record = array();jXLfI: pdo_update($this->tb_form, $record, array("reid" => $reid));w4FUG: if (!($setting['role'] == 1 && $_W['role'] == 'operator' && !$role)) {}aYSSz: if (!checksubmit('submit')) {}fQXx2: $operation = !empty($_GPC['op']) ? $_GPC['op'] : 'display';wDL4I: if (!checksubmit('submit')) {}tBW91: tzi4N:VP6xL: include $this->template('linkage');rkAXl: $data = array("l1" => $_GPC['la1'], "l2" => $_GPC['la2']);qDcCm: itoast('抱歉，联动不存在或是已经被删除！', referer(), 'error');q76M4: $parentid = intval($_GPC['parentid']);XT7ZJ: $linkage = pdo_fetch('SELECT * FROM ' . tablename($this->tb_linkage) . " WHERE id = '{$id}'");YbJ0r: aQmFG:NN0Fh: $role = $this->get_isrole($reid, $_W['user']['uid']);qqamk: include $this->template('linkage');fekoc: $la = iunserializer($activity['linkage']);lRQB0: require MODULE_ROOT . '/fans.web.php';LjZuQ: load()->func('tpl');YZnwY: message('抱歉，上级联动不存在或是已经被删除！', $this->createWebUrl('linkage', array("op" => "post", "reid" => $reid)), 'error');dMFe1: HI8v4:fA2rt: if (!empty($_GPC['title'])) {}u7X7X: unset($data['parentid']);Fjnjq: if (!empty($id)) {}uhun3:e3NkR: if ($operation == 'display') {}TXcP9: if (empty($parentid)) {}Ky4Xo: $activity = pdo_get($this->tb_form, array("weid" => $_W['uniacid'], "reid" => $reid), array("title", "linkage"));RBUf9: RBZtx:YkEQt: pdo_insert($this->tb_linkage, $data);R_Ccs: hrCxF:zCAHd: h12Mr:ZeeMC: } public function doMobileGetLinkage() {HXXLS: message($result, '', 'ajax');zf5y1: message($result, '', 'ajax');RBXY6: $result['status'] = 1;W6JZi: $result['status'] = 0;T1y7w: $result['jss'] = '没有下级内容';mlQH1: ogVVL:TkPiF: if (!empty($jss)) {}NV7Nu: $result['jss'] = $jss;nrjBn: $jss = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_linkage) . ' WHERE parentid = :parentid ORDER BY displayorder desc, id DESC', array(":parentid" => $_GPC['linkage1']));Sg6zb: global $_GPC, $_W;woZGx: } public function doWebStaff() {qRssl: $total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_staff) . ' WHERE ' . $where, $params);qFN_y: yvNSK:q5u3Z: bwE7o:pak_O: B8yGG:TrW60: wTzSI:yTwHe: if ($op == 'post') {}RR6Kq: iB73h:xmG6N: if (!($setting['role'] == 1 && $_W['role'] == 'operator' && !$role)) {}EEoBa: foreach ($_GPC['ids'] as $k => $v) {fzDe8: $data = array("nickname" => trim($_GPC['nickname'][$k]), "openid" => trim($_GPC['openid'][$k]), "weid" => trim($_GPC['weid'][$k]));fwbKX: pdo_update('dayu_form_staff', $data, array("reid" => $reid, "id" => intval($v)));OwpAR: KxZMn:szEmn: }rnk92: include $this->template('staff');vJjU1: $data['nickname'] = $_GPC['nickname'];lIaug: $params[':reid'] = $reid;uehY1: message('表单不存在或已删除', $this->createWebUrl('display'), 'error');EJSBV: $pager = pagination($total, $pindex, $psize);sbrGT: if (!checksubmit('submit')) {}On3yp: include $this->template('staff');YKc83: if ($op == 'list') {}PXbwX: $data['openid'] = $_GPC['openid'];dB9mV: itoast('编辑成功', $this->createWebUrl('staff', array("op" => "list", "reid" => $reid)), 'success');KVErX:cto_y: bMqPH:Sslt2:xLZeB: message('您没有权限进行该操作.');EPLZb: $data['reid'] = $reid;SyWy6: Il5_H:cG7Q4: itoast('删除成功.', referer());Q2eP5: if (empty($_GPC['keyword'])) {}eQmYz: if ($op == 'staffdel') {}esD5U: global $_W, $_GPC;y_M2B: if (!checksubmit('submit')) {}bIj3b: $where .= " AND nickname LIKE '%{$_GPC['keyword']}%'";c2b2Y: jfdEu:SrXs9: $weid = $_W['uniacid'];IbHvZ:n8gHi: $op = trim($_GPC['op']) ? trim($_GPC['op']) : 'list';QiHe9: if (empty($_GPC['ids'])) {}qPSFb: require MODULE_ROOT . '/fans.web.php';K_FkG: n472w:OZv3H: if (empty($id)) {}iJIG6: $lists = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_staff) . ' WHERE ' . $where . ' ORDER BY createtime DESC,id ASC LIMIT ' . ($pindex - 1) * $psize . ',' . $psize, $params, 'id');DJm5u: $psize = 20;xHE6E: RjKU6:FwK7A: $data['weid'] = $_GPC['weid'];yGU2m: pdo_insert('dayu_form_staff', $data);bA42a: $data['createtime'] = time();PENgf: $where = ' reid = :reid';DG7wU: zXOgf:edmja: $reid = intval($_GPC['reid']);bhQ5c: if (!empty($activity)) {}cYXSM: sGfuZ:dlaU7: itoast('添加客服成功', $this->createWebUrl('staff', array("reid" => $reid, "op" => "list")), 'success');Aa5AX: pdo_delete('dayu_form_staff', array("id" => $id));PkR3z: $activity = $this->get_form($reid);gPz4H: $role = $this->get_isrole($reid, $_W['user']['uid']);xAJnR: $pindex = max(1, intval($_GPC['page']));Y92tc: $id = intval($_GPC['id']);cSZHC: } public function doWebchangecheckedAjax() {YU3Sa: exit('0');yc7yF: message('您没有权限进行该操作.');eBe9Q: if (!($setting['role'] == 1 && $_W['role'] == 'operator' && !$role)) {}PO1Rl: $change = $_GPC['change'];BcxrL: BpKzj:NZh6k: if (false !== pdo_update($this->tb_form, array($field => $change), array("reid" => intval($id), "weid" => $_W['uniacid']))) {}RlvWu: IJhnf:viNUb: require MODULE_ROOT . '/fans.web.php';G9bDS: hXGN2:zOfdM: $id = $_GPC['id'];k2M0I: exit('1');nglW6: global $_W, $_GPC;tRP5E: $role = $this->get_isrole($id, $_W['user']['uid']);gvzEQ: $field = $_GPC['field'];jfSqU:NsCIv: } public function doWebEditkf() {wA7Qn: exit;y97eW: CRlPN:oK00V: global $_W, $_GPC;G50eU: $openid = $_GPC['openid'];Ua3Of: $nickname = $_GPC['nickname'];a0I8p: $fff = pdo_fetchall('SELECT reid,title FROM ' . tablename($this->tb_form));gbvMk: include $this->template('kf_edit');y8jA7: if (!is_array($reid)) {}ObmsA: if (!($setting['role'] == 1 && $_W['role'] == 'operator' && !$role)) {}SN0_1: message('更改成功!', referer());sTpB8: xw6vD:niWM1: $role = $this->get_isrole($reid, $_W['user']['uid']);TdzaK: $fun = explode(',', $config['reid']);uurJP: J_bj7:cTQoT: kFakg:mbmbu: require MODULE_ROOT . '/fans.web.php';yK07q: $config = pdo_fetch('SELECT * from ' . tablename($this->tb_staff) . ' where id=' . $_GPC['id']);DaRTm: $actid = substr($actid, 0, strlen($actid) - 1);lV5y4: $reid = $_GPC['reid'];CkRzY: if (!($_GPC['dopost'] == 'update')) {}FV_iZ: message('您没有权限进行该操作.');WspDI: foreach ($reid as $k => $v) { $actid = $v . ','; Pl4eF: }lX7iA: $a = pdo_update('dayu_form_staff', array("reid" => $actid, "nickname" => $nickname, "openid" => $openid), array("id" => $_GPC['id']));USGI3: } public function doWebDetail() {vCdfm: lPhnO:BT2Es: mc_group_update(mc_openid2uid($row['openid']));Gfsxm: BIrXt:dDhDV: mExKV:aShiO: $sms_data = array("mobile" => $row['mobile'], "title" => $activity['title'], "mname" => $row['member'], "mmobile" => $row['mobile'], "openid" => $row['openid'], "status" => $state['name'], "ptname" => $_W['account']['name'], "addons" => $_W['current_module']['name'], "content" => $alldata);Semvq: $row = pdo_fetch($sql, $params);kcelK: $linkage['l1'] = $this->get_linkage($linkage['l1'], '');QBV38: $kami = pdo_get('dayu_sendkami', array("weid" => $_W['uniacid'], "id" => $row['kid']), array());a42BC: message('访问非法.');AtzFV: $huifu = $state['name'] . $kfinfo . $revoice;iDM9Q: $acc = WeAccount::create($_W['acid']);MIq9v: $kami = pdo_get('dayu_kami', array("weid" => $_W['uniacid'], "id" => $row['kid']), array());Fltq6: $info .= "<a href='{$url}'>现在去评价</a>";wHdNe: $record['icredit'] = 1;IsRTf: H270z:bERmg: $linkage = iunserializer($row['linkage']);y5Vfz: if (!is_array($_GPC['rethumb'])) {}K7MGc: itoast('修改成功', referer(), 'success');aphJp: if (!empty($activity)) {}JkLAN: if (!(pdo_tableexists('dayu_comment') && !empty($par['comment']) && empty($row['commentid']) && $_GPC['status'] == '3')) {}f9BVL: $acc = notice_init();wg1cF: $kfinfo = !empty($_GPC['kfinfo']) ? '
客服回复：' . $_GPC['kfinfo'] : '';p47tc: foreach ($wxcard as &$val) { $val['coupon'] = pdo_get('coupon', array("uniacid" => $_W['uniacid'], "card_id" => $val['cardid']), array("id", "title")); yGly8: }xjCi0: $thumb1 = unserialize($row['rethumb']);N7xxs: $_W['page']['title'] = $activity['title'] . ' 表单详情';rETyQ: if (!($row['icredit'] != '1' && $par['icredit'] == '1' && $activity['credit'] != '0.00' && $_GPC['status'] == '3')) {}XayEm: i08TZ:WoK5A: svgow:Bkg4T: $testfile = $_FILES['upfile'];Qsam2: $info = '【您好，受理结果通知】
';cUYk8: $row['user'] = mc_fansinfo($row['openid'], $acid, $weid);ctE4j: twNFp:dcPsD: $status = $this->get_status($row['reid'], $row['status']);o_8QI: $record['yuyuetime'] = strtotime($_GPC['yuyuetime']);YVGGN: VQ2pC:xIipI: llV8T:btlfk: $data = array("first" => array("value" => $activity['mfirst'] . '
', "color" => "#743A3A"), "keyword1" => array("value" => $row['member']), "keyword2" => array("value" => $row['mobile']), "keyword3" => array("value" => $_GPC['yuyuetime']), "keyword4" => array("value" => $huifu), "remark" => array("value" => '
' . $activity['mfoot'], "color" => "#008000"));dFtfD: if (!(pdo_tableexists('dayu_sendkami') && !empty($par['sendkami']) && $row['kid'])) {}tm06O: $record['status'] = intval($_GPC['status']);NxOM2: YgnZI:k4LrH: load()->func('communication');YMD82: foreach ($_GPC['rethumb'] as $thumb) { $th[] = tomedia($thumb); AqYCz: }WR2gJ: $info .= "<a href='{$url}'>点击查看详情</a>";cTb05: $log = $activity['title'] . '-' . $activity['credit'] . '积分';pHp7G: $settings = uni_setting($_W['uniacid'], array("creditnames", "creditbehaviors"));Kf5FY: $state = $this->get_status($row['reid'], $_GPC['status']);jQkX_: $row['file'] = iunserializer($row['file']);Y0fAA: $role = $this->get_isrole($row['reid'], $_W['user']['uid']);w1M7S: global $_W, $_GPC;wqaAT: $acc->sendTplNotice($row['openid'], $activity['m_templateid'], $data, $outurl, '#FF0000');nN8Oh: $custom = array("msgtype" => "text", "text" => array("content" => urlencode($info)), "touser" => $row['openid']);hQvXz: $outurl = !empty($par['noticeurl']) ? $par['noticeurl'] : $_W['siteroot'] . 'app/' . $this->createMobileUrl('mydayu_form', array("op" => "detail", "rerid" => $rerid, "id" => $row['reid']));rB_zc: if (!($setting['role'] == 1 && $_W['role'] == 'operator' && !$role)) {}t48vh: $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `reid`=:reid';WGIlm: if (!($_GPC['status'] == '3' && $par['icredit'] == '1')) {}Lu78a: S_rQO:K8RDX: message('发送失败，原因为' . $status['message']);OLk2P: $activity = pdo_fetch($sql, $params);e4450: $CustomNotice = $acc->sendCustomNotice($custom);OSnk_: dQ9yl:lzdJB: $custom = array("msgtype" => "text", "text" => array("content" => urlencode($info)));tL8M6: $acc = WeAccount::create($_W['acid']);SFZIo: foreach ($thumb1 as $p) { $rethumb[] = is_array($p) ? $p['attachment'] : $p; teazr: }tAPFr: yEhtX:XGmlM: return error(-1, $acc['message']);r_KS6: $linkage['l2'] = $this->get_linkage($linkage['l2'], '');cXpfT: $params = array();dOe6Y: oHvO2:pxnzR: $wxcard = pdo_fetchall('SELECT * FROM ' . tablename('dayu_wxcard') . ' WHERE weid = :weid AND cid=:cid AND status=1', array(":weid" => $_W['uniacid'], ":cid" => $par['wxcard']));jmXP0: $row['thumb'] = iunserializer($row['thumb']);xonSN: $acc = WeAccount::create($_W['acid']);WIaLJ: mc_credit_update(mc_openid2uid($row['openid']), $behavior['activity'], $activity['credit'], array(0, $activity['title']));ENhe8: $formdata = $this->order_foreach($row['reid'], $rerid);BrSvr: $url = $outurl;ysQ7m: $record = array();MD5VY: if (!(pdo_tableexists('dayu_kami') && !empty($par['kami']) && $row['kid'])) {}xc89V: $status = $this->get_status($reid, $_GPC['status']);TFs0K: $settings = $this->module['config'];App3_: $params[':weid'] = $_W['uniacid'];s3DTN: $row['member'] = !empty($row['member']) ? $row['member'] : $row['user']['nickname'];y8nZV: VnOTI:OrTAL: if (!($activity['custom_status'] == 1)) {}rlLBy: $msg = '';hPIgW: if (!is_array($linkage)) {}gAc0r: if (!checksubmit('submit')) {}wmQB0: $info .= "{$par['commenttitle']}
";DtgJy: $row['yuyuetime'] && ($row['yuyuetime'] = date('Y-m-d H:i:s', $row['yuyuetime']));b5XAL: foreach ($_GPC['file'] as $file) { $th[] = $file; t4ScS: }vStKB: $info .= "姓名：{$row['member']}
手机：{$row['mobile']}
受理结果：{$huifu}
";tYI9A: $la = iunserializer($activity['linkage']);DtlR6: if (!(!empty($par['wxcard']) && !empty($_GPC['wxcard']) && pdo_tableexists('dayu_wxcard'))) {}mT9SB: $params = array();BFaaA: ihttp_post(murl('entry', array("do" => "Notice", "id" => $activity['smstype'], "m" => "dayu_sms"), true, true), $sms_data);wCMS3: $CustomNotice = $acc->sendCustomNotice($custom);nB5cb: RNPvx:lkVOu: m3Mhn:ptvV2: $sql = 'SELECT * FROM ' . tablename($this->tb_info) . ' WHERE `rerid`=:rerid';iSF8m: if (!($activity['custom_status'] == '0' && !empty($activity['m_templateid']))) {}ArtLW: if (!is_error($status)) {}pzIPi: $state = array();Yf6AI: RxEcM:UZ2cG: if (!(!empty($par['comment']) && pdo_tableexists('dayu_comment') && !empty($row['commentid']))) {}F67In: $info = $activity['title'] . ' 处理结果 ' . $status['name'] . '
';p6THv: message('非法访问.');tUXAg: $msg .= $wxcard_post['msg'];KxzoL: message('您没有权限进行该操作.');ZCEe2: load()->func('file');PWVpC: $wxcard_post = ihttp_post(murl('entry', array("do" => "recorded", "couponid" => $_GPC['wxcard'], "m" => "dayu_wxcard"), true, true), array("addons" => $_W['current_module']['name'], "openid" => $row['openid'], "infoid" => $rerid));E7jG5: $rethumb = array();NZZq1: $record['rethumb'] = iserializer($th);YOT2j: G82K_:G3lPp: $alldata = array();Rv817: if (!is_array($thumb1)) {}wTgY5: if (empty($_GPC['file'])) {}rFItb: QTSEV:eDNHj: p1CXu:lqUQ5: $record['file'] = iserializer($th);b0yUF: bZLF7:zBNUp: tae7f:xI3UC: foreach ($formdata as $index => $v) {Sjz7H: HsjAV:uVXq0: $alldata[] = $v['title'] . ':' . $v['data'] . ',';garIX: $formdata[$index]['data'] .= $fdata['data'];Bmv4l: ynJ1N:gDvjO: if (!($value['type'] == 'reside')) {}Y28lH: }PCqHr: load()->func('tpl');zS5cF: if (empty($row['thumb'])) {}hkVUx: if (!is_array($wxcard)) {}Q1Jz_: $url = $outurl;IQ_1w: nVM8J:VeiLX: if (!empty($row)) {}G4kOz: $row['voices'] = $row['voice'];owYuT: if (!(!empty($par['wxcard']) && pdo_tableexists('dayu_wxcard_activity'))) {}R6ixr: j07Ft:SQOoj: mc_notice_credit1($row['openid'], mc_openid2uid($row['openid']), $activity['credit'], $log);T7D1W: pdo_update('dayu_form_info', $record, array("rerid" => $rerid));xOxG_: $comment = pdo_get('dayu_comment', array("weid" => $_W['uniacid'], "id" => $row['commentid']), array());OxZGw: $par = iunserializer($activity['par']);d5HrN: B8VzJ:C5i9i: foreach ($arr2 as $index => $v) { $state[$v][] = $this->get_status($row['reid'], $v); KRh1D: }UXLeF: $custom['touser'] = trim($row['openid']);VkfAf: $custom = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_custom) . ' WHERE weid = :weid ORDER BY displayorder DESC', array(":weid" => $_W['uniacid']));ZzFaw: WgRH0:REklD: $behavior = $settings['creditbehaviors'];Jf3HH: $params[':rerid'] = $rerid;iMM0j: include $this->template('detail');puD5b: if (!is_error($acc)) {}ZeNC4: $arr2 = array("0", "1", "2", "3", "8", "7");TEYSz: require MODULE_ROOT . '/fans.web.php';wOgJs: $record['kfinfo'] = $_GPC['kfinfo'];EYmHV: $params[':reid'] = $row['reid'];XapQt: FUZaK:wn3a_: if (!($par['sms'] != '0' && $par['paixu'] != '2' && !empty($activity['smstype']))) {}oONh_: $ytime = date('Y-m-d H:i:s', TIMESTAMP);FSqZN: $rerid = intval($_GPC['id']);sirYS: eCsjM:SSKOJ: $row['revoices'] = $row['revoice'];YnuxH: } public function doWebupfile() {qUPl1: uLnSf:JYadI: $max_file_size = 2000000;bHSEP: global $_W, $_GPC;W7qc9: if (!($max_file_size < $file['size'])) {}NM3HT: $destination = $destination_folder . time() . '.' . $ftype;Ntm45: $pinfo = pathinfo($file['name']);ES2wr: exit;U4Un9: if (is_uploaded_file($_FILES['upfile'][tmp_name])) {}o5oiL: echo 'name';NCwE4: $destination_folder = ATTACHMENT_ROOT . 'dayu_form/' . $_W['uniacid'] . '/file/';siog3: if (move_uploaded_file($filename, $destination)) {}vVlL8: $ftype = $pinfo['extension'];rnYq8: Im4Ft:iV3jz: $fname = $pinfo[basename];aHgwK: YuMva:ICA9V: exit;UuNUn: $image_size = getimagesize($filename);s3F2X: echo 'size';N1kCv: if (!(file_exists($destination) && $overwrite != true)) {}eixon: mkdir($destination_folder);U4B4y: exit;b8V3D: if (file_exists($destination_folder)) {}K3dTP: echo $dest . $fname;Nt0Wv: $file = $_FILES['upfile'];V98Ml: $pinfo = pathinfo($destination);SvKrQ: $filename = $file['tmp_name'];ubyeA: exit;s74Es: echo 'move';moUUs: G5gVt:hVply: Qdd48:fpmVF: echo 'nothing';tiQ59: $dest = '/attachment/dayu_form/' . $_W['uniacid'] . '/file/';LWkOE: } public function doWebManage() {XFKyh: S_vfr:J1Xlo: $where = 'reid = :reid';LtFBH: if (empty($list)) {}j2nMz: $where2 .= " and a.status='{$status}'";iLc57: w0RRz:AsZp0: header("Content-Disposition:attachment; filename={$activity['title']}=={$stime}-{$etime}.csv");a3lo1: $htmlheader = array("openid" => "粉丝编号", "member" => $activity['member'], "mobile" => $activity['phone']);XELPd: if (empty($_GPC['kf'])) {}IXt3A: if (!empty($activity)) {}GK6Y1: $fields = pdo_fetchall($sql, $params, 'refid');D52jM: $params = array();qXfcn: $par = iunserializer($activity['par']);srdvk: $childlist = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_data) . ' WHERE rerid IN (\'' . implode('\',\'', is_array($rerid) ? $rerid : array($rerid)) . '\') AND `reid`=:reid', array(":reid" => $reid));z5QFm: agppa:ERvgJ: L3Wq7:ZRrxJ: $where2 .= ' and (a.member like :member or a.mobile like :mobile)';Im0rX: $params2[':starttime'] = $starttime;wE3a2: zlZ3B:SWL2s: mkg6S:FcZ22: if (empty($_GPC['time'])) {}rAGMI: foreach ($listall as $index => $v) {Tkqe8: foreach (explode(',', $v['data']) as $val) { $v[] = $val; aUGjn: }t9gyE: $v['link']['l1'] = $this->get_linkage($linkage['l1'], '');zBtrT: unset($v['linkage']);r_x9K: $v['l2'] = $v['link']['l2']['title'];Jj1YV: $v['link']['l2'] = $this->get_linkage($linkage['l2'], '');aBoLH: $linkage = iunserializer($v['linkage']);a46Sm: nnluZ:zFWhh: Bzzw_:q7yI3: unset($v['data']);Q7t6L: $v['l1'] = $v['link']['l1']['title'];SsOpQ: MCECU:XAGjb: unset($v['link']);X54j0: $childrens[] = $v;LXKDD: if (!is_array($linkage)) {}VCKdR: }pq7rY: $staff = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_staff) . ' WHERE reid = :reid ORDER BY `id` DESC', array(":reid" => $reid));yDsb6: $where .= ' AND createtime >= :starttime AND createtime <= :endtime ';QMjcc: $where .= " and status='{$status}'";oadEt: foreach ($list as &$r) {hTyF_: $r['kf'] = mc_fansinfo($r['kf'], $acid, $_W['uniacid']);IdA8I: $r['groupid'] = mc_fetch($r['user']['uid'], array("groupid"));zvaAz: $r['voices'] = strstr($r['voice'], 'http://') ? $r['voice'] : $setting['qiniu']['host'] . '/' . $r['voice'];WkAix: $r['user'] = mc_fansinfo($r['openid']);CBGaH: $r['consult'] = pdo_fetch('SELECT id FROM ' . tablename('dayu_consult') . ' WHERE infoid = :infoid ORDER BY createtime DESC', array(":infoid" => $r['rerid']));tQdTP: if (empty($r['consult']['id'])) {}kQztp: DMQfR:s25jw: $r['revoices'] = strstr($r['revoice'], 'http://') ? $r['revoice'] : $setting['qiniu']['host'] . '/' . $r['revoice'];eJN9U: $r['consultid'] = '1';vpI9N: $r['state'] = $this->get_status($r['reid'], $r['status']);SEaIV: if (!pdo_tableexists('dayu_consult')) {}idg0p: oaiXj:sUvoM: $r['comment'] = !empty($par['comment']) && pdo_tableexists('dayu_comment') && !empty($r['commentid']) ? $this->get_comment($r['commentid']) : '';dIzH8: zhf1k:Hr3vI: }WTzNl: feJD_:lGmgI: $where .= " and kf LIKE '%{$_GPC['kf']}%'";g34FK: TaNH7:SdG97: $total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_info) . " WHERE {$where}", $params);OWatS: $params[':member'] = "%{$_GPC['keywords']}%";dj81w: if (!checksubmit('export', true)) {}kBm8S: $params = array(":reid" => $reid);sCmCw: $activity = pdo_get($this->tb_form, array("weid" => $_W['uniacid'], "reid" => $reid));ZMhHt: echo $html;BWK4B: $endtime = empty($_GPC['time']['end']) ? TIMESTAMP : strtotime($_GPC['time']['end']);COpwH: if (!empty($fields)) {}O0ODa: $zuhe = array_merge($htmlheader, $ds, $htmlfoot);GSvNu: $ds = array();Dco23: $etime = date('Ymd', $endtime);GhV0N: yOag9:ztI1t: $rerid = array_keys($list);FJ98u: $where2 .= ' AND a.createtime >= :starttime AND a.createtime <= :endtime ';vsvz6: $reid = intval($_GPC['id']);vZ7u7: if (empty($_GPC['time'])) {}X23O_: require MODULE_ROOT . '/fans.web.php';Nw4Lh: OIBtA:IyBeq: global $_W, $_GPC;ZreNt: $params2 = array(":reid" => $reid);HvvoO: if (!($status != '')) {}mCmMH: $params[':starttime'] = $starttime;XPx0W: message('非法访问.');O3Fan: R_IRW:yl6Qd: $params2[':mobile'] = "%{$_GPC['keywords']}%";QG_9C: $where2 = 'a.reid = :reid';HeE25: load()->model('mc');pZDH3: AbH0O:DlEzY: FaMPn:k_szj: $params2[':member'] = "%{$_GPC['keywords']}%";NaYmH: $htmlfoot = array("status" => "状态", "kfinfo" => "回复", "createtime" => "提交时间");CRd7_: LFWyb:nonVp: YtKDC:ffM8a: if (empty($_GPC['kf'])) {}KCPPq: include $this->template('manage');jy0kM: $sql = 'SELECT * FROM ' . tablename($this->tb_info) . " WHERE {$where} ORDER BY createtime DESC LIMIT " . ($pindex - 1) * $psize . ',' . $psize;Sem8m: message('您没有权限进行该操作.');Lvo4C: $allTotal = pdo_fetchall('SELECT `reid` FROM ' . tablename($this->tb_info) . ' WHERE reid = :reid GROUP BY openid', array(":reid" => $reid));MbRDr: $_W['page']['title'] = $activity['title'] . ' 记录管理';NkgTf: $childrens = array();ojkRI: $params2[':endtime'] = $endtime;R8Cg3: $stime = date('Ymd', $starttime);PREum: $html = $this->from_export_parse($zuhe, $childrens, $reid);WwSTy: lHm7y:AyzED: $where .= ' and (member like :member or mobile like :mobile)';OgUDd: foreach ($fields as $f) { $ds[$f['refid']] = $f['title']; m2ToQ: }x27BR: if (!($setting['role'] == 1 && $_W['role'] == 'operator' && !$role)) {}FC615: if (!($status != '')) {}mG8UH: LYgCV:crO24: if (empty($_GPC['keywords'])) {}LF_J8: $list = pdo_fetchall($sql, $params, 'rerid');V6mJO: exit;dG13S: $params[':reid'] = $reid;qoFk1: $pindex = max(1, intval($_GPC['page']));OEaEf: $psize = 15;cOJk8: $where2 .= " and a.kf LIKE '%{$_GPC['kf']}%'";Nwr5I: Y7W2i:Aqsk2: $params[':mobile'] = "%{$_GPC['keywords']}%";nIqJW: $la = iunserializer($activity['linkage']);dWi0z: $pager = pagination($total, $pindex, $psize);FWP1g: message('非法访问.');vc65H: foreach ($list as $key => &$value) {MjT2K: unset($v);fmYSZ: igF6y:awOLT: o3zge:awELA: if (!is_array($value['fields'])) {}IJ9rQ: foreach ($value['fields'] as &$v) {F7Eab: MK_7L:l0wNT: EzZox:VmqwJ: if (!(strstr($field, 'images') || strstr($field, 'dayu_form'))) {}HdtsG: $v = $img . tomedia($v) . '" style="width:50px;height:50px;"/>';vVyWK: $img = '<img src="';pnj1Y: }Tj4zA: IWr7P:pD_4u: }L33y6: $params[':endtime'] = $endtime;i09Pe: $children = array();hoRCl: header('Content-type:text/csv');s0PPN: $status = $_GPC['status'];abofV: if (empty($_GPC['keywords'])) {}Hrh2s: $starttime = empty($_GPC['time']['start']) ? strtotime('-1 month') : strtotime($_GPC['time']['start']);oCddo: $listall = pdo_fetchall('SELECT a.reid,a.rerid,a.member,a.mobile,a.openid,a.linkage,a.status,a.kfinfo,a.createtime,(SELECT GROUP_CONCAT(b.data ORDER BY b.displayorder desc) FROM ' . tablename($this->tb_data) . ' AS b WHERE b.rerid = a.rerid) data FROM ' . tablename($this->tb_info) . " AS a WHERE {$where2} ORDER BY a.rerid DESC", $params2);kKI0G: $sql = 'SELECT * FROM ' . tablename($this->tb_field) . ' WHERE `reid`=:reid ORDER BY displayorder desc';m3yUu: $role = $this->get_isrole($reid, $_W['user']['uid']);F7Ieb: foreach ($childlist as $reply => $d) {r09hy: pBc2a:b6dxL: hHj0I:Lmyox: unset($children[$reply]);cyLGQ: $children[$d['rerid']][] = $d;RSV_F: if (empty($d['rerid'])) {}lNZs9: }LGREg: } public function doWebbatchrecord() {sWI09: if (!empty($reply)) {}sDAXD: $reid = intval($_GPC['reid']);dWffW: keQuH:huvI3: ucMsF:TBdMx: if (!($setting['role'] == 1 && $_W['role'] == 'operator' && !$role)) {}zbGlp: require MODULE_ROOT . '/fans.web.php';DMdQr: message('抱歉，表单主题不存在或是已经被删除！');tYe0Q: message('记录删除成功！', '', 0);iUYs9: $reply = pdo_fetch('select reid from ' . tablename($this->tb_form) . ' where reid = :reid', array(":reid" => $reid));YbXXg: message('您没有权限进行该操作.');QbevX: global $_GPC, $_W;wW3zt: nGM9P:cNHRK: foreach ($_GPC['idArr'] as $k => $rerid) {IoiuF: A2yW3:eBvwq: pdo_delete('dayu_form_info', array("rerid" => $rerid, "reid" => $reid));OzQ5u: pdo_delete('dayu_form_data', array("rerid" => $rerid));pGlcz: $rerid = intval($rerid);CAzXC: }McVdb: $role = $this->get_isrole($reid, $_W['user']['uid']);cvN2W: } public function doWebupdategroup() {vBpel: exit(json_encode(array("status" => "error", "mess" => $data['message'])));dcECN: if (is_error($data)) {}UOKZ7: exit(json_encode(array("status" => "success")));Kgkri: EdPl7:M0pri: oZtfR:bqwDa:KFl3t: if (!$_W['isajax']) {}gNlkr: $groupid = intval($_GPC['groupid']);UJmFs:N3L6i: global $_GPC, $_W;TStYN: pdo_update('mc_mapping_fans', array("groupid" => $groupid), array("uniacid" => $_W['uniacid'], "acid" => $_W['acid'], "openid" => $openid));btkPC: $data = $acc->updateFansGroupid($openid, $groupid);QfYdQ: $openid = trim($_GPC['openid']);sSTqa: USAZH:XugYH: exit(json_encode(array("status" => "error", "mess" => "粉丝openid错误")));o5Ar8: if (!empty($openid)) {}He6LG: $acc = WeAccount::create($_W['acid']);DIDse: S3v2r:g3P6H: FxGg8:bf1pM: } public function doWebDisplay() {w774W: message('复制表单出错', '', 'error');C0OfU: $op = trim($_GPC['op']) ? trim($_GPC['op']) : '';vtwNM: $params = array();hb_BM: if (empty($_GPC['keyword'])) {}MCfXm: unset($form['reid']);hKy3x: if (!empty($form)) {}T35au: $roleid = array_keys($role);O6ubI: $pager = pagination($total, $pindex, $psize);yURvO: if (!$cateid) {}idvAY: B1VHu:TCeoz: $where .= ' AND reid IN (\'' . implode('\',\'', is_array($roleid) ? $roleid : array($roleid)) . '\')';dqHj5: $id = intval($_GPC['id']);fSSFD: if (!($op == 'copy')) {}D26yN: $params[':status'] = $switch;Oogd4: message('复制表单成功', $this->createWebUrl('display'), 'success');l2Ufm: global $_W, $_GPC;rG4_G: pdo_query($sql, $params);mhXP1: $ds = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_form) . ' WHERE ' . $where . ' ORDER BY status DESC,reid DESC LIMIT ' . ($pindex - 1) * $psize . ',' . $psize, $params);lwBee: mNcZJ:kTMCk:FTAAo: exit;T7Hml: yqtcu:r9bYW: $form = pdo_fetch('SELECT * FROM ' . tablename($this->tb_form) . ' WHERE weid = :weid AND reid = :reid', array(":weid" => $_W['uniacid'], ":reid" => $id));gerK6: foreach ($fields as &$val) {o_ORh: unset($val['refid']);or0le: pdo_insert($this->tb_field, $val);ZMOHm: sYGAJ:T39qs: $val['reid'] = $form_id;DTz6I: }fUJQO: message('表单不存在或已删除', referer(), 'error');v0oTP: CXHzl:zFOT5: $reid = intval($_GPC['reid']);Fez0A: bFSc0:u3_9i: SLMPS:XwLaB: $_W['page']['title'] = '表单列表';c10DC: $form['createtime'] = TIMESTAMP;nkbXY: PfiTr:UPWQy: ivcPe:YoGgN: require MODULE_ROOT . '/fans.web.php';SINAN: $role = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_role) . ' WHERE weid = :weid and roleid = :roleid  ORDER BY id DESC', array(":roleid" => $_W['user']['uid'], ":weid" => $weid), 'reid');eMnJI: tB5RM:JvOFJ: nhbgZ:TDPNH: $where = 'weid = :weid';oXVFO: $form_id = pdo_insertid();xgjet: if (!$_W['ispost']) {}S2WII: $params[':reid'] = $reid;l1qCX: foreach ($ds as &$item) {zYacT: $var1 = '&' . $item['var1'] . '=变量1';CkNeD: $item['cate'] = $this->get_category($item['cid']);gdT6Z: if (empty($item['var3'])) {}DBTxz: oFoLL:TvnRO: $item['la'] = $this->get_linkage($item['reid'], 1);aTS7J: $item['record'] = $item['isget'] == 1 ? murl('entry', array("do" => "record", "id" => $item['reid'], "m" => "dayu_form"), true, true) . $var1 . $var2 . $var3 : murl('entry', array("do" => "record", "id" => $item['reid'], "m" => "dayu_form"), true, true);Leo60: $item['isstart'] = $item['starttime'] > 0;VN3cp: $item['mylink'] = murl('entry', array("do" => "Mydayu_form", "id" => $item['reid'], "weid" => $item[weid], "m" => "dayu_form"), true, true);Vncl1: $item['par'] = iunserializer($item['par']);MeNi_: AAUCS:b5x4G: $item['isvar'] = $item['isget'] == 1 ? '<span class="btn btn-success btn-sm">启用</span>' : '<span class="btn btn-default btn-sm">关闭</span>';Zpy1v: $var3 = '&' . $item['var3'] . '=变量3';fTBDG: $item['link'] = $item['isget'] == 1 ? murl('entry', array("do" => "dayu_form", "id" => $item['reid'], "m" => "dayu_form"), true, true) . $var1 . $var2 . $var3 : murl('entry', array("do" => "dayu_form", "id" => $item['reid'], "m" => "dayu_form"), true, true);DF_Yk: if (empty($item['var1'])) {}JQ82g: sf0zC:kt1Ni: $item['switch'] = $item['status'];ROFRq: V7zfq:ZtZ21: if (empty($item['var2'])) {}hAdzA: $item['role'] = $this->get_isrole($item['reid'], $_W['user']['uid']);uSwpy: $var2 = '&' . $item['var2'] . '=变量2';ndfil: $item['count'] = $this->get_count($item['reid']);aqJ2d: }yzzKY: $total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_form) . ' WHERE ' . $where, $params);XKcJT: $psize = 10;Fvd9I: bnBoC:eXnoL: $fields = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_field) . ' WHERE reid = :reid', array(":reid" => $id));Ib0BX: $switch = intval($_GPC['switch']);Lw57o: $form['title'] = $form['title'] . '_' . random(6);jAWgz: include $this->template('display');sT_Oo: if (!($status != '')) {}QAWwQ: if (empty($fields)) {}KVHuH: $pindex = max(1, intval($_GPC['page']));M_9Qu: $status = $_GPC['status'];icd94: if (!($setting['role'] == 1 && $_W['role'] == 'operator')) {}cXotp: $params = array(":weid" => $weid);eJ6PF: $sql = 'UPDATE ' . tablename($this->tb_form) . ' SET `status`=:status WHERE `reid`=:reid';JYH6d: $cateid = intval($_GPC['formid']);QJuZq: LIU1Q:C68ZJ: $where .= " AND title LIKE '%{$_GPC['keyword']}%'";dP5Xi: $where .= ' and status=' . intval($status);J9b0i: pdo_insert($this->tb_form, $form);JcRQr: if (!$form_id) {}AzojN: $category = pdo_fetchall('SELECT id,title FROM ' . tablename($this->tb_category) . ' WHERE weid = :weid ORDER BY `id` DESC', array(":weid" => $_W['uniacid']));IzNR5: $where .= ' and cid=' . intval($cateid);IiQXC: } public function doWebDelete() {u_5bU: $params[':reid'] = $reid;huIgu: $sql = 'DELETE FROM ' . tablename($this->tb_field) . ' WHERE `reid`=:reid';RxtI_: TCDHC:Un6GC: $sql = 'DELETE FROM ' . tablename($this->tb_form) . ' WHERE `reid`=:reid';mP6Zp: pdo_query($sql, $params);rjfk1: message('非法访问.');JUVc_: $params = array();xRrD7: $sql = 'DELETE FROM ' . tablename($this->tb_staff) . ' WHERE `reid`=:reid';TN8ny: message('操作成功.', referer());NTd4A: $sql = 'DELETE FROM ' . tablename($this->tb_info) . ' WHERE `reid`=:reid';JeVwi: message('您没有权限进行该操作.');TkPI2: if (!($_W['role'] == 'operator' && !$role)) {}AI2w1: pdo_query($sql, $params);FRUXa: $role = $this->get_isrole($reid, $_W['user']['uid']);inBEO: if (!($reid > 0)) {}RpWBe: WsCiW:Z15_j: require MODULE_ROOT . '/fans.web.php';Oh80t: pdo_query($sql, $params);utp4m: pdo_query($sql, $params);HsT0d: global $_W, $_GPC;vJkIQ: $reid = intval($_GPC['id']);tT2lq: pdo_query($sql, $params);EuK6X: $sql = 'DELETE FROM ' . tablename($this->tb_data) . ' WHERE `reid`=:reid';fF3F3: } public function doWebdayu_formDelete() {RGaeD: if (empty($id)) {}QvRKa: $id = intval($_GPC['id']);hgcVT: w9IN3:f9l5I: require MODULE_ROOT . '/fans.web.php';Xuk2p: pdo_delete('dayu_form_info', array("rerid" => $id));O4HDN: message('操作成功.', referer());VnX22: global $_W, $_GPC;TbR5a: lrc6H:bJ266: $role = $this->get_isrole($id, $_W['user']['uid']);CEWti: message('您没有权限进行该操作.');mA6ds: pdo_delete('dayu_form_data', array("rerid" => $id));YPTZ0: if (!($setting['role'] == 1 && $_W['role'] == 'operator' && !$role)) {}qRtYP: } public function doMobiledayu_formDelete() {tklmn: if (!empty($id) && $openid == $form['openid']) {}Quta1:KCyFE: EF4gG:YeKy1: $id = intval($_GPC['id']);UBYQL: pdo_delete('dayu_form_data', array("rerid" => $id));TKWgl: pdo_delete('dayu_form_info', array("rerid" => $id));KW67P: IqPu_:l9D73: $form = pdo_fetch('SELECT rerid, openid FROM ' . tablename($this->tb_info) . " WHERE rerid = '{$id}'");WdPQK: $reid = intval($_GPC['reid']);mDeKO: $this->showMessage('删除成功.', $this->createMobileUrl('mydayu_form', array("weid" => $_W['uniacid'], "id" => $reid)), '', '', '');GdkEe: global $_W, $_GPC;SiEH0: $this->showMessage('删除失败，原因：该记录不在您的名下.', $this->createMobileUrl('mydayu_form', array("weid" => $_W['uniacid'], "id" => $reid)), '', '', '');VBA4Z: $openid = intval($_GPC['openid']);TOt2z: } public function doWebPost() {K1TsT: $sql = 'SELECT * FROM ' . tablename($this->tb_field) . ' WHERE `reid`=:reid ORDER BY `displayorder` DESC,`refid`';tcrLk: $sql = 'SELECT openid,nickname FROM ' . tablename('mc_mapping_fans') . ' WHERE acid =:acid AND openid = :openid';zqVao: plnrk:wVKlE: foreach ($skins as &$s) { $s['weid'] = !empty($s['ids']) ? explode(',', $s['ids']) : ''; ihdM0: }IW3T4: if (!empty($openid)) {}c3qQa: $record['cid'] = intval($_GPC['cate']);V_o5o: $activity['starttime'] && ($activity['starttime'] = date('Y-m-d H:i:s', $activity['starttime']));aIxvC:AxssB: $record['information'] = trim($_GPC['information']);jVwfx: if (empty($_GPC['thumb'])) {}nYs5i: $title = !empty($_GPC['id']) ? '编辑表单' : '新建表单';dmecL: $record['mbgroup'] = $_GPC['mbgroup'];Rw_S3: aWjIC:oSruJ: itoast('保存成功.', 'refresh');xbjtk: XSoGv:QQVQL: $record['agreement'] = trim($_GPC['agreement']);qk6p5: $sql = 'SELECT openid,nickname FROM ' . tablename('mc_mapping_fans') . ' WHERE acid =:acid AND nickname = :nickname';uII6P: $record['kfirst'] = trim($_GPC['kfirst']);Lb2wh: $types = array();PFq2L: $record['title'] = trim($_GPC['activity']);lYoc0: $params[':reid'] = $reid;x5Ml0: $var1 = !empty($par['var1']) ? '&' . $par['var1'] . '=自定义变量1' : '';O2NWc: $activity = pdo_fetch($sql, $params);ecyHx: if (!pdo_tableexists('dayu_wxcard_activity')) {}nAO6p: yo7RJ:lc4v4: $links = '保存表单后生成链接';eZNW7: if ($reply) {}ap7Y2: $print = pdo_fetchall('SELECT * FROM ' . tablename('dayu_print') . ' WHERE weid = :weid and status=1', array(":weid" => $_W['uniacid']));q9hM7: $record['list'] = intval($_GPC['list']);rHsjU: $record['paixu'] = intval($_GPC['paixu']);Olp4P: $types['checkbox'] = '多选(checkbox)';mgYwK: if ($reid) {}Hy6Pf: $dayu_check = pdo_fetchall('SELECT * FROM ' . tablename('dayu_check_category') . ' WHERE weid = :weid', array(":weid" => $_W['uniacid']));Z2OdD: if (is_array($_GPC['slide'])) {}erjrx: zIyV6:VeBNZ: c35_c:k8BYS: XAnmV:CkUNA: $ds = pdo_fetchall($sql, $params);oQ0iK: $record['thumb'] = $_GPC['thumb'];a6OPC: lRgBu:KRZJ4: $record['inhome'] = intval($_GPC['inhome']);nsqao: Xy_Ff:eiHGp: $params[':reid'] = $reid;zldqD: $par = iunserializer($activity['par']);Xr21q: $record['mfoot'] = trim($_GPC['mfoot']);N98YW: KFZ9d:VJQoZ: foreach ($_GPC['title'] as $k => $v) {t0N0Z: $field['loc'] = $_GPC['loc'][$k];mp3EG: hlZEW:S8roZ: $field['displayorder'] = range_limit($_GPC['displayorder'][$k], 0, 254);V44jn: $field['essential'] = $_GPC['essentialvalue'][$k] == 'true' ? 1 : 0;ijbxS: pdo_insert('dayu_form_fields', $field);mH88W: $field['title'] = trim($v);neX3m: $field['description'] = $_GPC['desc'][$k];e6pwh: $field['image'] = $_GPC['image'][$k];AVtJW: $field['type'] = $_GPC['type'][$k];B7rw0: $field = array();PEVap: $field['only'] = $_GPC['only'][$k];m_3uO: $field['bind'] = $_GPC['bind'][$k];nM44L: $field['value'] = $_GPC['value'][$k];G8Hsa: $field['reid'] = $reid;zrKo_: $field['value'] = urldecode($field['value']);g70Tn: }n5mKw: $activity = array("kfirst" => "有客户提交新的表单，请及时跟进", "kfoot" => "点击处理客户提交的表单。", "mfirst" => "受理结果通知", "mfoot" => "如有疑问，请致电联系我们。", "information" => "您提交申请我们已经收到, 请等待客服跟进.", "adds" => "联系地址", "voice" => "录音", "voicedec" => "录音说明", "pluraltit" => "上传图片", "status" => 1, "credit" => 0, "member" => "姓名", "phone" => "手机", "endtime" => date('Y-m-d H:i:s', strtotime('+365 day')));eZMeO: $types['email'] = '电子邮件(email)';KrE3Q: if ($op == 'verify') {}gUI2V:qSSDY: $record['isget'] = intval($_GPC['isget']);RaEeo: $record['isinfo'] = intval($_GPC['isinfo']);ikjHC: $record['pluraltit'] = trim($_GPC['pluraltit']);QCy4A: $record['isrevoice'] = intval($_GPC['isrevoice']);b6rQe: $params = array();Xi7Nd: $params[':weid'] = $_W['uniacid'];xahkr: itoast('保存表单失败2, 请稍后重试.', '', 'error');rn_yJ: $record['isloc'] = intval($_GPC['isloc']);ui_1F: $record['slide'] = '';yUxVF: foreach ($permission as &$p) {TaSSR: kvss4:n7Wu8: $p['user'] = $this->get_role($p['uid']);l0wkZ: x69Et:kadjN: $p['select'] = 1;UwHKc: if (!(!empty($role) && in_array($p['uid'], $rolearr))) {}yZg1P: }uB9yo: MEDIc:M8yO9: if (!pdo_tableexists('dayu_consult_category')) {}SFYus: $sql = 'DELETE FROM ' . tablename($this->tb_field) . ' WHERE `reid`=:reid';LoL1y: vExyL:AITXY: $types['text'] = '字符串(text)';HTek1: XSM6j:exwwf:Uw9G0: zK1zL:NCmLe: $record['m_templateid'] = trim($_GPC['m_templateid']);dj0Y2: $params[':weid'] = $_W['uniacid'];dkocw: $record['noticeemail'] = trim($_GPC['noticeemail']);kgOqQ: itoast('保存表单失败1, 请稍后重试.', '', 'error');s9ltT: if (!($_GPC['role'] && $reid)) {}KzNJX: $types['tel'] = '电话(tel)';brIO3: $data = array("edit" => intval($_GPC['edit']), "isdel" => intval($_GPC['isdel']), "follow" => intval($_GPC['follow']), "replace" => intval($_GPC['replace']), "card" => intval($_GPC['card']), "pretotal" => intval($_GPC['pretotal']), "daynum" => intval($_GPC['daynum']), "allnum" => intval($_GPC['allnum']), "header" => intval($_GPC['header']), "state1" => trim($_GPC['state1']), "state2" => trim($_GPC['state2']), "state3" => trim($_GPC['state3']), "state4" => trim($_GPC['state4']), "state5" => trim($_GPC['state5']), "state8" => trim($_GPC['state8']), "var1t" => trim($_GPC['var1t']), "var1" => trim($_GPC['var1']), "var2t" => trim($_GPC['var2t']), "var2" => trim($_GPC['var2']), "var3t" => trim($_GPC['var3t']), "var3" => trim($_GPC['var3']), "title" => trim($_GPC['titles']), "ismname" => intval($_GPC['ismname']), "mname" => trim($_GPC['mname']), "submitname" => trim($_GPC['submitname']), "btncolor" => trim($_GPC['btncolor']), "business" => trim($_GPC['business']), "address" => trim($_GPC['address']), "tel" => trim($_GPC['tel']), "lat" => $_GPC['baidumap']['lat'], "lng" => $_GPC['baidumap']['lng'], "noticeurl" => trim($_GPC['noticeurl']), "kami" => intval($_GPC['kami']), "print" => intval($_GPC['print']), "sendkami" => intval($_GPC['sendkami']), "comment" => intval($_GPC['comment']), "commenttitle" => trim($_GPC['commenttitle']), "consult" => intval($_GPC['consult']), "wxcard" => intval($_GPC['wxcard']), "getadd" => intval($_GPC['getadd']), "icredit" => intval($_GPC['icredit']), "onlytit" => trim($_GPC['onlytit']), "sms" => $_GPC['sms'], "smstype" => $_GPC['yztype'], "subtitle" => trim($_GPC['subtitle']), "icon" => $_GPC['icon'], "isrand" => $_GPC['isrand'], "randnum" => $_GPC['randnum'], "dayu_check" => intval($_GPC['dayu_check']));jqsu8: $record['plural'] = intval($_GPC['plural']);qsn2C: $record['member'] = trim($_GPC['member']);MrglE: kaloQ:ynRzB: RqTgu:Q3W0i: P6q1R:k1dhR: okosh:sarjS: kOKKW:FJ1EX: foreach ($_GPC['role'] as $rid) {aqn8Q: $rid = intval($rid);fInNP: LMtTX:YUNga: $insert = array("weid" => $_W['uniacid'], "reid" => $reid, "roleid" => $rid);HXYfN: unset($insert);Bus11: pdo_insert($this->tb_role, $insert) ? '' : itoast('抱歉，更新失败！', referer(), 'error');IiAGT: }NOoRA: $par['map'] = array("lat" => $par['lat'], "lng" => $par['lng']);WI84Q: $record['smstype'] = $_GPC['smstype'];h_oUD: $behavior = $settings['creditbehaviors'];Gu1Qs: if ($reid) {}uXDbd: $types['radio'] = '单选(radio)';wmhJ2: $record['kfoot'] = trim($_GPC['kfoot']);LB7XU: $record['status'] = intval($_GPC['status']);zVACA: txLzZ:Auo6y: if (!pdo_tableexists('dayu_kami_category')) {}bNIHw: $params[':reid'] = $reid;epAo9: $openid = trim($_GPC['openid']);LCnqd: $params[':reid'] = $reid;RVqf_: $settings = uni_setting($_W['uniacid'], array("creditnames", "creditbehaviors", "uc", "payment", "passport"));hbx38: $record['ivoice'] = intval($_GPC['ivoice']);yLG0i: !empty($activity['slide']) && ($slide = iunserializer($activity['slide']));z7Hv7: $record['skins'] = trim($_GPC['skins']);OlSZs: $params = array();XKRpD: $params = array();pUCMp: file_delete($_GPC['thumb-old']);G0zAV: $sendkami = pdo_fetchall('SELECT * FROM ' . tablename('dayu_sendkami_category') . ' WHERE weid = :weid', array(":weid" => $_W['uniacid']));E8fNs: $record['smsid'] = $_GPC['smsid'];mF4N9: muOR6:gXw8v: if (empty($skins)) {}Vtnne: XVJsp:bh0se: FHFOJ:b9GYs: $record['slide'] = iserializer($se);No1Dh: $record['par'] = iserializer($data);GsZjz: $groups = mc_groups();tcJGd: hfHqk:D9Dxu: $creditnames = $settings['creditnames'];lvOvU: $record['voicedec'] = trim($_GPC['voicedec']);oE7u3: $record['content'] = trim($_GPC['content']);LwxDL: $reid = intval($_GPC['id']);byvKd: if (!checksubmit()) {}lbnAh: $types['phone'] = '手机(phone)';KiylS: $types['image'] = '上传图片(image)';TW0fg: $types['select'] = '下拉框(select)';KV1Q0: TlA6k:RhaLA: load()->model('mc');BqDiM: $hasData = true;xAI5p: $links = murl('entry', array("do" => "dayu_form", "id" => $reid, "m" => "dayu_form"), true, true) . $var1 . $var2 . $var3;JdbuR: if (empty($permission)) {}znSV7: WrhO9:ancTZ: ae0UF:dyWWk: $types['calendar'] = '日历(calendar)';x1z47: $record['starttime'] = strtotime($_GPC['starttime']);cn516: if (!empty($exist)) {}hk0Lq: $consult = pdo_fetchall('SELECT * FROM ' . tablename('dayu_consult_category') . ' WHERE weid = :weid', array(":weid" => $_W['uniacid']));TkUcq: iDpYr:ulUzd: $comment = pdo_fetchall('SELECT * FROM ' . tablename('dayu_comment_category') . ' WHERE weid = :weid', array(":weid" => $_W['uniacid']));QA3mP: $par = array("state1" => "待受理", "state2" => "受理中", "state3" => "已完成", "state4" => "拒绝受理", "state5" => "已取消", "state8" => "退回修改", "mname" => "往期记录", "submitname" => "立 即 提 交", "header" => "1");U_l5e: $var3 = !empty($par['var3']) ? '&' . $par['var3'] . '=自定义变量3' : '';yk4pC: Nid4g:qSneh: $record['mobile'] = trim($_GPC['mobile']);Hukwl:gSIGA: $op = trim($_GPC['op']) ? trim($_GPC['op']) : 'post';fCips: $record['isrethumb'] = intval($_GPC['isrethumb']);ShFiP: Fw2Bk:Lb0gD: $permission = pdo_fetchall('SELECT id, uid, role FROM ' . tablename('uni_account_users') . ' WHERE uniacid = :weid and role != :role  ORDER BY uid ASC, role DESC', array(":role" => "clerk", ":weid" => $weid));TklLf: pdo_query($sql, $params);Y_5hs: $record['isvoice'] = intval($_GPC['isvoice']);Ycp6S: $record['voice'] = trim($_GPC['voice']);k_C3e: if (!pdo_tableexists('dayu_comment_category')) {}VSqEc: $category = pdo_fetchall('select * from ' . tablename($this->tb_category) . ' where weid = :weid ORDER BY id DESC', array(":weid" => $weid));CpGNH: $params = array();AkEzA:DMuo_: if (!pdo_tableexists('dayu_form_skins')) {}hY6NI: $hasData = false;lH2zX: if (!$_W['isajax']) {}lwu7k: if ($op == 'post') {}mO9WK: $record['outlink'] = trim($_GPC['outlink']);CNd1E: message(error(-1, '未找到对应的粉丝编号，请检查昵称或openid是否有效'), '', 'ajax');OemNW: $activity['endtime'] && ($activity['endtime'] = date('Y-m-d H:i:s', $activity['endtime']));HrBJu: $types['textarea'] = '文本(textarea)';yZNOX: $record['phone'] = trim($_GPC['phone']);eQeI4: $kami = pdo_fetchall('SELECT * FROM ' . tablename('dayu_kami_category') . ' WHERE weid = :weid', array(":weid" => $_W['uniacid']));eRsAk: foreach ($_GPC['slide'] as $slide) { $se[] = tomedia($slide); CcZYg: }p9QWk: p47NF:aWYVc: $exist = pdo_fetch($sql, array(":nickname" => $nickname, ":acid" => $_W['acid']));bw2Cd: if (!pdo_tableexists('dayu_check_category')) {}Lc_Fo: $record['createtime'] = TIMESTAMP;nHThi: $reply = pdo_fetch($sql, $params);NPLLg: $reid = pdo_insertid();SVZ1p: $record = array();Ma1Ne: S8PDY:KWM23: $var2 = !empty($par['var2']) ? '&' . $par['var2'] . '=自定义变量2' : '';suxeC: $types['idcard'] = '身份证(idcard)';Af7Hp: $record['status'] = 1;NxE3u: FTZj7:WsaWy: $role = pdo_fetchall('SELECT roleid FROM ' . tablename($this->tb_role) . " WHERE weid = '{$_W['uniacid']}' AND reid = '{$reid}'");LM0I3: if (empty($role)) {}kaIzC: load()->func('file');sFrMF: if (!($setting['role'] == 1 && ($_W['role'] == 'founder' || $_W['role'] == 'manager'))) {}YAAJo: if (!pdo_tableexists('dayu_print')) {}LnOJP: gLDJy:AMReZ: pdo_tableexists('dayu_photograph_fields') && ($types['photograph'] = '证件照(photo)');uqCJ2: $sql = 'SELECT COUNT(*) FROM ' . tablename($this->tb_info) . ' WHERE `reid`=' . $reid;HyaVU: if (!$reid) {}uH0hc: if (!pdo_tableexists('dayu_sms')) {}sI_U3: $types['range'] = '日期时间(range)';V7F2e: RjfqL:DbNqm: if (!(pdo_fetchcolumn($sql) > 0)) {}Ny43C: $exist = pdo_fetch($sql, array(":openid" => $openid, ":acid" => $_W['acid']));Kz0q5: message(error(0, $exist), '', 'ajax');BtkAw: pdo_insert('dayu_form', $record);Rffum: foreach ($role as $r) { $rolearr[] = $r['roleid']; T5hjL: }PdwxE: require MODULE_ROOT . '/fans.web.php';SQnzB: $modules = uni_modules();VwFtp: $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `reid`=:reid';gADQ5: $record['custom_status'] = intval($_GPC['custom_status']);zB386: include $this->template('post');xu8B3: VXbtA:aOdeW:RyYQd: if (empty($reid)) {}yqn97: $record['credit'] = $_GPC['credit'];FMKbD: $record['endtime'] = strtotime($_GPC['endtime']);Wu5DG: $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `reid`=:reid';e3Okm: pdo_delete($this->tb_role, array("weid" => $_W['uniacid'], "reid" => $reid));pC57j: $types['number'] = '数字(number)';B_ZFg: Lz10y:RHxe4: $wxcard = pdo_fetchall('SELECT * FROM ' . tablename('dayu_wxcard_activity') . ' WHERE weid = :weid and status=1', array(":weid" => $_W['uniacid']));uC7c0: $fields = mc_fields();y3cpo: $nickname = trim($_GPC['nickname']);T1DeX: $sms = pdo_fetchall('SELECT * FROM ' . tablename('dayu_sms') . ' WHERE weid = :weid', array(":weid" => $_W['uniacid']));c5fUI: $record['description'] = trim($_GPC['description']);zdOfE: if (!pdo_tableexists('dayu_sendkami_category')) {}lmmbY: if (!$activity) {}Ex7In: m2tza:nPe4H: $record['mfirst'] = trim($_GPC['mfirst']);Jiv6D: if (!(pdo_update('dayu_form', $record, array("reid" => $reid)) === false)) {}NWHW_: $record['k_templateid'] = trim($_GPC['k_templateid']);GWfnb: bYxcI:TVGvB: $record['weid'] = $_W['uniacid'];HIaSp: global $_W, $_GPC;lKxEf: mzb6R:PEnsz: $record['smsnotice'] = $_GPC['smsnotice'];YOzH_: $skins = pdo_fetchall('SELECT * FROM ' . tablename('dayu_form_skins') . ' WHERE status = 1 ORDER BY id', array());H8AVs: vsfRs:W2AP1: tPiyo:Cv3iL: if ($hasData) {}hiOk2: $types['reside'] = '省市区(reside)';QF0l5: } public function doWebBatchrRcord() {INT3A: if (!empty($reply)) {}oxgp4: $role = $this->get_isrole($reid, $_W['user']['uid']);cmI2L: $result['msg'] = '记录批量删除成功！';MhfAU: Qhm1y:EgRNb: $result['status'] = 1;FPLQP: $reply = pdo_fetch('select reid from ' . tablename($this->tb_form) . ' where reid = :reid', array(":reid" => $reid));XUnAb: require MODULE_ROOT . '/fans.web.php';JukLD: message('您没有权限进行该操作.');OlflB: hXxuL:kT4Ar: $result['status'] = 0;oShG6: global $_GPC, $_W;AFN9V: $result['msg'] = '抱歉，表单主题不存在或是已经被删除！';ZeBdo: $reid = intval($_GPC['reid']);Ndcq6: message($result, '', 'ajax');iPVw3: if (!($setting['role'] == 1 && $_W['role'] == 'operator' && !$role)) {}z7zVl: foreach ($_GPC['idArr'] as $k => $rerid) {Un4J4: yDwCS:vdBIR: pdo_delete($this->tb_info, array("rerid" => $rerid, "reid" => $reid));wmEwf: pdo_delete($this->tb_data, array("rerid" => $rerid, "reid" => $reid));TVhdi: }WnZav: v_v7r:vp4ib: } public function doWebRecordSet() {ZtSot: message('保存成功.', 'refresh');ya09V: $record['fields'] = iserializer($th);UkNIm: fkpqM:evNRx: foreach ($_GPC['fields'] as $fields) { $th[] = $fields; tFlyW: }Wd5nl: $reid = intval($_GPC['reid']);bit3t: $params = array();IeRZc: require MODULE_ROOT . '/fans.web.php';xoYin: global $_W, $_GPC;QPHOl: if (!(pdo_update('dayu_form', $record, array("reid" => $reid)) === false)) {}t2a6_: $role = $this->get_isrole($reid, $_W['user']['uid']);UL1Hp: if (empty($_GPC['fields'])) {}vkMKT: icbVW:dRdyd: $record['avatar'] = intval($_GPC['avatar']);l90yT: $sql = 'SELECT * FROM ' . tablename($this->tb_field) . ' WHERE `reid`=:reid ORDER BY `displayorder` DESC,`refid`';T6w2C: $params = array();Bbl2E: if (!checksubmit()) {}g1BGo: $params[':weid'] = $_W['uniacid'];T11wz: if (!($setting['role'] == 1 && $_W['role'] == 'operator' && !$role)) {}UEFTI: $record = array();iw9Cr: $activity = pdo_fetch($sql, $params);MPYna: $params[':reid'] = $reid;RHAio: $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `reid`=:reid';RNR67: message('保存表单失败, 请稍后重试.');qtvvH: include $this->template('recordset');nH2ZJ: $record['field'] = intval($_GPC['field']);Pp9Lw: message('您没有权限进行该操作.');nGHw_: KeRYi:nvyz0: $record['bcolor'] = $_GPC['bcolor'];DqhqR: GmcAO:tYoGs: $record = iunserializer($activity['fields']);LYlHL: $ds = pdo_fetchall($sql, $params);jGic3: $params[':reid'] = $reid;nBr9Z: MmrGH:x6AvV: } public function doWebCustom() {rnrS2: include $this->template('custom');nauFl: RarCE:BcVsg: if (!($setting['role'] == 1 && $_W['role'] == 'operator' && !$role)) {}eXRpR: itoast('抱歉，快捷回复内容不存在或是已经被删除！', $this->createWebUrl('custom', array("op" => "display")), 'error');tzdxf: pdo_insert($this->tb_custom, $data);LJ_xy: message('您没有权限进行该操作.');FPw3S: if (!empty($_GPC['raply'])) {}ey8bU: if ($operation == 'display') {}GlJVU: include $this->template('custom');i3I35: $op = $operation = $_GPC['op'] ? $_GPC['op'] : 'display';ASNEy: foreach ($_GPC['displayorder'] as $id => $displayorder) { pdo_update($this->tb_custom, array("displayorder" => $displayorder), array("id" => $id)); vQ0FD: }yg38h: global $_W, $_GPC;m3aZs:GB2mP: $custom = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_custom) . ' WHERE weid = :weid ORDER BY displayorder DESC', array(":weid" => $weid));WuQda: dcVOR:ZKZRy: $data = array("weid" => $weid, "displayorder" => intval($_GPC['displayorder']), "raply" => $_GPC['raply']);ZTW8e: $role = $this->get_isrole($id, $_W['user']['uid']);pMPvr: $id = intval($_GPC['id']);Ir4VV: itoast('快捷回复内容排序更新成功！', $this->createWebUrl('custom', array("op" => "display")), 'success');n2U3s: WArD_:kJpIP: pdo_delete($this->tb_custom, array("id" => $id));vc872: $city = array("displayorder" => 0);XnjTK: itoast('快捷回复内容删除成功！', $this->createWebUrl('custom', array("op" => "display")), 'success');NHSmV: if (!checksubmit('submit')) {}J5IFc: if ($operation == 'delete') {}DJYSa: $custom = pdo_fetch('SELECT * FROM ' . tablename($this->tb_custom) . " WHERE id = '{$id}'");R3t9i: pCbqv:XpSxa: if (!empty($id)) {}kRXpq: $id = $_GPC['id'];Ue292: if (empty($_GPC['displayorder'])) {}jcRal: if ($operation == 'post') {}z317W:Q4gi8: Qx2oB:q5Jli: YY9fI:ZC0Ur: A82lP:XwEvn: gNDN5:tIqkD:aM8D9: itoast('更新快捷回复内容成功！', $this->createWebUrl('custom', array("op" => "display")), 'success');ni3Bh: kkBsA:UqNOX: require MODULE_ROOT . '/fans.web.php';sm17q:zJbfh: if (!empty($custom)) {}c05Xr: jVfom:EtCVW: pdo_update($this->tb_custom, $data, array("id" => $id));ePyiJ: if (!empty($id)) {}mrf27: $id = pdo_insertid();Ro7_y:kl3Ux: OmFCN:DhQ7N: Ap8RK:g7kD0: gbZ1k:nEJlj: xDYaG:Es7SZ: load()->func('tpl');MrwqK: $id = intval($_GPC['id']);C0Lxn: $custom = pdo_fetch('SELECT * FROM ' . tablename($this->tb_custom) . " WHERE id = '{$id}'");S1zWM: message('抱歉，请输入快捷回复内容！');hlFsy: } public function doMobilerecord() {GOgho: $rows = pdo_fetchall($sql, $params, 'rerid');Mo5uX: $record = iunserializer($activity['fields']);JvfN3: $reid = intval($_GPC['id']);XpX7C: $list = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_form) . ' WHERE weid = :weid and kfid = :openid and status = 1 ORDER BY reid DESC', array(":weid" => $weid, ":openid" => $openid), 'reid');MuGCS: $psize = 10;SO34C: $params[':weid'] = $_W['uniacid'];omowJ: $sql = 'SELECT * FROM ' . tablename($this->tb_info) . " WHERE reid=:reid {$where} ORDER BY createtime DESC,rerid DESC LIMIT " . ($pindex - 1) * $psize . ',' . $psize;si00V: $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `reid`=:reid';r7kbf: $pindex = max(1, intval($_GPC['page']));nQXgm: foreach ($childlist as $reply => $r) {PokRB: Q_Ln4:kQYt1: $children[$r['rerid']][] = $r;INKhM: unset($children[$reply]);TzMH8: if (empty($r['rerid'])) {}NNqkd: zZVbG:egYZl: }CHDz6: $activity = pdo_fetch($sql, $params);vh9WO: $params[':reid'] = $reid;fkOdd: $pager = $this->pagination($total, $pindex, $psize);xrTtN: require MODULE_ROOT . '/fans.mobile.php';HAm80: $state = !empty($activity['state3']) ? $activity['state3'] : '已完成';yEAxZ: $childlist = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_data) . ' WHERE rerid IN (\'' . implode('\',\'', is_array($rerid) ? $rerid : array($rerid)) . "') AND `reid`=:reid AND `refid` IN ({$fids}) ORDER BY displayorder DESC,rerid DESC", array(":reid" => $reid));Yhqou: $fids = implode(',', $record);jdp0R: $this->showMessage('非法访问，主题不存在', '', 'error', '', '');Pwmra: foreach ($rows as $index => $row) {laiWw: if (empty($row['rethumb'])) {}TzGsf: $rows[$index]['thumbs'] = !empty($row['rethumb']) ? iunserializer($row['thumb']) : '';Yg98K: $rows[$index]['thumb'] = iunserializer($row['thumb']);zpnbe: cKE_7:ED7tp: JyMHW:OIEnu: $maps = $piclist;f__ut: foreach ($rows[$index]['thumbs'] as $p) { $piclist .= is_array($p) ? $p['attachment'] : tomedia($p) . ','; yDq3j: }j9eLS: $rows[$index]['rethumb'] = !empty($row['rethumb']) ? iunserializer($row['rethumb']) : '';a7ur4: foreach ($rows[$index]['rethumb'] as $p) { $piclist .= is_array($p) ? $p['attachment'] : tomedia($p) . ','; cYo5d: }uX04Y: bJrtn:T9Nei: if (empty($row['rethumb'])) {}Rts2B: fMa1Z:suOux: BsCah:B35WE: $rows[$index]['user'] = mc_fansinfo($row['openid'], $acid, $weid);snTel: }kIunM: global $_W, $_GPC;DvakE: $rerid = array_keys($rows);mFO4S: $where .= ' and status=3';vQ9Ng: p2g9d:J2Rxd: $params[':reid'] = $reid;CMWam: i3snT:JSq25: $total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_info) . " WHERE reid = :reid {$where} ", $params);j4Eat: $params = array();XOm7l: include $this->template('record');ResiK: $children = array();ILuYf: AQc0f:N6d51: if ($activity) {}qWi45: $params = array();oYNRg: } public function doMobileCheckOnly() {O1hzM:b4K7G: $result['msg'] = $_GPC['title'] . $msg;N6Wg5: $par = iunserializer($activity['par']);G7Y2n: $result['status'] = '1';q2oU_: if ($_GPC['content'] == $data['data']) {}cFZto: $result['status'] = '0';fhuXv: message($result, '', 'ajax');eNPzt: d_djq:kADph: global $_W, $_GPC;MQF7B: $activity = pdo_get($this->tb_form, array("weid" => $_W['uniacid'], "reid" => $_GPC['reid']), array("par"));XV38x: nVy05:q8eq0: $msg = !empty($par['onlytit']) ? $par['onlytit'] : '存在相同内容，请重新填写';TFRWH: $result['msg'] = '可使用';E3Jb9: $data = pdo_fetch('SELECT * FROM ' . tablename($this->tb_data) . ' WHERE reid = :reid AND refid = :refid', array(":reid" => $_GPC['reid'], ":refid" => $_GPC['refid']));Pu3I8: } public function doMobileEdit() {zadss: $_share['imgUrl'] = tomedia($activity['thumb']);Gp1wj: foreach ($field as $f) {DU9Ow: $ds[$f['refid']]['tixing'] = !empty($f['description']) ? urldecode($f['description']) : '请输入' . $f['title'];S2oow: $ds[$f['refid']]['default'] = $f['default'];dabtq: if (!($f['type'] == 'image')) {}ohw6x:c4E93: $reside = $f;gkHEe: $ds[$f['refid']]['loc'] = $f['loc'];maHlp: $ds[$f['refid']]['tixing'] = !empty($f['description']) ? urldecode($f['description']) : '请填写' . $f['title'];el0oa: $ds[$f['refid']]['type'] = $f['type'];McdHI: if (!(!empty($f['loc']) && pdo_tableexists('dayu_form_plugin_radio') && ($f['type'] == 'radio' || $f['type'] == 'checkbox'))) {}Py5MR: $ds[$f['refid']]['options'] = explode(',', $f['value']);kQT5l: $fids[] = $f['refid'];GGzpF: $ds[$f['refid']]['refid'] = $f['refid'];e9dPZ: $ds[$f['refid']]['tixing'] = !empty($f['description']) ? urldecode($f['description']) : '请选择' . $f['title'];buVU4:cihEf: $f['default'] = $profile[$f['bind']];f2GIs: F03h5:DTxUf: if (!($f['type'] == 'reside')) {}xv4yg: if (in_array($f['type'], array("text", "number", "email"))) {}se4Yv: ibXzk:Mcrio: $ds[$f['refid']]['photograph_url'] = $f['photograph_url'];vT0L7: $ds[$f['refid']]['fid'] = $f['title'];hAF9e: VwR5O:RtTxg: p2_01:dj3C7: $ds[$f['refid']]['dayu_radio'] = pdo_fetchall('SELECT * FROM ' . tablename('dayu_form_plugin_radio') . ' WHERE weid = :weid and cid = :cid and status=1 ORDER BY id DESC', array(":weid" => $weid, ":cid" => $f['loc']));yK5s4: $ds[$f['refid']]['essential'] = $f['essential'];gsGhm: if ($f['type'] == 'textarea') {}HfA6G: if (!$profile[$f['bind']]) {}ygZcs: $ds[$f['refid']]['image'] = !empty($f['image']) ? $f['image'] : TEMPLATE_WEUI . 'images/nopic.jpg';jeO6E: srudR:xRuQ_: SFM7s:o7dQd: CImPS:sp_DD: wa68y:ezGYz: }b8MUL: if ($activity['custom_status'] == 0 && $staff) {}e1m1i: $info = pdo_get($this->tb_info, array("rerid" => $rerid), array());z31gG: $acc = WeAccount::create($_W['acid']);uGvsd: $activity = pdo_get($this->tb_form, array("weid" => $weid, "reid" => $reid), array());RCefv: exit;ZOoBs: $rerid = intval($_GPC['rerid']);Gy6ne: foreach ($staff as $s) {FooDY: $url = $_W['siteroot'] . 'app/' . $this->createMobileUrl('manageform', array("name" => "dayu_form", "id" => $reid));awkdr: $custom = array("msgtype" => "text", "text" => array("content" => urlencode($info)), "touser" => $s['openid']);T7hpU: $info = '【您好，有新的消息】
';AOkX3: Rcpeu:V5Rs9: $CustomNotice = $acc->sendCustomNotice($custom);KbWHQ: $info .= "<a href='{$url}'>点击查看详情</a>";laF0S: $info .= "姓名：{$_GPC['member']}
手机：{$_GPC['mobile']}
内容：{$bodym}
";dN48b: }OgvMa: IInTh:IaiZn: $datas = array();UFXal: ihttp_email($activity['noticeemail'], $activity['title'] . '的表单提醒', '<h4>姓名：' . $info['member'] . '</h4><h4>手机：' . $info['mobile'] . '</h4>' . $body);zCq42: $row = array();uEIo1: if (!is_array($staff)) {}tfSh3: $this->showMessage($activity['information'], $outlink);nuFkl: foreach ($_GPC['thumb'] as $thumb) { $th[] = tomedia($thumb); Cxqty: }B3_X4: YKYBS:utk0j: $reid = intval($_GPC['reid']);VdXaJ: $title = $par['header'] == 1 ? $activity['title'] : $activity['titles'];YNC1u: load()->func('tpl');CcMyx: foreach ($datas as $row) {nbu8J: if (!strstr($row['content']['data'], 'images')) {}jSZv8: $body .= '<h4>' . $row['content']['data'] . '</h4>';Ts6c4: Lvz0S:P5H0Q: $smsbody .= $row['content']['data'] . '，';dE3lr: $bodym .= '\\n　' . $row['content']['data'];mfIAO: IGi6v:v31gL: $bodnew .= !empty($row['content']['data']) ? '\\n' . $row['fid']['title'] . '：' . $row['content']['data'] : '';MnUdM: $row['fid'] = $this->get_fields($field['refid']);yKqjE: $field = pdo_get($this->tb_data, array("redid" => $row['refid']), array());D36er: $row['data'] = '有';HLd2d: }bq2pH: global $_W, $_GPC;ncqQR: gh1Zw:j8rBJ: REuHU:gjqKh: if (!is_array($_GPC['thumb'])) {}Zu5m1: if (!is_array($staff)) {}fS9Es: $profile = mc_fetch($uid, $binds);LPCQj: if (!empty($datas)) {}DPjrq: Ybq_P:YkMPu: $binds = array();xzMO0: v1Qmi:UsUBR: $info['fields'] = $info['redid'] = array();RtCmH: load()->func('communication');oiwoH: exit;kpe1z: Nlg2R:bYRPk: foreach ($staff as $s) {J2gAG: ms168:Cg9qj: $this->send_template_message(urldecode(json_encode($template)));R_sNc: $template = array("touser" => $s['openid'], "template_id" => $activity['k_templateid'], "url" => $_W['siteroot'] . 'app/' . $this->createMobileUrl('manageform', array("name" => "dayu_form", "id" => $reid)), "topcolor" => "#FF0000", "data" => array("first" => array("value" => urlencode($activity['kfirst'] . '\\n'), "color" => "#743A3A"), "keyword1" => array("value" => urlencode($info['member']), "color" => "#000000"), "keyword2" => array("value" => urlencode($info['mobile']), "color" => "#000000"), "keyword3" => array("value" => urlencode($ytime), "color" => "#000000"), "keyword4" => array("value" => urlencode($status['name'] . '\\n' . $bodnew . '\\n'), "color" => "#FF0000"), "remark" => array("value" => urlencode($activity['kfoot']), "color" => "#008000")));u0Mg1: }sZ6Bg: if (!($_W['ispost'] || checksubmit('submit'))) {}B08Xa: $outlink = !empty($activity['outlink']) ? $activity['outlink'] : $this->createMobileUrl('mydayu_form', array("op" => "detail", "rerid" => $rerid, "id" => $reid));hHUTP: zjJdF:YKUFu: require MODULE_ROOT . '/fans.mobile.php';gIdR9: $this->showMessage('非法访问.', '', '', '', '');BGMu3: $picker = 1;M5hUv: $status = $this->get_status($reid, '0');i68gE: KEBKb:e05oE: $ytime = date('Y-m-d H:i:s', TIMESTAMP);M3uHB: $row['status'] = '0';Y1JGv: T5AlF:WWMz7:xppsF: $_share['title'] = $activity['title'];TpzL6: $this->showMessage('记录不存在或是已经被删除！', '', '', '', '');pB3aG: $par = iunserializer($activity['par']);cKNPU: foreach ($_POST as $key => $value) {FBUrH: $entry['data'] = strval($value);lIke4: pdo_update($this->tb_data, $entry, array("redid" => $key));mmxUx: $entry = array();cPcSv: NPkqR:nixJo: $datas[] = array("content" => $entry, "refid" => $key);Y6wQz: }utJQ7: $_share['content'] = $activity['description'];bHiiR: if (empty($datas)) {}Zy_i1: $ds = $fids = array();oGoq9: $staff = pdo_fetchall('SELECT `openid` FROM ' . tablename($this->tb_staff) . ' WHERE reid=:reid AND weid=:weid', array(":weid" => $_W['uniacid'], ":reid" => $reid));M9DAK: TOHsR:UzsF8: ECHAn:nyUgI: $returnUrl = urlencode($_W['siteurl']);jdfFq: ytaG4:V4W55: pdo_update($this->tb_info, $row, array("rerid" => $rerid));JgpRc: HodCA:rPDEe: $row['thumb'] = iserializer($th);EiwbG: A4dEG:gmRng: $this->showMessage('非法访问，提交数据不能为空', '', 'error');BDQ3p: E1JCQ:ob3fu: if (empty($activity['noticeemail'])) {}SAlzK: $field = pdo_getall($this->tb_field, array("reid" => $reid), array(), '', 'displayorder DESC,refid DESC', '');Wh8TV: a1h85:mAtfZ: include $this->template('edit');YWqdy: $fdatas = pdo_fetchall($sql, $params);hnkkh: n_3Jc:Aatk7: $submitname = !empty($par['submitname']) ? $par['submitname'] : '立 即 提 交';KRUtR: $sql = 'SELECT * FROM ' . tablename($this->tb_data) . " WHERE `reid`='{$reid}' AND `rerid`='{$rerid}' AND `refid` IN ({$fids})";VPGr3: foreach ($fdatas as $fd) {JzWtO: $info['redid'][$fd['refid']] = $fd['redid'];KpTtk: $info['fields'][$fd['refid']] = $fd['data'];HL8Ft: $info['fields'][$fd['refid']] = tomedia($fd['data']);HD6f5: yWeHL:d7s02: if (strstr($fd['data'], 'images')) {}IW4Go:eMkkd: MdaSe:VYpDv: AKSFf:goGq5: }V5Eqb: $this->showMessage('不能修改内容', 'error');Njuyv: $fids = implode(',', $fids);fHVz4: if (!($par['edit'] != '1' && $info['status'] != '8')) {}jHf3a: E7N0r:QEvQd: if (!empty($field)) {}ScdGA: if (!($info['openid'] != $openid)) {}COTM9: } public function doMobiledayu_form() {ghQxu: HT1DO:KUTdT: $row['mobile'] = $_GPC['mobile'];o9ppK: $sms_data = array("mobile" => $activity['mobile'], "title" => $activity['title'], "mname" => $member, "mmobile" => $_GPC['mobile'], "openid" => $row['openid'], "status" => $state['name'], "ptname" => $_W['account']['name'], "addons" => $_W['current_module']['name'], "content" => $alldata);pq8Y_: $btncolor = $btncolor['css'];Mqft9: ViXi7:BDQsG: $ds = pdo_fetchall($sql, $params);FdJmL: load()->func('communication');fu1ac: if (empty($datas)) {}kH1kY: $this->showMessage('表单不完整，缺少自定义项目，无法正常访问.');mdwXk: TP_x9:QCvPe: if (!(!empty($par['comment']) && pdo_tableexists('dayu_comment'))) {}JhcTp:iVTdg: $row['address'] = $_GPC['address'];ODqxR: if (!empty($rerid)) {}egX_6: $row['kid'] = $kami['id'];fveNi: foreach ($staff as $s) {UAnKk: $url = $_W['siteroot'] . 'app/' . $this->createMobileUrl('manageform', array("name" => "dayu_form", "weid" => $row['weid'], "id" => $row['reid']));BgHtp: $info .= "<a href='{$url}'>点击查看详情</a>";KU7Yp: $custom = array("msgtype" => "text", "text" => array("content" => urlencode($info)), "touser" => $s['openid']);suweX: xxNv7:GAHjm: $info .= "姓名：{$member}
手机：{$_GPC['mobile']}
内容：{$bodym}{$voice}{$kamiinfo}
";G3gzj: $info = '【您好，有新的消息】
';WSYQa: $CustomNotice = $acc->sendCustomNotice($custom);XigMe: }ENW09: $member = !empty($row['member']) ? $row['member'] : $fans['user']['nickname'];hW15A: if (!($allnum >= intval($par['allnum']))) {}TgbPL: load()->model('mc');HpG7Z: $row['openid'] = $openid;m0BlL: suO2R:D9jU3: $update['realname'] = $_GPC['member'];L0DGk: $this->showMessage('名额已满', '', 'info');CyLQs: if (empty($activity['noticeemail'])) {}Rhw2_: if (!($par['follow'] == 1)) {}qkQU9: exit;F0CZ4: $set = $this->module['config'];wbtCu: v4a2M:zgKQE: BD75M:yv1Yd: if (!($activity['endtime'] < TIMESTAMP)) {}M7j21: $row['var1'] = $_GPC[$par['var1']];mVDAW: foreach ($formdata as $index => $v) { $alldata[] = $v['title'] . ':' . $v['data'] . ','; f1k2H: }e7KyW: if (!$_FILES) {}HmTVm: $ds = pdo_fetchall($sql, $params);H9zct: $params[':reid'] = $reid;Gz83z: $this->showMessage('保存失败.');YWwCz: x0zXP:H67h0: $sql = 'SELECT * FROM ' . tablename($this->tb_field) . ' WHERE `reid` = :reid ORDER BY `displayorder` DESC';HCFHv: $params[':reid'] = $reid;BQ2eb: $log = $activity['title'] . '-' . $activity['credit'] . '积分';NHt4V: cs6vX:tB4NX: if (!empty($repeat)) {}IuFh9: return error(-1, $acc['message']);uhDYl: $groupid = $group['groupid'];Ga8E1: if ($activity['custom_status'] == 0 && $staff && $activity['k_templateid']) {}Ic34E: $kami = pdo_get('dayu_kami', array("weid" => $weid, "number" => $_GPC['kami'], "cid" => $par['kami']), array("id", "status", "number", "password"));zXQvj: $repeat = $_COOKIE['r_submit'];F8N3u: W9BJK:kCt42: ohpdk:Lzl0n: $par = iunserializer($activity['par']);uTxTB: $sendkamidata = array("reid" => $reid, "infoid" => $rerid, "addons" => "dayu_form", "openid" => $openid, "name" => $row['member'], "mobile" => $row['mobile'], "status" => 1, "updatetime" => TIMESTAMP);ZSGpU: $activity['smsid'] && empty($member['mobile']) && $par['smstype'] == '1' && $this->showMessage('请完善手机号', murl('entry', array("do" => "index", "id" => $activity['smsid'], "m" => "dayu_sms", "form" => $_W['current_module']['name'], "returnurl" => $returnUrl), true, true), 'info');ur04f: zufKd:KAXfu: if (!($par['replace'] && !empty($update))) {}U95CK: iXQnS:gioa9: if (!is_array($staff)) {}zhtwx: rGZJa:dPQHj: J8aL0:f0P4n: $behavior = $settings['creditbehaviors'];onf0G: qzrob:dhWFW: $lg = array("l1" => intval($_GPC['linkage1']), "l2" => intval($_GPC['linkage2']));okX85: FoOsT:z9Njk: Dh9jA:cKB6_: foreach ($binds as $key => $value) {uY6T0: V3YSM:zf2wU: $binds[] = 'residedist';H__yI: $binds[] = 'resideprovince';WeRpP: $binds[] = 'birthmonth';Xip71:nacgv:CoUk8: $binds[] = 'residecity';z7jxO: bmcwT:EFeb9: if (!($value == 'birth')) {}pOR40: $binds[] = 'birthyear';DjOzE: unset($binds[$key]);qF99m: unset($binds[$key]);nKjIn: if (!($value == 'reside')) {}mWMQg: $binds[] = 'birthday';ItSsh: X6nub:Z0gZc: }n3F3R: l1oMn:HI4sU: pdo_update('dayu_sendkami', $sendkamidata, array("weid" => $weid, "id" => $row['kid']));tPuPR: $params = array();r2jbZ: $binds = array();ahzKi: $sql = 'SELECT * FROM ' . tablename($this->tb_field) . ' WHERE `reid` = :reid ORDER BY `displayorder` DESC';CEkNK: load()->func('communication');FuDmN: $this->getFollow();LflYq: $this->showMessage($dayu_check_category['reminder'], $check_url, 'info');SGpZF: pdo_update('dayu_form', $record, array("reid" => $reid));kdLpT: $this->showMessage('活动还未开始！<br>开始时间：' . date('Y-m-d H:i:s', $activity['starttime']));XK5TP: $this->showMessage('非法访问，主题不存在');n1tuc: fw8o5:YyFZ0: $comment = pdo_fetchall('SELECT * FROM ' . tablename('dayu_comment') . ' WHERE weid = :weid and reid = :reid ORDER BY reid DESC', array(":weid" => $weid, ":reid" => $reid));D09W1: $returnUrl = urlencode($_W['siteurl']);r45ko: gzE0F:HXeFK:LueO1: Cr_iL:bh_9p: if ($kami['password'] != $_GPC['pass']) {}FWVr9: if (!empty($activity['starttime'])) {}sc3Lt: if ($ds) {}AZf_L: $row['linkage'] = iserializer($lg);rwSL4: L2EXL:XWuY1: mc_credit_update($uid, $behavior['activity'], $activity['credit'], array(0, $activity['title']));GzO_B: I7ac4:Ou4vR: if (empty($_GPC['linkage2'])) {}kHBpO: load()->func('file');Vl1Pl: $initRange = $initCalendar = false;kGWTU: AMZFF:MyTZF: ulQGs:pwCNX: qDODg:oYm0y: $row['lat'] = $_GPC['getlat'];PcfDg: $row['getip'] = $_GPC['getip'];AvDxN: $sql = 'SELECT * FROM ' . tablename($this->tb_field) . ' WHERE `reid` = :reid ORDER BY rand() limit ' . $par['randnum'];HM0ZN: global $_W, $_GPC;iTToF: !empty($activity['slide']) && ($slide = iunserializer($activity['slide']));Ru5Fz: $update['residedist'] = $_GPC['reside']['district'];rD6AG: $time = date('Y-m-d', time());EaXzW: $submitname = !empty($par['submitname']) ? $par['submitname'] : '立 即 提 交';S3Luc: $profile = mc_fetch($uid, $binds);CIpsb: DCYwH:fN8XT: $linkage = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_linkage) . ' WHERE reid = :reid and parentid = 0 ORDER BY displayorder desc, id DESC', array(":reid" => $reid));Vnza7: $params[':weid'] = $weid;vKYSJ: ihttp_post(murl('entry', array("do" => "Notice", "id" => $activity['smsnotice'], "m" => "dayu_sms"), true, true), $sms_data);o_T8D: $this->showMessage($_GPC['kami'] . ' 已使用', '', 'error');jGwCi: $_share['content'] = $activity['description'];dgDnp: foreach ($staff as $s) {mCpCq: $this->send_template_message(urldecode(json_encode($template)));BAEQV: $template = array("touser" => $s['openid'], "template_id" => $activity['k_templateid'], "url" => $_W['siteroot'] . 'app/' . $this->createMobileUrl('manageform', array("name" => "dayu_form", "weid" => $row['weid'], "id" => $row['reid'])), "topcolor" => "#FF0000", "data" => array("first" => array("value" => urlencode($activity['kfirst'] . '\\n'), "color" => "#743A3A"), "keyword1" => array("value" => urlencode($member), "color" => "#000000"), "keyword2" => array("value" => urlencode($_GPC['mobile']), "color" => "#000000"), "keyword3" => array("value" => urlencode($ytime), "color" => "#000000"), "keyword4" => array("value" => urlencode($bodym . $voice . $kamiinfo . '\\n'), "color" => "#FF0000"), "remark" => array("value" => urlencode($activity['kfoot']), "color" => "#008000")));BnLhx: ooHVZ:qsR7n: }D3y9j: ihttp_email($activity['noticeemail'], $activity['title'] . '的表单提醒', '<h4>姓名：' . $member . '</h4><h4>手机：' . $_GPC['mobile'] . '</h4>' . $body . $voice . $kamiinfo);f6wT2: setcookie('r_submit', $_GPC['repeat']);srjaI:JrnRy: $sendkami = pdo_get('dayu_sendkami', array("weid" => $weid, "cid" => $par['sendkami'], "status" => "0"), array("id", "number", "password"));hcm1k: $ycredit = $credits[$behavior['activity']] + $activity['credit'];CPR2N: $kamiinfo = !empty($row['kid']) ? '\\n　卡号：' . $kami['number'] : '';lHZil: mc_notice_credit1($openid, $uid, $activity['credit'], $log);J3muz: JGFyW:wYmLX: $uid && mc_update($uid, $update);iQJ8R: if (!(intval($par['daynum']) != 0)) {}oyW6J: x_dFp:S60GK: $template = array("touser" => $row['openid'], "template_id" => $activity['k_templateid'], "url" => murl('entry', array("do" => "mydayu_form", "op" => "detail", "id" => $row['reid'], "rerid" => $rerid, "m" => "dayu_form"), true, true), "topcolor" => "#FF0000", "data" => array("first" => array("value" => urlencode($activity['mfirst']), "color" => "#743A3A"), "keyword1" => array("value" => urlencode($member), "color" => "#000000"), "keyword2" => array("value" => urlencode($row['mobile']), "color" => "#000000"), "keyword3" => array("value" => urlencode($ytime), "color" => "#000000"), "keyword4" => array("value" => urlencode($kamiinfo), "color" => "#FF0000"), "remark" => array("value" => urlencode($activity['mfoot']), "color" => "#008000")));O9Khg: if (!(pdo_insert('dayu_form_info', $row) != 1)) {}yXPSL: sfuVE:etyA6:tSHBO: Eap4Q:TuIPP: $mname = !empty($par['mname']) ? $par['mname'] : '往期记录';w6ZwJ: c5UI7:VaRCX: if (!empty($datas)) {}sHnLN: $this->send_template_message(urldecode(json_encode($template)));C7T10: exit;SounT: $params[':reid'] = $reid;H9aHo: foreach ($_FILES as $key => $file) {jUEdZ: $error = $upfile['error'];RiH0i: $type = $upfile['type'];GUEeC: @mkdirs($upload_path);iU8fe: $desfile = $upload_path . $target_filename;VEZDn: imagejpeg($image, $srcfile);D6PkB: $upfilesize = !empty($activity['filesize']) ? $activity['filesize'] : 12;guSNa: $entry['data'] = $upload_path . $target_filename;FTtCa: IBICH:j9X00: IsOd3:B25el: if (is_array($ret)) {}Usnk6: RuH76:aXLFZ: $tmp_name = $upfile['tmp_name'];eGRuM: message('移动文件失败，请检查服务器权限', referer(), 'error');t8_VB: mkdir($upload_path);BPoZD: $upload_path = ATTACHMENT_ROOT . '/dayu_form/' . $weid . '/';L1mSH: VtmYi:tmZOe: $target_filename = 'form' . $reid . '_' . date('YmdHis') . mt_rand(10, 99) . '.thumb.jpg';gogJs: if (!($refid && $field && $file['name'] && $field['type'] == 'image')) {}PCi8s: $entry['rerid'] = 0;fu3WK: if (!($maxfilesize > 0)) {}eVK7y: $content = date('Y-m-d H:i:s', TIMESTAMP);v9LE0: $entry = array();edXwm: dwc_f:iVioT: $uptypes = array("image/jpg", "image/png", "image/jpeg");cAgpz: unlink($srcfile);euZCR: eGsgq:gbKd5: $entry['reid'] = $reid;vGW5_: $imginfo = getimagesize($srcfile);LdltK:DoKO3: if (!($size > $maxfilesize * 1024 * 1024)) {}imVj2: if (intval($error) > 0) {}a4UfQ: $maxfilesize = $upfilesize;j181g: NJocK:RzBKt: $source_filename = 'form' . $reid . '_' . date('YmdHis') . mt_rand(10, 99) . '.jpg';UlSbw: $refid = intval(str_replace('field_', '', $key));w75d6: f9kVV:K_8r0: $datas[] = $entry;M8bdN: message('上传文件类型不符：' . $type, referer(), 'error');qkNPo: p1XXF:E0l05: txNRH:Abnqx: $srcfile = $upload_path . $source_filename;tsAML: $ret = file_image_thumb($srcfile, $desfile, $avatarsize);BlkMR: if (move_uploaded_file($tmp_name, $upload_path . $source_filename)) {}yVNIR: $name = $upfile['name'];VUDsR: $upfile = $file;u_GIu: $entry['refid'] = $refid;X3L6C: message('上传文件过大' . $_FILES['file']['error'], referer(), 'error');Dy07I: if (in_array($type, $uptypes)) {}Ck9oM: $color = imagecolorallocatealpha($image, 255, 0, 0, 50);MpaAB: $field = $fields[$refid];AMUZd: load()->func('file');iBew5: $size = $upfile['size'];GTJRC: $image = $fun($srcfile);Nz6z1: $imgtype = image_type_to_extension($imginfo[2], false);JhmdV: if (!strexists($key, 'field_')) {}r3lgo: $avatarsize = !empty($activity['upsize']) ? $activity['upsize'] : 640;Cy55n: $fun = 'imagecreatefrom' . $imgtype;IxBmT: z4WJS:dr9eg: imagestring($image, 5, 10, 10, $content, $color);TfAo5: message('上传错误：错误代码：' . $error, referer(), 'error');lwZv2: if (file_exists($upload_path)) {}udcI3: }naBm0: if ($kami['status'] == 1) {}e4kkG: c2EqS:DJF1N: EtjW9:YUUra: if (!(pdo_tableexists('dayu_kami') && !empty($par['kami']))) {}zkl8k: if ($repeat == $_GPC['repeat']) {}vOzme: include $this->template('skins/' . $activity['skins']);wdLrG: $picker = 1;yHxO_: $outlink = !empty($activity['outlink']) ? $activity['outlink'] : $this->createMobileUrl('mydayu_form', array("name" => "dayu_form", "weid" => $row['weid'], "id" => $row['reid']));T17SW: $rerid = pdo_insertid();arjCM: $ytime = date('Y-m-d H:i:s', TIMESTAMP);Ydb1O: hbzy2:hX8Jp: if (!($groupid != $activity['mbgroup'])) {}Jw6r0: h7bqL:V7c7K: $alldata = array();GOFfh: if ($activity) {}WrerB: rtYyK:hWBps: ckPGX:lbFzl: $this->showMessage('您还不是会员,请先领取您的会员卡.', $to_card, 'info');kLsq8: foreach ($_GPC['thumb'] as $thumb) { $th[] = tomedia($thumb); NnRGU: }LqCKq: if (!($activity['status'] == 0)) {}vaeiT: PNrC6:CnJbD: if (!($_W['ispost'] || checksubmit('submit'))) {}zLYoQ: $update['mobile'] = $_GPC['mobile'];j5lCa: dssAv:qcvbR: E3j3h:IqBX9: yvSb7:wGTQt: $datas = $fields = $update = array();He_0n: hR2d5:jxgjL: if (!is_error($acc)) {}RIFWI:HivFO: if (!(intval($par['pretotal']) != 0)) {}DG_WL: $tomorrow = strtotime('tomorrow');hMPY_: $fans['user'] = mc_fansinfo($openid, $acid, $weid);a4Idx: $credits = mc_credit_fetch($_W['member']['uid'], '*');R6Dsr: $state = $this->get_status($row['reid'], 1);ZEg14: $row['createtime'] = TIMESTAMP;phEy9:H8lXE: jfHVM:DaAC_: $today = strtotime('today');fYn8Q: $this->showMessage('抱歉,每人只能提交' . intval($par['pretotal']) . '次！', '', 'info');eUfiD: if (empty($kami)) {}rz71h: $to_card = $par['card'] == 1 ? murl('entry', array("do" => "card", "m" => "we7_coupon", "returnurl" => $returnUrl), true, true) : murl('entry', array("do" => "index", "m" => "dayu_card", "returnurl" => $returnUrl), true, true);sdO6Z: $this->showMessage('卡号不存在', '', 'error');wUbhJ: OrKY9:sPEVe: $row['lng'] = $_GPC['getlng'];mfXaf: w0aE9:qXKOT: foreach ($_GPC as $key => $value) {YOAPZ: if (!in_array($field['type'], array("checkbox"))) {}eLK9O: tS8eA:Bn84R: OY0Bj:A7VEE: $entry['refid'] = $refid;RemjR: $refid = intval(str_replace('field_', '', $key));zbQVs:BuhVq: zfLM6:f3WR5: $update[$bindFiled] = $value;UxPkD: $entry = array();ImZgY: $entry['displayorder'] = $field['displayorder'];IVz9Z: $entry['data'] = implode(',', $value);Cx64X: $entry['rerid'] = 0;q3Yjo: if ($field['loc'] == '-1') {}Ro5xN: if (!strexists($key, 'field_')) {}xvZGo: r6w9T:Fqbev: Kpgqp:zbegU: $entry['reid'] = $reid;qnmII: if (empty($bindFiled)) {}h5ax2:lKydw: $bindFiled = substr(strrchr($key, '_'), 1);sEIiG: $entry['data'] = trim($value);ADzVN: HBGnD:UKdh4: if (!in_array($field['type'], array("number", "text", "calendar", "email", "radio", "textarea", "range", "select", "image", "reside", "photograph", "tingli", "phone", "tel", "idcard"))) {}rC0Eu: if (!($refid && $field)) {}i2JUL: $entry['data'] = implode(',', $value);RXwVt: $datas[] = $entry;qWtrl: scTtU:Sk5bX: a0Aa2:M_xPN: $field = $fields[$refid];MYrDz: if (is_array($value)) {}tePu7: HqY_8:uDj8S: }XFyH3: foreach ($datas as &$r) {b3P1Z: pdo_insert('dayu_form_data', $r);FDtTa: YppXU:ASuJI: $r['rerid'] = $rerid;LHHmi: }T_ExX: if (!($par['card'] == 1 || $par['card'] == 2)) {}uA8AU: $row['var2'] = $_GPC[$par['var2']];q8i59: dfSAp:faNco: $kamidata = array("reid" => $reid, "infoid" => $rerid, "addons" => "dayu_form", "openid" => $openid, "name" => $row['member'], "mobile" => $row['mobile'], "status" => 1, "updatetime" => TIMESTAMP);NuE0T: pdo_update('dayu_kami', $kamidata, array("weid" => $weid, "id" => $row['kid']));sxRy4: $acc = WeAccount::create($_W['acid']);ls7Pk: UrT63:aLljw: foreach ($_GPC['reside'] as $key => $value) {y8kTL: $resideData = array("reid" => $reside['reid']);BEDQx: $resideData['rerid'] = 0;R2X7P: kK5na:Ln8tq: $resideData['data'] = $value;qOgM_: $datas[] = $resideData;vRyrg: $resideData['refid'] = $reside['refid'];WbXIb: }csGYS: $this->showMessage('保存失败。');ylPnN: $qqkey = $set['qqkey'];rPOG2: L12N8:YOe4X: foreach ($ds as &$r) {wXU6B: V43Z3:Bxprd: Nrb3Y:WxFkF: EQz0s:wRT2i: e2VLM:LKAZS: if ($r['bind'] == 'email' && strexists($profile[$r['bind']], 'we7.cc')) {}hc8QO:YVVOA: $r['tixing'] = !empty($r['description']) ? urldecode($r['description']) : '请选择' . $r['title'];KyIuR: $isloc = $r;DvcQk: k3M2Z:JXU4o: if (!$profile['gender']) {}VVEC5: knZzq:W3ZoU: AgoKf:eLY9k: $binds[$r['type']] = $r['bind'];YanQC: if (!$r['bind']) {}aYeUF: Nj32n:f25LS: $activity['smsid'] && $r['bind'] == 'mobile' && empty($profile[$r['bind']]) && $this->showMessage('请完善手机号', murl('entry', array("do" => "index", "id" => $activity['smsid'], "m" => "dayu_sms", "form" => $_W['current_module']['name'], "returnurl" => $returnUrl), true, true), 'info');wz3lO: scBKK:EhTbN:mM4zg: jPzXE:B8CMa: $r['default'] = '';UfoFy: $r['options'] = explode(',', $r['value']);qhyQO: if (!(!empty($r['loc']) && pdo_tableexists('dayu_form_plugin_radio') && ($r['type'] == 'radio' || $r['type'] == 'checkbox'))) {}LosD1: $profile['gender'] = '女';Hssjd: $initRange = true;jvm3j: MxlVQ:pgyn6: JxcgQ:U0yAh:uaV1R: $profile['gender'] = '男';Iw68f: xtA0H:rSgN7: sUCIb:mBIry: $r['default'] = $profile[$r['bind']];bvT5M: pdo_tableexists('dayu_photograph_fields') && ($r['photograph_url'] = murl('entry', array("do" => "index", "f" => $r['bind'], "m" => "dayu_photograph", "returnurl" => $returnUrl), true, true));cFJXd: $initCalendar = true;qRqLO: if (!$r['value']) {}x6Iyq: glBqv:mz5xI: TqBij:Uw7wl: AgHL9:HwGuD: Dqtg_:ZePuO: if (!($profile['gender'] == '2')) {}Y5DKH: $r['image'] = !empty($r['image']) ? $r['image'] : TEMPLATE_WEUI . 'images/nopic.jpg';pp7Gq: if (!($r['type'] == 'text' && $r['loc'] > 0)) {}ylhRr: if (in_array($r['type'], array("radio", "checkbox"))) {}flQp1: $r['dayu_radio'] = pdo_fetchall('SELECT * FROM ' . tablename('dayu_form_plugin_radio') . ' WHERE weid = :weid and cid = :cid and status=1 ORDER BY id DESC', array(":weid" => $weid, ":cid" => $r['loc']));XBgZC: $reside = $r;Or5Kw: if (!($profile['gender'] == '1')) {}OeeAO: if (!($profile['gender'] == '0')) {}uZGZD: if (!($r['type'] == 'reside')) {}NVvcG: JkAfk:e_I_y: $r['tixing'] = !empty($r['description']) ? urldecode($r['description']) : '请填写' . $r['title'];jsdBd: if (!($r['type'] == 'range')) {}oWjPc: $r['type'] == 'photograph' && empty($profile[$r['bind']]) && $this->showMessage('请完善' . $r['title'], murl('entry', array("do" => "index", "f" => $r['bind'], "m" => "dayu_photograph", "returnurl" => $returnUrl), true, true), 'info');vByN1: if (!($r['type'] == 'calendar')) {}a0wuV: $profile['gender'] = '保密';qrgH0: if (!$profile[$r['bind']]) {}Ys6jG: if (in_array($r['type'], array("text", "number", "email", "textarea", "idcard", "phone", "tel"))) {}Tf5_4: if (!($r['type'] == 'image')) {}nt2Yu: }yDLED: $params = array();eQKUJ: if (!($lognum >= intval($par['daynum']))) {}O0_3z: $this->showMessage($activity['information'], $outlink);p81Lx: $settings = uni_setting($_W['uniacid'], array("creditnames", "creditbehaviors", "uc", "payment", "passport"));TiiTn: jkRWy:Iw7hS: T3d94:gPywp: $this->showMessage($activity['information'], $this->createMobileUrl('mydayu_form', array("id" => $reid)));GxeNm: $this->showMessage('抱歉,每天只能提交' . intval($par['daynum']) . '次！', $this->createMobileUrl('mydayu_form', array("name" => "dayu_form", "weid" => $weid, "id" => $reid)), 'info');J3m9z: if (!(pdo_tableexists('dayu_kami') && !empty($par['kami']))) {}m_NN0: WZjgg:fXmJa: if (empty($_GPC['reside'])) {}EH2Vn: if (!(intval($par['allnum']) != 0)) {}LUDhH: if (!($activity['starttime'] > TIMESTAMP)) {}ZGNBK: $dayu_check = pdo_get('dayu_check', array("weid" => $weid, "openid" => $openid, "cid" => $dayu_check_category['id'], "status" => 1), array("id"));k3AiV: $_share['imgUrl'] = tomedia($activity['thumb']);uH71z: mc_group_update($uid);UqHwO: $group = pdo_fetch('SELECT * FROM ' . tablename('mc_members') . " WHERE uniacid = '{$weid}' AND uid = '{$uid}'");RdyQu: if (!($ishy == false)) {}AH5ab: $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `reid`=:reid';A6nP0: KujQI:olmZY: if (!(!empty($activity['mobile']) && !empty($activity['smsnotice']))) {}jvwM0: if (!(pdo_tableexists('dayu_check') && $par['dayu_check'])) {}ULkYV: $lognum = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_info) . " WHERE openid = '{$openid}' AND reid = '{$reid}' AND createtime > " . $today . ' AND createtime < ' . $tomorrow);P9Gd5: $activity = pdo_fetch($sql, $params);n7zmZ: foreach ($ds as $value) { $fields[$value['refid']] = $value; ZwqFM: }bxjV2: $yuyuetime = date('Y-m-d H:i', time() + 3600);UMJud: $row['kid'] = $sendkami['id'];SUgLD: GMThw:hcNDz: if (!($pretotal >= intval($par['pretotal']))) {}Ykh5d: $record = array();M1Cjv: $title = $par['title'] ? $par['title'] : $activity['title'];tbo_k: $this->showMessage($kami['password'] . '卡号与密码不匹配', '', 'error');YafqA: ihttp_post(murl('entry', array("do" => "print", "printid" => $par['print'], "m" => "dayu_print"), true, true), array("title" => $activity['title'], "realname" => $member, "mobile" => $_GPC['mobile'], "info" => $bodyp, "createtime" => $row['createtime']));xNHPt: $allnum = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_info) . ' WHERE reid = :reid and (status=0 or status=1 or status=3)', array(":reid" => $reid));r6P5a: $_share['title'] = $title;RF9D6: $row['voice'] = !empty($_GPC['voice']) ? $setting['qiniu']['host'] . '/' . $_GPC['voice'] : '';CqgSJ: gnvKx:VAkJp: if (!(pdo_tableexists('dayu_sendkami') && !empty($par['sendkami']))) {}IX6rG: $row['reid'] = $reid;bC9m7: $row = array();L0_pU: if (!($activity['credit'] != '0.00' && $par['icredit'] == '0')) {}CBMK4: !empty($activity['linkage']) && ($la = iunserializer($activity['linkage']));VdwQo: V27Or:VkUrj: v0uUZ:wFGza: setcookie('r_submit', $_GPC['repeat']);y_G0v: KCnPg:AEZAb: $row['member'] = $_GPC['member'];xNyXv: $row['getadd'] = $_GPC['getadd'];NpsYv: $formdata = $this->order_foreach($row['reid'], $rerid);mBqFN: UAs2b:wbjWs: HNt80:OFY9j: $update['residecity'] = $_GPC['reside']['city'];Wsnq0: $this->showMessage('非法访问，提交数据不能为空', '', 'error');EIjvc: if (!in_array('reside', $binds)) {}BReBm: dOo2Y:Q08YG: YN3gP:fKAUh: b0DKx:luZ5j: $kamiinfo = !empty($row['kid']) ? '\\n　卡号：' . $sendkami['number'] . '\\n　密码：' . $sendkami['password'] : '';t4meQ: $params = array();f8blP: $dayu_check_category = pdo_get('dayu_check_category', array("weid" => $weid, "id" => $par['dayu_check']), array("id", "reminder"));u1nH3: $pretotal = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_info) . ' WHERE reid = :reid AND openid = :openid', array(":reid" => $reid, ":openid" => $openid));u0nyc: if ($dayu_check) {}U8gLC: $check_url = murl('entry', array("do" => "index", "id" => $par['dayu_check'], "reid" => $reid, "formdo" => $_GPC['do'], "form" => $_W['current_module']['name'], "m" => "dayu_check", "returnurl" => $returnUrl), true, true);OS6Gf: kHx6c:cJVXT: $row['var3'] = $_GPC[$par['var3']];BESVD: $creditnames = $settings['creditnames'];iQEUi: $this->showMessage('您所在会员组没有相关的操作权限！', '', 'info');RfrfH: foreach ($datas as $d) {T3s0n: $smsbody .= $fields[$d['refid']]['title'] . ':' . $d['data'] . '，';IBedJ: $bodym .= '\\n　' . $fields[$d['refid']]['title'] . ':' . $d['data'];wRfGb: $d['data'] = '有';GtMxN: if (!strstr($d['data'], 'images')) {}cPOcY: h4pQn:MvdnC: $body .= '<h4>' . $fields[$d['refid']]['title'] . '：' . $d['data'] . '</h4>';olNPq: $bodyp .= $fields[$d['refid']]['title'] . '：' . $d['data'] . '|';SXw6F: nit1t:A0tXn: }ljZ5Q: $this->showMessage('活动已经结束！<br>截至时间：' . date('Y-m-d H:i:s', $activity['endtime']));yMy9r: $record['starttime'] = TIMESTAMP;pyg30: $ishy = $this->isHy($openid);zzoGk: $voice = !empty($_GPC['voice']) ? '\\n　有' . $activity['voice'] : '';Rol4n: $update['resideprovince'] = $_GPC['reside']['province'];KoNSs: if (!($activity['paixu'] != '2')) {}LNHts: if (!(pdo_tableexists('dayu_print') && !empty($par['print']))) {}BeiFw: exit;bv_Fx: if (empty($_GPC['repeat'])) {}MEeRt: $btncolor = $this->get_color($reid, $par['btncolor']);fysmu: if ($par['isrand'] == 1 && !empty($par['randnum'])) {}o677x: require MODULE_ROOT . '/fans.mobile.php';ouROi: if (!($activity['mbgroup'] != 0)) {}SvmXB: if (!is_array($_GPC['thumb'])) {}L6_ru: $staff = pdo_fetchall('SELECT `openid` FROM ' . tablename($this->tb_staff) . ' WHERE reid=:reid AND weid=:weid', array(":weid" => $_W['uniacid'], ":reid" => $row['reid']));Tdyce: $activity['thumb'] = tomedia($activity['thumb']);Q9r1A: if (!(pdo_tableexists('dayu_sendkami') && !empty($par['sendkami']))) {}c3Vrl: load()->func('tpl');Byxk2:D_B8e: $this->showMessage('活动已经停止.');kMXKf: zRGZm:zsnDI: if (!is_array($staff)) {}eCyYh: H58MT:dUDuy: A9jrs:xsMkN: $sendkami = pdo_get('dayu_sendkami', array("weid" => $weid, "cid" => $par['sendkami'], "id" => $row['kid']), array("id", "number", "password"));nwn8u: $reid = intval($_GPC['id']);DwyGl: $row['thumb'] = iserializer($th);IldGH: P_K_l:BNaDc: $acc = notice_init();vuj65: } public function doMobileConsult() {fJooG: if ($check['status'] == '3') {}y5TAv:WfB6k: $cid = intval($_GPC['cid']);bZRIW: $this->showMessage('已完成，关闭咨询.', referer(), 'error');EBhBc: IRJWq:ZzwAf: $title = '在线咨询';cVSx7: sHN7p:p1WMQ: FToYT:JUcPb: $check = pdo_get($this->tb_info, array("rerid" => $rerid), array());WwDpT: $this->showMessage('系统错误.', referer(), 'error');VWEIt: $rerid = intval($_GPC['rerid']);YFfCW: $staff = '1';pekbx: $back_url = $this->createMobileUrl('manageform', array("op" => "detail", "rerid" => $rerid, "id" => $reid));s7seO: $back_url = $this->createMobileUrl('mydayu_form', array("op" => "detail", "rerid" => $rerid, "id" => $reid));reuNg: if (!(empty($cid) || empty($reid) || empty($rerid) || empty($weid) || empty($openid))) {}PQMOy: yM48q:yxqP_: global $_W, $_GPC;Q_OPH: $staff = '0';yoJ90: include $this->template('consult');DScKP: Z6i3d:YZ3gl: $reid = intval($_GPC['reid']);ry3mI: if ($check['openid'] != $openid && !$isstaff) {}zfJJE:oYBTX:LlYp3: require MODULE_ROOT . '/fans.mobile.php';va1xS: if ($form == '1' && $isstaff) {}j_Bqg: $form = intval($_GPC['form']);CJt79: $title = '在线咨询管理';v2_DB: $this->showMessage('非法访问.', referer(), 'error');Jv5EB: $isstaff = $this->get_staff($openid, $reid);uvBm3: sOZ_o:wHNcs: } public function get_staff($openid, $reid) { global $_GPC, $_W; return pdo_get($this->tb_staff, array("weid" => $_W['uniacid'], "openid" => $openid, "reid" => $reid), array()); } public function doMobileUploads() {jmM4H: if (!($type == 'image')) {}YCIvu: $result['localId'] = $localId;Oc4ai: $result['path'] = $filename;txqGg: global $_W, $_GPC;Ad_SB:Bu1cX: load()->classs('account');MZbIA: $serverId = trim($_GPC['serverId']);WSFUM: $acid = $_W['acid'];vRYuZ: $result['status'] = 'success';kXzBt: d_p_8:WR7Db: $type = !empty($_GPC['type']) ? $_GPC['type'] : 'image';irN5q: exit(json_encode(array("status" => true)));uA5cz: GvReF:SWts6: $localId = trim($_GPC['localId']);nSoTU: $result['imgurl'] = $_W['attachurl'] . $filename;iWeSw: $acc = WeAccount::create($acid);htI0t: $params = array(":openid" => $_W['openid'], ":uniacid" => $_W['uniacid']);EGGLl: $_W['acid'] = pdo_fetchcolumn($sql, $params);qVwUE: if (!empty($_W['acid'])) {}yAy4D: file_delete($file);jZUSm: $media = array();WyWIL: $media['type'] = $type;g3VWh: AnLw8:LjqcW: $sql = 'SELECT acid FROM ' . tablename('mc_mapping_fans') . ' WHERE openid = :openid AND uniacid = :uniacid limit 1';wVPVp: if (!empty($_W['acid'])) {}OxdwR: $operation = !empty($_GPC['op']) ? $_GPC['op'] : 'upload';oq3Dd: $result['message'] = '上传失败';cVUEQ: $file = $_GPC['file'];YvEHG: $filename = $acc->downloadMedia($media, true);CkrcC: if (is_error($filename)) {}q_de3:U52Cc: $result['status'] = 'error';EUlwz: ImQWp:cjHeI: $media['media_id'] = $serverId;e8YtW: $result['message'] = '没有找到相关公众账号';NQhHl: if ($operation == 'upload') {}MWJ_O: if ($operation == 'remove') {}srfDo: k4Jcd:SctYQ: jfIBU:nj9gU: $result = array("status" => "error", "message" => "123", "data" => "");Ihfvq:IpYOa: die(json_encode($result));G_QFZ: $result['message'] = '上传成功';Raua7: ICvh8:xH7jp: $result['status'] = 'error';INBjH: sL31z:uPoQg: $result['serverId'] = $serverId;CI3Hc: } public function doMobileUpload() {fzjj6: unlink($upload_path . $pathname);HU6KW: mc_update($_W['member']['uid'], $data);eJTR5: message('远程附件上传失败，请检查配置并重新上传');WP1iR: load()->func('file');s9YvW: $pathname = $images_path . $pic;wikNR: $picurl = $upload_path . $images_path . $pic;sj4gB: $params[':reid'] = $reid;CNRmq: $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `reid`=:reid';Swiku:snjH0: $targetName = $picurl;Jtfjf: load()->classs('weixin.account');qH3s2: global $_W, $_GPC;FwBPn: load()->func('tpl');UVA3I: if (file_exists($upload_path . $images_path)) {}RiC8W: if (empty($_W['setting']['remote']['type'])) {}toeka: $params = array();Q_cVG: $reid = intval($_GPC['id']);kD273: $data = array("avatar" => $images_path . $pic);rSbhX: $picurl = $upload_path . $images_path . $pic;ny2fl: $remotestatus = file_remote_upload($pathname);Qmz_u: $access_token = $accObj->fetch_token();P2k8a: if ($reid) {}Weobn: xvG3K:gSbec: $ch = curl_init($url);bi8LY: curl_setopt($ch, CURLOPT_HEADER, 0);FoSKx: curl_close($ch);rM_NC:qg2y9: @mkdirs($upload_path . $images_path);U5uo4: $pic = 'avatar_' . date('YmdHis') . mt_rand(1000, 9999) . '.jpg';vRAvQ: curl_setopt($ch, CURLOPT_FILE, $fp);OX__Y: D2oZF:qCeW1: $media_id = $_GET['media_id'];N0X0l: wvlZu:oRbue:KWi5b: load()->func('file');aZ5pM: $upload_path = ATTACHMENT_ROOT;o2Tno: nMrpO:Qk3AM: gBXwy:kp3n1: if ($_GPC['type'] == 2) {}kv0_9: fclose($fp);WXo8K: $fp = fopen($targetName, 'wb');UaWIs: gOwbg:UdPnV: $url = 'http://file.api.weixin.qq.com/cgi-bin/media/get?access_token=' . $access_token . '&media_id=' . $media_id;Iz_zF: $pic = 'form' . $reid . '_' . date('YmdHis') . mt_rand(1000, 9999) . '.jpg';paYS4: $images_path = 'dayu_form/' . $_W['uniacid'] . '/';WTNvw: $remoteurl = $pathname;hJML_: JAW13:Dy75G: mkdir($upload_path . $images_path);irbN_: echo $pathname;yhaOO: $activity = pdo_fetch($sql, $params);EgBog: curl_exec($ch);hsb3n: $params[':weid'] = $weid;xjGWa: if (is_error($remotestatus)) {}s_4aQ: $accObj = WeixinAccount::create($_W['uniacid']);waaH2: load()->model('mc');kCp32: } public function download_voice($media_id, $retry = 0) {VrSUp: global $_W, $_GPC;JoSKG: load()->func('communication');kx5Nf: Bf5bf:BmHU1: mdMgc:wq0r4: EqE90:qKa_q: $this->download_voice($media_id, 1);WLSWr: $access_token = WeAccount::token();NuUPf: $voice = ihttp_get('http://file.api.weixin.qq.com/cgi-bin/media/get?access_token=' . $access_token . '&media_id=' . $media_id);H5kTM: qhWmK:TWao0: return false;iDVB1:Ms3Cm: return $voice['content'];oTsAc: if (isset($voice['headers']['Content-disposition'])) {}XENHw: if (!($retry === 0)) {}lyxdA: if (!$media_id) {}yIyqS: } public function upload_qiniu_voice($filename, $content) {dd8sa: WWe7W:YzPd2: if (isset($r['persistentId']) && !empty($r['persistentId'])) {}jgH98: wjx0X:vqr0v: jL6Ad:hnTjU: $setting = $this->module['config'];z9R3P: return '';YDzKK: $qiniu = new Qiniu($setting['qiniu']);t7q1i:QQlfn:NoO2u: return '';oxWIU: return $r['persistentId'];bxBIC: $pipeline = $setting['qiniu']['pipeline'];ZSOAO: aC_Tw:MZV1i: $r = $qiniu->putContent($filename, $content, $pipeline);nrXkq: if ($r === false) {}NURNa: if (empty($setting['qiniu'])) {}FgaDf: CJ7_Z:a3GqT: require MODULE_ROOT . '/Qiniu.class.php';dFzMY: } public function doMobileUploadvoice() {ZXLMb: global $_W, $_GPC;Thncu: beZlV:DTyQK: $this->showMessage('serverId为空');gjrOq: if (!$content) {}gxdpB: $filename = 'dayu_form_' . $_W['uniacid'] . '_' . $_GPC['serverId'] . '.mp3';T9E50: $content = $this->download_voice($_GPC['serverId'], '');rsElS: $r = $this->upload_qiniu_voice($filename, $content);dpXCb: if (!empty($_GPC['serverId'])) {}Nddb8: LggXo:Dkry0: $setting = $this->module['config'];pbCDj: } public function doMobileGetprefop() {WRi6L: vKYKP:QcFxd: echo '1';yidyX: if (!($r['code'] == 0)) {}l1vrJ: jA_1z:u2XHo: global $_W, $_GPC;bS2JH: if (!$_GPC['persistentId']) {}KEj60: $r = json_decode($r, true);Vgh1D: if (!isset($r['code'])) {}U06u0: $r = file_get_contents('http://api.qiniu.com/status/get/prefop?id=' . $_GPC['persistentId']);H3qvv: bsJE8:JOGsm: } public function doMobiletest() { global $_W, $_GPC; include $this->template('test'); } public function doMobileUploadVideo() {Me__x: $filedata = array("media" => '@' . $filename);vj531: $access_token = WeAccount::token();K1w1N: var_dump($result);BsDTY: $filename = 'bbb.mp4';C_AiA: $url = 'https://api.weixin.qq.com/cgi-bin/media/upload?type=video&access_token=' . $access_token;vvh8h: $result = ihttp_request($url, json_encode($filedata));hrvP1: global $_W, $_GPC;meLsR: die;GuIPX: load()->func('communication');Ceied: } public function curl_post($sucai, $img) {M7nmI: $ch = curl_init();GkGSe:EnQ48: At3EI:FgG84: curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);LtESZ: return $output;qxKn4: curl_setopt($ch, CURLOPT_SAFE_UPLOAD, true);ub0aR: curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);xfazL: curl_setopt($ch, CURLOPT_POSTFIELDS, $data);symFC: curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);OY3Ql: MlbYv:ctQCL: curl_setopt($ch, CURLOPT_POST, 1);cIHFM: curl_setopt($ch, CURLOPT_SAFE_UPLOAD, false);cklGZ: if (class_exists('CURLFile')) {}ziE1l: $data = array("media" => new \CURLFile(realpath($img)));A6tk0: curl_close($ch);s5Nsk: $data = array("media" => '@' . realpath($img));p8Chj: Idrwr:nFaWX: if (!defined('CURLOPT_SAFE_UPLOAD')) {}NsRHZ: curl_setopt($ch, CURLOPT_URL, $sucai);Ek0vk: $output = curl_exec($ch);Yw6fy: } function curl_post2($url, $data = null) {KQDnD: return $output;UlGkQ: curl_setopt($curl, CURLOPT_POSTFIELDS, $data);qfVpx: curl_close($curl);p680O: curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);cEIln: if (empty($data)) {}xQDQj: curl_setopt($curl, CURLOPT_POST, 1);ma_0Y: $output = curl_exec($curl);AgPFn: $curl = curl_init();yT1t4: W91OB:ITuQ4: curl_setopt($curl, CURLOPT_URL, $url);sRuhf: } public function doMobileMydayu_form() {auh_7: $total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_info) . " WHERE openid = :openid and reid = :reid {$where}", array(":openid" => $_W['openid'], ":reid" => $reid));y_CGj: $ds = $fids = array();tTWq_:ivuWA: mJMR8:LRcIE: $consult = pdo_fetch('SELECT id,createtime FROM ' . tablename('dayu_consult') . ' WHERE infoid = :infoid and openid = :openid and rid = \'0\' ORDER BY createtime DESC', array(":infoid" => $row['rerid'], ":openid" => $openid));WKGav: WHOAG:TPkfR: $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `reid`=:reid';FC9FH: if (empty($row['rethumb'])) {}r234I: z12_G:Rfl_p: $pindex = max(1, intval($_GPC['page']));pBZfG: $this->getFollow();kqzvO:MeS5t: $row = pdo_fetch('SELECT * FROM ' . tablename($this->tb_info) . ' WHERE openid = :openid AND rerid = :rerid', array(":openid" => $_W['openid'], ":rerid" => $rerid));LuWVs: bpffA:fBf01: if (!is_array($linkage)) {}ifnjS: $new_array = array();xhSh1:kiuCg: tklnG:FEPCE: foreach ($new_array as $u => $v) { $last[] = $u; KT0ap: }oOzUV: $status = intval($_GPC['status']);ueow1: $mname = !empty($par['mname']) ? $par['mname'] : '往期记录';Y545x: $dayu_form['content'] = htmlspecialchars_decode($dayu_form['content']);RRWf3: $user_footer = 1;YdLmg: CUQrV:hGsUD: $setting = $this->module['config'];Hczc2: $status = intval($_GPC['status']);ZqjlB: $pindex = max(1, intval($_GPC['page']));cnbBj: $this->showMessage('非法访问.');Rr7K2:G5ySY: $rerid = array_keys($rows);GTxPL: $total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_info) . " WHERE openid = :openid {$where} ", array(":openid" => $_W['openid']));GjBIS: $member = mc_oauth_userinfo($_W['acid']);pm4C3: Gjm3J:S3x2j: $linkage['l1'] = $this->get_linkage($linkage['l1'], '');CHCcS: WwlK7:llvce: foreach ($rows as $key => $val) {FjBzu: $rows[$key]['user'] = mc_fansinfo($val['openid'], $acid, $weid);Tkcx1: $rows[$key]['kf'] = mc_fansinfo($val['kf'], $acid, $weid);Vs7Q8: $rows[$key]['comment'] = !empty($par['comment']) && pdo_tableexists('dayu_comment') && !empty($val['commentid']) ? $this->get_comment($val['commentid']) : '';BHkVh: $rows[$key]['status'] = $this->get_status($val['reid'], $val['status']);h3yS5: nOds6:dYaqa: }pdYmV: $linkage = iunserializer($row['linkage']);s5FQx: foreach ($fdatas as $fd) { $row['fields'][$fd['refid']] = $fd['data']; GAOpv: }F7RiY: rpXP_:pR7fc: XsFmm:nCJoc: if (!$fids) {}PE2aH: $params = array();F9ILw: $operation = !empty($_GPC['op']) ? $_GPC['op'] : 'display';S9sav: $par = iunserializer($activity['par']);qlmJ9: dC7K3:XFkN0: zSZbt:S2eqI: EHOUO:Cs46v: tnHZi:v9FFb: if (!empty($member['avatar'])) {}tLQQI: $consultr = pdo_fetch('SELECT id,createtime FROM ' . tablename('dayu_consult') . ' WHERE infoid = :infoid and rid = \'1\' ORDER BY createtime DESC', array(":infoid" => $row['rerid']));tj_9u: if (!empty($row)) {}GvdAJ: $fdatas = pdo_fetchall($sql, $params);j30DQ: if ($par['follow'] == 1) {}hp6s4: $sql = 'SELECT * FROM ' . tablename($this->tb_field) . ' WHERE `reid`=:reid ORDER BY displayorder DESC, refid DESC';KP3Fs: $row['createtime'] = !empty($row['createtime']) ? date('Y-m-d H:i', $row['createtime']) : '时间丢失';YbOL4: foreach ($ds as $value) {MY0im: if (!($value['type'] == 'reside')) {}maMpi: z6I6W:LLqDP: nv2Qf:mxfUU: $row['fields'][$value['refid']] = '';O1j7X: foreach ($fdatas as $fdata) {G3pqF: if (!($fdata['refid'] == $value['refid'])) {}DonkA: ET8V6:ctcEh: ireVx:Spxve: $row['fields'][$value['refid']] .= $fdata['data'];y1GXf: }pMnH1: YaVwC:w9WJE:KOgNg: }KSAsW: if (!($_GPC['status'] != '')) {}IO3uF: if (!(pdo_tableexists('dayu_kami') && !empty($par['kami']) && $row['kid'])) {}N5cvZ: $row['voices'] = $row['voice'];xPMrn: $row['thumb'] = iunserializer($row['thumb']);kGwjp: if (!(pdo_tableexists('dayu_sendkami') && !empty($par['sendkami']) && $row['kid'])) {}U4pcu: if (!(!empty($consult['id']) && $consult['createtime'] < $consultr['createtime'])) {}F_Bq5: if ($openid) {}hRvxR: if ($reid) {}eyuD3: $psize = 10;HZjuj: include $this->template('dayu_form');iRdZJ: $row['fields'] = array();AKxZJ: $row['file'] = iunserializer($row['file']);dg95Y: $row['user'] = mc_fansinfo($row['openid'], $acid, $weid);aBFPB: $where .= ' and ( status=2 or status=-1 )';ufWmS: ODit7:wIKQv: $rows = pdo_fetchall($sql, $params);ReMBY: $pager = $this->pagination($total, $pindex, $psize);IM1CY: foreach ($rows as $v) { $new_array[$v['reid']] = 1; NZFyI: }ddmYs: BWNyf:ApKEO: $kami = pdo_get('dayu_sendkami', array("weid" => $weid, "id" => $row['kid']), array());IsHkj:ltc03: $params[':reid'] = $row['reid'];H_gY2: $children = array();D4wsF: $fields = pdo_fetchall($sql, $params);oLW2E: Jh_Wb:W5xE3: $list = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_form) . " WHERE weid = :weid and reid in({$fids}) and status = 1 ORDER BY reid DESC", array(":weid" => $weid), 'reid');MdrN9: Etf_y:DV14n: $kami = pdo_get('dayu_kami', array("weid" => $weid, "id" => $row['kid']), array());xKuHl: $sql = 'SELECT `reid` FROM ' . tablename($this->tb_info) . ' WHERE openid = :openid ORDER BY rerid DESC';kN4ke: $row['rethumb'] = iunserializer($row['rethumb']);hI9Wd:hh0Mq: WmA2w:zKLZk: $linkage['l2'] = $this->get_linkage($linkage['l2'], '');C0GEE: $where .= " and status={$status}";Qxdmz: $status = $this->get_status($row['reid'], $row['status']);ngIFn: foreach ($fields as $f) {ZnkRa: $ds[$f['refid']]['fid'] = $f['title'];Hws4t: g0ft3:EiiJK: $ds[$f['refid']]['refid'] = $f['refid'];mkXdP: $ds[$f['refid']]['loc'] = $f['loc'];k4jJ4: $fids[] = $f['refid'];nwDUG: $ds[$f['refid']]['type'] = $f['type'];n0TMg: }wN4nJ: b_Y2E:n7JPe: $this->showMessage('非法访问');Tupm_: UtKF_:SwzMt: TMf4w:rbzuf: if (!(!empty($par['comment']) && pdo_tableexists('dayu_comment') && !empty($row['commentid']))) {}mpihQ: require MODULE_ROOT . '/fans.mobile.php';nWWMR: $la = iunserializer($activity['linkage']);uTeq0: if ($status == 2) {}U_EpL: rYFtU:miWZN: $member = mc_oauth_fans($openid, $_W['acid']);vnIe7: $params = array();Xo66q: foreach ($childlist as $reply => $r) {BzJcF: $children[$r['rerid']][] = $r;zYq2W: unset($children[$reply]);BbXt1: if (empty($r['rerid'])) {}CLPpQ: uLnAR:P50H2: HRbyS:TpN8Z: }V4PGJ: $params[':reid'] = $reid;yMExJ: EQ7mF:zbVlV: Z3AYb:LV6Mh: if ($status == 2) {}UvyYU: tjhyD:dgkbn: $where .= ' and ( status=2 or status=-1 )';V3IDa: fDuOb:fAKJV: $params[':weid'] = $_W['uniacid'];RQ8JX: $rerid = intval($_GPC['rerid']);vSN_V: $rows = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_info) . " WHERE openid = :openid {$where} ORDER BY rerid DESC LIMIT " . ($pindex - 1) * $psize . ",{$psize}", array(":openid" => $_W['openid']));ADeU8: $activity = pdo_fetch($sql, $params);kACHJ: if ($operation == 'detail') {}wUQnt: $last = array();Tkupl: $params[':openid'] = $openid;kvlLW: $reid = intval($_GPC['id']);Aykgy: $sql = 'SELECT * FROM ' . tablename($this->tb_data) . " WHERE `reid`=:reid AND `rerid`='{$row['rerid']}' AND `refid` IN ({$fids})";wOBo0: if (!empty($fields)) {}rQkfC: $where .= " and status={$status}";fqplq: $this->showMessage('记录不存在或是已经被删除！');OqGYQ: $fids = implode(',', $last);V2PUo: $list = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_form) . ' WHERE weid = :weid and status = 1 ORDER BY reid DESC', array(":weid" => $weid), 'reid');v5Ji9: $rows = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_info) . " WHERE openid = :openid and reid = :reid {$where} ORDER BY rerid DESC LIMIT " . ($pindex - 1) * $psize . ",{$psize}", array(":openid" => $_W['openid'], ":reid" => $reid), 'rerid');L3h_0: nTnwT:MZc0j: $c_tishi = '<span class="weui-badge right" style="margin-left: 5px;position: absolute;top:5px;right:5px;">有新回复</span>';RBejj: if ($operation == 'display') {}y9rA_: $row['revoices'] = $row['revoice'];zmoRp: $fids = implode(',', $fids);BkXzS: $member = !empty($member) ? $member : $_SESSION['userinfo'];eAceS: global $_W, $_GPC;fRGcz: $comment = pdo_get('dayu_comment', array("weid" => $weid, "id" => $row['commentid']), array());ZmQCm: Qr93W:LEpJL: if (!pdo_tableexists('dayu_consult')) {}jN1W_: $row['yuyuetime'] = !empty($row['yuyuetime']) ? date('Y-m-d H:i', $row['yuyuetime']) : '请等待客服受理';RpUoe: $childlist = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_data) . ' WHERE rerid IN (\'' . implode('\',\'', is_array($rerid) ? $rerid : array($rerid)) . '\') AND `reid`=:reid', array(":reid" => $reid));JheiN: $title = $activity['title'];yrYSj: kHdKb:oO0TP:PBYWh: $params = array();QzaDb: kgCja:GeVWY: ATnsw:tPs21: aeESj:MdTbu: if ($_W['account']['level'] > 3) {}jLn2O: B8xOy:c7v_P: if (!($_GPC['status'] != '')) {}JwJpn: $psize = 10;mmqFV: } public function doMobileGetForm() {FqyYx: $mylink = $this->createMobileUrl('mydayu_form', array("id" => $form['reid']));l7z4P: $result['id'] = $form['reid'];wOrIv: $result['html2'] = $html2;qD47f: $par = iunserializer($form['par']);YZIt0: $form = pdo_get($this->tb_form, array("weid" => $weid, "reid" => $_GPC['id']), array());zw_Rx: $weid = $_W['uniacid'];NlHNW: $result['html'] = $html;Fl_2E: $html2 = '
<div class="weui_tabbar tab-bottom">
<a href="javascript:;" class="weui_tabbar_item close-popup">
<div class="weui_tabbar_icon">
<svg class="icon" aria-hidden="true">
<use xlink:href="#icon-close"></use>
</svg>
</div>
<p class="weui_tabbar_label">关闭</p>
</a>
<a href="' . $link . '" class="weui_tabbar_item">
<div class="weui_tabbar_icon">
<svg class="icon" aria-hidden="true">
<use xlink:href="#icon-xinzeng"></use>
</svg>
</div>
<p class="weui_tabbar_label">' . $form['title'] . '</p>
</a>
<a href="' . $mylink . '" class="weui_tabbar_item">
<div class="weui_tabbar_icon">
<svg class="icon" aria-hidden="true">
<use xlink:href="#icon-jihuajindu"></use>
</svg>
</div>
<p class="weui_tabbar_label">' . $par['mname'] . '</p>
</a>
</div>
';IfPCe: $thumb = tomedia($form['thumb']);ii2l_: $link = $this->createMobileUrl('dayu_form', array("id" => $form['reid']));OWkQH: $result['mname'] = $form['mname'];a_2K3: global $_GPC, $_W;ecxXO: $html = '
<div class="weui-header bg-blue">
<div class="weui-header-left">
<a href="javascript:;" class="icon icon-109 f-white close-popup">
<svg class="icon" aria-hidden="true">
<use xlink:href="#icon-left"></use>
</svg>
</a>
</div>
<h1 class="weui-header-title">' . $form['title'] . '</h1>
</div>
<div class="weui-weixin">
<div class="weui-weixin-ui">
<div class="weui-weixin-page">
<div class="weui-weixin-img text-center"><img src="' . $thumb . '" id="image" class="center" style="width:100%;"></div>
<div class="weui-weixin-content">' . htmlspecialchars_decode($form['content']) . '</div>
</div>
</div>
</div>
';er9eN: message($result, '', 'ajax');zCAvf: } public function doMobilelist() {hWm3z: global $_W, $_GPC;Sx9hH: include $this->template('list');TJugh: $list = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_form) . ' WHERE weid = :weid and status = 1 ORDER BY reid DESC', array(":weid" => $_W['uniacid']), 'reid');ZvgHI: } public function doMobilePower() {GRSP0: $result['status'] = 0;Njp_e: U6jSt:f8ZUR: $result['status'] = 1;zIzeI: $result['msg'] = '派单成功';nyb3t: HhE4H:RmDTh: $info .= "<a href='{$url}'>点击查看详情</a>";Naq5F: $result['msg'] = '转移成功';yrF2E: mRqtW:hY7S1: message($result, '', 'ajax');Xbbcs: $data = array("kfid" => $_GPC['openid']);KS8XW: $info .= "姓名：{$content['member']}
手机：{$content['mobile']}
管理员派单
";auomW: $data = array("kf" => $_GPC['openid']);JK8p3: $CustomNotice = $acc->sendCustomNotice($custom);YpSRq: if (!($_GPC['table'] == 'manage')) {}CQ3j2:b6iRP: message($result, '', 'ajax');g1itJ: FgEIn:ntqid: F2idM:HdhKS: $activity = pdo_get($this->tb_form, array("weid" => $_W['uniacid'], "reid" => $reid), array("title", "k_templateid", "kfirst", "kfoot"));RCCW_: if ($_W['account']['level'] == ACCOUNT_SERVICE_VERIFY && !empty($activity['k_templateid'])) {}k6HUc: $result['msg'] = '派单失败';gLTkY: $this->send_template_message(urldecode(json_encode($template)));MD5H6: $reid = $_GPC['reid'];DSrrG: shySg:rpgQA: $result['msg'] = '转移失败';c0QQr:i3Odg: RZQsi:wKzcY: if (pdo_update($this->tb_form, $data, array("reid" => $reid, "weid" => $_W['uniacid'])) === false) {}TS6BS:YgdGA: $template = array("touser" => $_GPC['openid'], "template_id" => $activity['k_templateid'], "url" => $_W['siteroot'] . 'app/' . $this->createMobileUrl('manageform', array("name" => "dayu_form", "weid" => $_W['uniacid'], "op" => "detail", "id" => $reid, "rerid" => $rerid)), "topcolor" => "#FF0000", "data" => array("first" => array("value" => urlencode($activity['kfirst'] . '\\n'), "color" => "#743A3A"), "keyword1" => array("value" => urlencode($content['member']), "color" => "#000000"), "keyword2" => array("value" => urlencode($content['mobile']), "color" => "#000000"), "keyword3" => array("value" => urlencode(date('Y-m-d H:i:s', $content['createtime'])), "color" => "#000000"), "keyword4" => array("value" => urlencode('管理员派单\\n'), "color" => "#FF0000"), "remark" => array("value" => urlencode($activity['kfoot']), "color" => "#008000")));Brq2R: if (!($_GPC['table'] == 'case')) {}aFk3M: global $_GPC, $_W;r6DnN: czfa4:lT5YZ: $custom = array("msgtype" => "text", "text" => array("content" => urlencode($info)), "touser" => $_GPC['openid']);voh06: $content = pdo_get($this->tb_info, array("reid" => $reid, "rerid" => $rerid), array("member", "mobile", "createtime"));pgvId: $rerid = $_GPC['rerid'];YsZ6n: $info = "【您好，{$activity['title']} 通知】
";qFLSu: if (pdo_update($this->tb_info, $data, array("reid" => $reid, "rerid" => $rerid)) === false) {}n3spu: $result['status'] = 0;CYF01: $acc = WeAccount::create($_W['acid']);Guk1v: $result['status'] = 1;EFYZ9: $url = $_W['siteroot'] . 'app/' . $this->createMobileUrl('manageform', array("name" => "dayu_form", "weid" => $_W['uniacid'], "op" => "detail", "id" => $reid, "rerid" => $rerid));cYKy4: } public function doMobilemanageform() {TmQeY:xZfT6: zKLpd:I1mgH: igrJW:ZX4ZS: dNgAf:LwX1l: $pindex = max(1, intval($_GPC['page']));zKoyZ: qszmf:QyMek: $fids = implode(',', $fids);rUoC7: $record['status'] = intval($_GPC['status']);x20Io: $log = $activity['title'] . '-' . $activity['credit'] . '积分';B4q0U: F3xVh:XjjxQ: $rerid = array_keys($rows);lN_K7: $par = iunserializer($activity['par']);XGH6f: XTOxE:MN__m: if (!(pdo_tableexists('dayu_sendkami') && !empty($par['sendkami']) && $row['kid'])) {}E9KPj: if ($repeat == $_GPC['repeat']) {}GLJGO: $row['thumb'] = iunserializer($row['thumb']);eanFm: $info = $activity['title'] . ' 处理结果 ' . $status['name'] . '
';wrdeR: $pager = $this->pagination($total, $pindex, $psize);GX0Js: $manage_footer = 1;Ya0Lg: if (!$_W['ispost']) {}vja1t: oXKOi:GNQ1a: e4yoO:ZVQ_a: $title = $activity['title'];kF3w_: load()->func('communication');aNSp7: $ytime = date('Y-m-d H:i:s', $yuyuetime);mP3hM: $info = '【您好，受理结果通知】
';l3FT6: $params2 = array();dIXl9: if (!($_GPC['status'] == '3' && $par['icredit'] == '1')) {}Iz71M: if ($openid == $activity['kfid'] || $openid == $row['kf'] || $activity['guanli'] == '1' && $isstaff) {}koUe7: $sql = 'SELECT * FROM ' . tablename($this->tb_field) . ' WHERE `reid`=:reid ORDER BY displayorder DESC, refid DESC';StI9L: $sms_data = array("mobile" => $row['mobile'], "title" => $activity['title'], "mname" => $row['member'], "mmobile" => $row['mobile'], "openid" => $row['openid'], "status" => $status['name'], "ptname" => $_W['account']['name'], "addons" => $_W['current_module']['name'], "content" => $alldata);FxaMJ: dVkK8:hkf65: n0dtT:KKnxz: if (!empty($row)) {}eDKJe: $consultr = pdo_fetch('SELECT id,createtime FROM ' . tablename('dayu_consult') . ' WHERE infoid = :infoid AND rid = \'1\' ORDER BY createtime DESC', array(":infoid" => $row['rerid']));feIn3: $row = pdo_fetch('SELECT * FROM ' . tablename($this->tb_info) . ' WHERE rerid = :rerid', array(":rerid" => $rerid));vmtR0: $msg = '';um6gb: $record['rethumb'] = iserializer($th);BIrw2: $this->showMessage($activity['information'], $this->createMobileUrl('mydayu_form', array("id" => $reid)));QiYh8: if (!($par['sms'] != '0' && $par['paixu'] != '2' && !empty($activity['smstype']))) {}nGUBN: global $_W, $_GPC;wExEJ: if (!(!empty($par['comment']) && pdo_tableexists('dayu_comment') && !empty($row['commentid']))) {}wrqx5: $list = pdo_fetchall('SELECT s.reid, y.* FROM ' . tablename($this->tb_staff) . ' s left join ' . tablename($this->tb_form) . ' y on y.reid=s.reid WHERE y.weid=:weid AND y.status=1 AND s.openid=:openid ORDER BY y.reid DESC', array(":weid" => $weid, ":openid" => $openid), 'reid');bCO0S: $row['rethumb'] = iunserializer($row['rethumb']);W1s4r: if ($operation == 'detail') {}Znkak: ihttp_post(murl('entry', array("do" => "Notice", "id" => $activity['smstype'], "m" => "dayu_sms"), true, true), $sms_data);gcNsM: DgwoT:FbmmE: dsuMD:Yl8Cg: $sql = 'SELECT * FROM ' . tablename($this->tb_info) . " WHERE reid=:reid {$where} ORDER BY createtime DESC,rerid DESC LIMIT " . ($pindex - 1) * $psize . ',' . $psize;F1gzv: CHbMp:VlYa8: qpAny:Ws0iU: mc_notice_credit1($row['openid'], mc_openid2uid($row['openid']), $activity['credit'], $log);x5CPZ: $c_tishi = '<span class="weui-badge right" style="margin-left: 5px;position: absolute;top:5px;right:5px;">有新咨询</span>';XkDCC: $params[':reid'] = $reid;N7ApB: $info .= "<a href='{$url}'>现在去评价</a>";JA3KJ: jzlfj:R5X6M: if ($openid) {}xRi2k: $fields = pdo_fetchall($sql, $params);olZXb: $custom = array("msgtype" => "text", "text" => array("content" => urlencode($info)), "touser" => $row['openid']);H2EKE: PfJzA:Z4i7h: $childlist = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_data) . ' WHERE rerid IN (\'' . implode('\',\'', is_array($rerid) ? $rerid : array($rerid)) . '\') AND `reid`=:reid', array(":reid" => $reid));sKFzC: $settings = uni_setting($_W['uniacid'], array("creditnames", "creditbehaviors"));SJCGM: $activity = $this->get_form($reid);pDMX9: $where .= " and status={$status}";SnRs9: $acc = WeAccount::create($_W['acid']);kAtSA: $custom = array("msgtype" => "text", "text" => array("content" => urlencode($info)), "touser" => $row['openid']);vBWx_: $sql = 'SELECT * FROM ' . tablename($this->tb_data) . " WHERE `reid`=:reid AND `rerid`='{$row['rerid']}' AND `refid` IN ({$fids})";fErc6: $huifu = $status['name'] . $kfinfo . $revoice;FPnHJ: o8ENS:IqT0M: $info .= "姓名：{$row['member']}
手机：{$row['mobile']}
受理结果：{$huifu}
";HhzVB: YgCjq:r6CEE: if (!(!empty($consult['id']) && $consult['createtime'] > $consultr['createtime'])) {}X7u0o: N3yX5:Ioo0L: $outurl = !empty($par['noticeurl']) ? $par['noticeurl'] : $_W['siteroot'] . 'app/' . $this->createMobileUrl('mydayu_form', array("op" => "detail", "rerid" => $rerid, "id" => $row['reid']));WOhKU: if (!is_error($acc)) {}eq47R: $dayu_form = pdo_fetch('SELECT * FROM ' . tablename($this->tb_form) . ' WHERE reid = :reid', array(":reid" => $row['reid']));gJ2gw: BujQo:omGSR: $row['fields'] = array();LGPaJ: $linkage = iunserializer($row['linkage']);kPdVD: tjyB1:vpjSU: $isstaff = pdo_get($this->tb_staff, array("weid" => $weid, "openid" => $openid), array("id"));HplvK: $record = array();dZmNy: foreach ($rows as $key => $val) {PvfPM: VuTJC:u5mbN: $rows[$key]['kf'] = mc_fansinfo($val['kf'], $acid, $weid);JHLZE: $rows[$key]['status'] = $this->get_status($val['reid'], $val['status']);hSJJM: $rows[$key]['user'] = mc_fansinfo($val['openid'], $acid, $weid);sNQpt: $rows[$key]['comment'] = !empty($par['comment']) && pdo_tableexists('dayu_comment') && !empty($val['commentid']) ? $this->get_comment($val['commentid']) : '';b1Awr: }QqdIL:aNDpf: $formdata = $this->order_foreach($row['reid'], $rerid);rs7n8: foreach ($childlist as $reply => $r) {FGY8M: $children[$r['rerid']][] = $r;u34L5: OO4Qi:YPMXs: if (empty($r['rerid'])) {}Bs_Uh: yWrwF:fDtF8: unset($children[$reply]);ajndp: }Fdl0w: load()->func('tpl');iFXY_: TS2Kq:mzJSR: if (!($activity['guanli'] == '0')) {}eRvSL: if ($isstaff) {}Ri_iP: XNaoe:u1YMp: $state = array();TzmKP: $params2[':openid'] = $openid;t4bin: $record['kf'] = $openid;vcUr0: abEbn:CZ1ay: if (!pdo_tableexists('dayu_consult')) {}UpHpo: if ($dayu_form['custom_status'] == 1) {}uKeJN: OaMcW:DLLtJ: $behavior = $settings['creditbehaviors'];LMBHI: $this->showMessage('记录不存在或是已经被删除！');UsWp9: XPxMd:UEUv5: $revoice = !empty($_GPC['revoice']) ? '\\n有语音答复' : '';bT9CT: foreach ($fields as $f) {uaJzz: $ds[$f['refid']]['loc'] = $f['loc'];A5aYY: $ds[$f['refid']]['type'] = $f['type'];sZdC9: $ds[$f['refid']]['fid'] = $f['title'];K1g8V: $ds[$f['refid']]['refid'] = $f['refid'];kJdxb: JRmij:zfpVI: $fids[] = $f['refid'];JXe3_: }HdnjE: WtTzv:Na4gp: $where2 = 'weid=:weid and status = 1';GjUzh: $url = $outurl;qUnQh: $acc = WeAccount::create($_W['acid']);slZMs: $row['voices'] = $row['voice'];ulSh0: $record['yuyuetime'] = TIMESTAMP;kVULv: require MODULE_ROOT . '/fans.mobile.php';cVJ1J: $status = $this->get_status($row['reid'], $row['status']);s1T6k: LPzq3:OQs3r: $children = array();HMkCg: qy1oK:FX6ry: $params[':reid'] = $row['reid'];S0EB3: $template = array("touser" => $row['openid'], "template_id" => $dayu_form['m_templateid'], "url" => $outurl, "topcolor" => "#FF0000", "data" => array("first" => array("value" => urlencode($dayu_form['mfirst']), "color" => "#743A3A"), "keyword1" => array("value" => urlencode($row['member']), "color" => "#000000"), "keyword2" => array("value" => urlencode($row['mobile']), "color" => "#000000"), "keyword3" => array("value" => urlencode(date('Y-m-d H:i:s', TIMESTAMP)), "color" => "#000000"), "keyword4" => array("value" => urlencode($huifu), "color" => "#FF0000"), "remark" => array("value" => urlencode($dayu_form['mfoot']), "color" => "#008000")));zD8Vg: foreach ($ds as $value) {LW36x: htJ1I:LpwND: lAnBA:BEuJk: foreach ($fdatas as $fdata) {YLpDp: $row['fields'][$value['refid']] .= $fdata['data'];o3_vq: if (!($fdata['refid'] == $value['refid'])) {}CVHlK: HYKSf:ZI4hP: A3axq:QvwCd: }dz2mw:mB7Ob: MyxLM:oDcsp: if (!($value['type'] == 'reside')) {}TVi1c: $row['fields'][$value['refid']] = '';k6a9O: }udSfS: if (!($row['icredit'] != '1' && $par['icredit'] == '1' && $activity['credit'] != '0.00' && $_GPC['status'] == '3')) {}oJbXN: QKp40:qu1yb: if (!empty($repeat)) {}tvhsx: $info .= "{$par['commenttitle']}
";kC3Ol:xsf19: if (empty($row['rethumb'])) {}p_G_L: $this->showMessage('非法访问，空');WcDLP:JF0n9: pdo_update('dayu_form_info', $record, array("rerid" => $rerid));ERvAc:aDMVF: if (!empty($fields)) {}G301c: $status = $_GPC['status'];gRwlv: include $this->template('manage_form');RnlHW: setcookie('r_submit', $_GPC['repeat']);PbRj9: ghBt5:ntPsc: $linkage['l2'] = $this->get_linkage($linkage['l2'], '');qJouj: return error(-1, $acc['message']);TvliR: IvVmv:VMIqo: foreach ($formdata as $index => $v) { $alldata[] = $v['title'] . ':' . $v['data'] . ','; qym8n: }DWwHK: C22YC:om3QC: $record['kfinfo'] = $_GPC['kfinfo'];AaxeA: $where2 .= ' and kfid = :openid';SRwyL: $info .= "<a href='{$url}'>点击查看详情</a>";L6_WA: if ($operation == 'display') {}T66px: $this->showMessage('非法访问！你不是管理员。', $this->createMobileUrl('index'), 'info');eOS0V: pslzp:laAV4: if (!(!empty($par['wxcard']) && !empty($_GPC['wxcard']) && pdo_tableexists('dayu_wxcard'))) {}eDaXS: NPPmu:wmb_G: $kfinfo = !empty($record['kfinfo']) ? '\\n客服回复：' . $record['kfinfo'] : '';S0kbv: if (!(!empty($par['wxcard']) && pdo_tableexists('dayu_wxcard_activity'))) {}ZRwef: b3sNT:BAh6w: mc_group_update(mc_openid2uid($row['openid']));V1Ypl: $repeat = $_COOKIE['r_submit'];H7ihU: $yuyuetime = !empty($row['yuyuetime']) ? date('Y-m-d H:i', $row['yuyuetime']) : date('Y-m-d H:i', TIMESTAMP);WdsMs: $linkage['l1'] = $this->get_linkage($linkage['l1'], '');zTXJQ: $this->send_template_message(urldecode(json_encode($template)));PZCf5: $face = mc_fansinfo($row['openid'], $acid, $weid);oUm9G: S7PxR:i8jnh: if (!is_array($linkage)) {}AuyuG: $where .= ' and ( status=2 or status=-1 )';PasI2: $record['icredit'] = 1;Hixoq: $kami = pdo_get('dayu_sendkami', array("weid" => $weid, "id" => $row['kid']), array());Nj027: $row['yuyuetime'] = !empty($row['yuyuetime']) ? date('Y年m月d日 H:i', $row['yuyuetime']) : '客服尚未受理';SK8d4: kMmPe:zf4a0: $custom = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_custom) . ' WHERE weid = :weid ORDER BY displayorder DESC', array(":weid" => $weid));B_auk: $la = iunserializer($activity['linkage']);x8i85: if (!($openid != $activity['kfid'] && $activity['guanli'] == '0')) {}Tl0VD: foreach ($list as $key => $val) { $list[$key]['count'] = $this->count_form($val['reid'], 2); jefYi: }rKEZP:q12DT: if (!is_error($acc)) {}WPTvW: if (!(pdo_tableexists('dayu_kami') && $row['kid'])) {}piQ1p: $operation = !empty($_GPC['op']) ? $_GPC['op'] : 'display';O4sWc: OygxN:ZAyfU: $wxcard = pdo_fetchall('SELECT * FROM ' . tablename('dayu_wxcard') . ' WHERE weid = :weid AND cid=:cid AND status=1', array(":weid" => $_W['uniacid'], ":cid" => $par['wxcard']));dAtxh: yT5EW:Z62xo: $rerid = intval($_GPC['rerid']);qGX8M: $comment = pdo_get('dayu_comment', array("weid" => $weid, "id" => $row['commentid']), array());BZdwg: $titles = $activity['title'];LBGtw: SygdE:l1UXu: $rows = pdo_fetchall($sql, $params, 'rerid');Z13iQ: setcookie('r_submit', $_GPC['repeat']);CeN9C: N8fl1:LH7Mf: $params = array();dkxEy: if (!($status != '')) {}afw8D: if (!is_array($wxcard)) {}pcI_T: $acc = notice_init();CnqSH: $row['user'] = mc_fansinfo($row['openid'], $acid, $weid);lr9ww: $this->showMessage('非法访问！你不是管理员。');tu7oP: if ($reid) {}y3fho: FFOIg:fRdSh: $this->showMessage('修改成功' . $msg, referer(), 'success');zl_95: $CustomNotice = $acc->sendCustomNotice($custom);n05Nw: $wxcard_post = ihttp_post(murl('entry', array("do" => "recorded", "couponid" => $_GPC['wxcard'], "m" => "dayu_wxcard"), true, true), array("addons" => $_W['current_module']['name'], "openid" => $row['openid'], "infoid" => $rerid));klc0t: $ds = $fids = array();KsCOR: if (empty($_GPC['repeat'])) {}ciE6V: if ($status == 2) {}J1Lq5: $alldata = array();Wlkit: $acc = notice_init();siCVF: $staff = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_staff) . ' WHERE reid = :reid ORDER BY `id` DESC', array(":reid" => $reid));vDIF2: njEp1:mFnt3: $total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_info) . " WHERE reid = :reid {$where} ", $params);m8bQu: if (!(pdo_tableexists('dayu_comment') && !empty($par['comment']) && empty($row['commentid']) && $_GPC['status'] == '3')) {}Usggj: PMaeM:pOtcq: foreach ($_GPC['thumb'] as $thumb) { $th[] = tomedia($thumb); DIqen: }p2MdJ: foreach ($arr2 as $index => $v) { $state[$v][] = $this->get_status($reid, $v); BZz3t: }qADJM: $status = $this->get_status($reid, $_GPC['status']);D0lNc: $url = $outurl;ksqtX:hIOUl: $setting = $this->module['config'];v43oS: XJrHV:OOOke: $reid = intval($_GPC['id']);QEGrh: $params2[':weid'] = $weid;S6qyN: mc_credit_update(mc_openid2uid($row['openid']), $behavior['activity'], $activity['credit'], array(0, $activity['title']));U0Cj5: $dayu_form['content'] = htmlspecialchars_decode($dayu_form['content']);ypSap: $arr2 = array("0", "1", "2", "3", "8");I6fWE: $picker = 1;gK0iP: $fdatas = pdo_fetchall($sql, $params);vOnmx: $where .= " and kf='{$openid}'";eQ2px: $row['file'] = iunserializer($row['file']);pi6MH: wALdA:j0iLN: return error(-1, $acc['message']);kCYdt: $msg .= $wxcard_post['msg'];nCEbM: hL7qx:TnsKw: $params = array();Zwo1v: $consult = pdo_fetch('SELECT id,createtime FROM ' . tablename('dayu_consult') . ' WHERE infoid = :infoid AND rid = \'0\' ORDER BY createtime DESC', array(":infoid" => $row['rerid']));U8jCM: SqKNb:NMPmt: $psize = 10;ShLQE: foreach ($wxcard as &$val) { $val['coupon'] = pdo_get('coupon', array("uniacid" => $_W['uniacid'], "card_id" => $val['cardid']), array("id", "title")); s3aGw: }ESfjX: $record['revoice'] = empty($row['revoice']) ? $_GPC['revoice'] : $row['revoice'];a5gPe: vQYbg:vwjiA: $kami = pdo_get('dayu_kami', array("weid" => $_W['uniacid'], "id" => $row['kid']), array());S2M85: $list = pdo_fetchall('SELECT * FROM ' . tablename($this->tb_form) . " WHERE {$where2} ORDER BY reid DESC", $params2, 'reid');JeF_A: $this->showMessage('非法访问.');Oxcjz: $CustomNotice = $acc->sendCustomNotice($custom);u_CZE: if (!is_array($_GPC['thumb'])) {}KZ34G: $row['createtime'] = !empty($row['createtime']) ? date('Y年m月d日 H:i', $row['createtime']) : '时间丢失';qpA7B: $row['member'] = !empty($row['member']) ? $row['member'] : $row['user']['nickname'];pNksi: foreach ($fdatas as $fd) { $row['fields'][$fd['refid']] = $fd['data']; ZpsY0: }oulT2: } public function isHy($openid) {MTNMu:kogYe: PvL_T:mgPsY: global $_W;Enzlq: if (empty($card)) {}pwDAD: load()->model('mc');WMlu7: return false;Omg7U: $card = pdo_fetch('SELECT * FROM ' . tablename('mc_card_members') . ' WHERE uniacid=:uniacid AND openid = :openid ', array(":uniacid" => $_W['uniacid'], ":openid" => $openid));JaNTE: NDEpG:f9bXO: return true;Vfyh5: } public function send_template_message($data) {Nt30q: load()->func('communication');wxhuG: $url = 'https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=' . $access_token;lhjlz: return error(-1, "访问公众平台接口失败, 错误: {$response['message']}");Gmzm0: KBn11:OXLsb: return error(-1, "访问微信接口错误, 错误代码: {$result['errcode']}, 错误信息: {$result['errmsg']},信息详情：{$this->error_code($result['errcode'], $result['errmsg'])}");m5k0U: dm2b6:ay27J: if (empty($result)) {}EawOn:Jp00o: if (!empty($result['errcode'])) {}FoNFc: crgsA:fqLaG: load()->classs('weixin.account');x9Dp1: afPGd:Pabor:UtA5u: return error(-1, "接口调用失败, 原数据: {$response['meta']}");mxbF5: global $_W, $_GPC;glC_2: return true;ixtgc: $result = @json_decode($response['content'], true);ZQ3LS: if (!is_error($response)) {}cZcy3: $response = ihttp_request($url, $data);BkQwT: $access_token = WeAccount::token();x4MB4: } public function AjaxMessage($msg, $status = 0) {rCFwb: $result = array("message" => $msg, "status" => $status);yoXW1: echo json_encode($result);uZ_zu: exit;KMzOw: } public function doMobilechangeAjax() {eXuyL: $params[':reid'] = $reid;QvZeu: $params = array();jn710: $id = intval($_GPC['id']);ZOSC0: $acc = WeAccount::create($_W['acid']);oTKmJ: pdo_update('dayu_form_info', $data, array("rerid" => $id));baBCs: $data = array("status" => $status);jjOOA: $data = array("first" => array("value" => $activity['mfirst'] . '
', "color" => "#743A3A"), "keyword1" => array("value" => $row['member']), "keyword2" => array("value" => $row['mobile']), "keyword3" => array("value" => date('Y-m-d H:i:s', TIMESTAMP)), "keyword4" => array("value" => $activity['state3']), "remark" => array("value" => '
' . $activity['mfoot'], "color" => "#008000"));LJfRT: if (!empty($id)) {}RoPw5: $url = !empty($par['noticeurl']) ? $par['noticeurl'] : $_W['siteroot'] . 'app/' . $this->createMobileUrl('mydayu_form', array("op" => "detail", "rerid" => $id, "id" => $reid));amtlL: $acc->sendTplNotice($row['openid'], $activity['m_templateid'], $data, $url, '#FF0000');OOVpQ: $status = $_GPC['status'];L2uxz: $this->AjaxMessage('更新成功!', 1);DsaFb: $activity = pdo_fetch($sql, $params);R6QQq: $reid = intval($_GPC['reid']);PZSjx: LtGdl:hFCz_: $par = iunserializer($activity['par']);lTE_M: $row = pdo_fetch('SELECT * FROM ' . tablename($this->tb_info) . ' WHERE rerid = :rerid', array(":rerid" => $id));jFaDQ: $this->AjaxMessage('更新失败!', 0);HxOVU:r4Sly: QsKqQ:cYFsb: global $_W, $_GPC;JmUCi: $params[':weid'] = $_W['uniacid'];vUzhg: $sql = 'SELECT * FROM ' . tablename($this->tb_form) . ' WHERE `weid`=:weid AND `reid`=:reid';HPv1Z: } public function doMobileLocate() {UI3m6: O9k_V:QXRHL: $result['message'] = '';ggU9b: $result['lat'] = $setting['contact']['lat'];cqLGk: $result['name'] = $setting['contact']['company'];EEQjK: $result['lng'] = $setting['contact']['lng'];oAggv: if (!($_GPC['op'] == 'contact')) {}QG1qu: require MODULE_ROOT . '/fans.mobile.php';ypmKa: global $_W, $_GPC;lW8Fl: $result = array("error" => "error", "message" => "", "data" => "");Luyg5: $result['mobile'] = $setting['contact']['mobile'];lfHLp: die(json_encode($result));Klk2G: $result['address'] = $setting['contact']['province'] . $setting['contact']['city'] . $setting['contact']['district'] . $setting['contact']['address'];ymVtN: } function pagination($tcount, $pindex, $psize = 15, $url = "", $context = array("before" => 5, "after" => 4, "ajaxcallback" => "")) {cu7Ga: $html .= "<div class=\"pager-first\"><a {$pdata['faa']} class=\"pager-nav\">首页</a></div>";oI9Ov: $pdata['tcount'] = $tcount;PMF_Y: $html .= "<div class=\"pager-next\"><a {$pdata['naa']} class=\"pager-nav\">下一页</a></div>";ANjce: H1bCE:pq8Ba: $html .= '<div class="pager-left">';mW9pD: $_GET['page'] = $pdata['lindex'];HGrg7: if ($pdata['cindex'] < $pdata['tpage']) {}DS8HH: $pdata['naa'] = 'href="?' . str_replace('*', $pdata['nindex'], $url) . '"';ZInK3: xCDj2:amzV1: $html = '<div class="pager">';qz8KL: $pdata['paa'] = 'href="javascript:;" onclick="p(\'' . $_W['script_name'] . $url . '\', \'' . $pdata['pindex'] . '\', ' . $context['ajaxcallback'] . ')"';IzvYQ: $pdata['cindex'] = $cindex;mUnRq: nSq2I:IY16y: $html .= "<div class=\"pager-pre\"><a {$pdata['paa']}>上一页</a></div>";FiW63: $pdata['nindex'] = $cindex < $pdata['tpage'] ? $cindex + 1 : $pdata['tpage'];Jcz7n: if ($context['isajax']) {}ljsrl: global $_W;Fqdou: $cindex = min($cindex, $pdata['tpage']);EWZt2: $pdata['faa'] = 'href="javascript:;" onclick="p(\'' . $_W['script_name'] . $url . '\', \'' . $pdata['findex'] . '\', ' . $context['ajaxcallback'] . ')"';oEgn1: HJzT8:PlTa_: $pdata['paa'] = 'href="?' . str_replace('*', $pdata['pindex'], $url) . '"';L3Vq3: $pdata['laa'] = 'href="?' . str_replace('*', $pdata['lindex'], $url) . '"';N3o0f: if (!($pdata['tpage'] <= 1)) {}pmPCU: if ($url) {}rzOSO:S6bmd: return $html;HrKf7: $cindex = $pindex;dw9Zc: $pdata['pindex'] = $cindex > 1 ? $cindex - 1 : 1;OwJld: $pdata['laa'] = 'href="javascript:;" onclick="p(\'' . $_W['script_name'] . $url . '\', \'' . $pdata['lindex'] . '\', ' . $context['ajaxcallback'] . ')"';pizJ9: s45hh:I1sej: BBJQ1:ZcXhl: $html .= '<div class="pager-pre" style="width:100%"><a href="###">第一页</a></div>';d7VNS:yrBEa: RGtoG:FLeE9: $pdata['findex'] = 1;XECrq: $pdata['naa'] = 'href="' . $_W['script_name'] . '?' . http_build_query($_GET) . '"';XGzMJ:oasAJ: $html .= '<div class="pager-left">';pEyXH: return '';x3qkk: $html .= '<div class="pager-right">';bBBA6: if ($url) {}LRrJf: $url = $_W['script_name'] . '?' . http_build_query($_GET);X7E4F:hCazh: $html .= "<div class=\"pager-cen\">{$pindex} / " . $pdata['tpage'] . '</div>';E5Bwd: if (!$context['ajaxcallback']) {}I3mQ6: brVuA:ytilz: $pdata['naa'] = 'href="javascript:;" onclick="p(\'' . $_W['script_name'] . $url . '\', \'' . $pdata['nindex'] . '\', ' . $context['ajaxcallback'] . ')"';KcvuK: b3dDn:h_FDh: $pdata['laa'] = 'href="' . $_W['script_name'] . '?' . http_build_query($_GET) . '"';HqKu0: $html .= '</div>';Qz4CF: $_GET['page'] = $pdata['findex'];RzTVX: $pdata['faa'] = 'href="' . $_W['script_name'] . '?' . http_build_query($_GET) . '"';zRZ8c: $cindex = max($cindex, 1);gBx7u: $pdata['faa'] = 'href="?' . str_replace('*', $pdata['findex'], $url) . '"';GN62i: $pdata['paa'] = 'href="' . $_W['script_name'] . '?' . http_build_query($_GET) . '"';XGQ2u: if ($pdata['cindex'] > 1) {}Ih29c: $_GET['page'] = $pdata['nindex'];Xayv7: $html .= '</div>';qxpdF: $html .= '</div>';Z0gQ2: $pdata = array("tcount" => 0, "tpage" => 0, "cindex" => 0, "findex" => 0, "pindex" => 0, "nindex" => 0, "lindex" => 0, "options" => "");YQHoL: $html .= '<div class="clr"></div></div>';CoHBg: $html .= '<div class="pager-next" style="width:100%"><a href="###">尾页</a></div>';oq3dW: $_GET['page'] = $pdata['pindex'];Tfwid: $html .= '<div class="pager-right">';azxjN: $context['isajax'] = true;tp7JA: IAHtl:Py3Xy: $pdata['tpage'] = ceil($tcount / $psize);T2nnS: vXrGi:UbVyH: $html .= "<div class=\"pager-end\"><a {$pdata['laa']} class=\"pager-nav\">尾页</a></div>";l4Cr1: $html .= '</div>';EquOx: $pdata['lindex'] = $pdata['tpage'];lyMYd: } public function doMobileFansUs() {ESsuI: include $this->template('fans_us');AjW12: $qrcodesrc = tomedia('qrcode_' . $_W['acid'] . '.jpg');ECwd0: global $_W, $_GPC;cDIbL: require MODULE_ROOT . '/fans.mobile.php';eYx9N: } public function getFollow() {VERmT: if (intval($p['follow']) == 0) {}Y3FM5: require MODULE_ROOT . '/fans.mobile.php';Azcmy: header('Location: ' . $this->createMobileUrl('FansUs'), true, 301);dSmqy: $p = pdo_fetch('SELECT follow FROM ' . tablename('mc_mapping_fans') . ' WHERE uniacid = :weid AND openid = :openid LIMIT 1', array(":weid" => $_W['uniacid'], ":openid" => $_W['openid']));EdABR: return true;SWTfM: RQyA1:p1uHB: global $_GPC, $_W;B9bfv:HjzqW: oBBfV:HF2HG: } private function checkauth3($openid, $nickname, $headimgurl) {d4hjD: $default_groupid = pdo_fetchcolumn('SELECT groupid FROM ' . tablename('mc_groups') . ' WHERE uniacid = :uniacid AND isdefault = 1', array(":uniacid" => $_W['uniacid']));Z1hLr: $fanid = pdo_insertid();RXYx9: $post = array("acid" => $_W['acid'], "uniacid" => $_W['uniacid'], "nickname" => $nickname, "openid" => $_W['fans']['openid'], "salt" => random(8), "follow" => 0, "updatetime" => TIMESTAMP, "tag" => base64_encode(iserializer($_W['fans'])));fiwSF: nKScv:FL4pU:IqMmc: if (empty($fan['uid'])) {}KjKlb: if (empty($_W['member']['uid']) && empty($settings['passport']['focusreg'])) {}bfpYc: $_W['member']['uid'] = $fan['uid'];SyrFs: $fanid = $fan['fanid'];P5khR: a56Lu:kHUL9: Rq6Cb:WIWum: $uid = pdo_insertid();cUkBo: pdo_insert('mc_mapping_fans', $post);M97oS: $data = array("uniacid" => $_W['uniacid'], "email" => $email, "salt" => random(8), "groupid" => $default_groupid, "createtime" => TIMESTAMP, "password" => md5($message['from'] . $data['salt'] . $_W['config']['setting']['authkey']), "avatar" => $headimgurl, "nickname" => $nickname);UAKoo: $fan = pdo_get('mc_mapping_fans', array("uniacid" => $_W['uniacid'], "openid" => $openid));b2VEt: global $_W, $engine;Uz_dv: $settings = cache_load('unisetting:' . $_W['uniacid']);SmaxQ:CYKgw: rMx9R:chM2f: $_W['fans']['uid'] = $fan['uid'];rhdKe: $email = md5($oauth['openid']) . '@vqiyi.cn';hBMfy: aWAXE:X5F3S: pdo_update('mc_mapping_fans', array("uid" => $uid), array("fanid" => $fanid));GWKs2: WK6VG:ZDEIC: $_W['fans']['uid'] = $uid;etuNK: checkauth();uYi4s: if (!empty($fan)) {}WJaPH: pdo_insert('mc_members', $data);MdLvY:ZNrc3: $_W['member']['uid'] = $uid;z7y21: } private function checkAuth2() {Zo6eI:gk2fY: $_W['member']['uid'] = $fan['uid'];KRADB: $_W['member']['uid'] = $uid;tl1Uz: CqWby:aXx0w: $uid = pdo_insertid();E5AlX: weKuR:VuRO8:Y9cEw: $setting = cache_load('unisetting:' . $_W['uniacid']);PFE9n: $_W['fans']['uid'] = $uid;zwySq: if (empty($_W['member']['uid']) && empty($setting['passport']['focusreg'])) {}iB9P_: pdo_insert('mc_members', array("uniacid" => $_W['uniacid']));HE1QO: pdo_update('mc_mapping_fans', array("uid" => $uid), array("fanid" => $fanid));kbFrd: isnxQ:QG2mQ: $fan = pdo_get('mc_mapping_fans', array("uniacid" => $_W['uniacid'], "openid" => $_W['openid']));qTLi6: oc3zR:nuF2z: $_W['fans']['uid'] = $fan['uid'];ZwKsN: $post = array("acid" => $_W['acid'], "uniacid" => $_W['uniacid'], "openid" => $_W['openid'], "updatetime" => time(), "follow" => 0);KFSKr: if (empty($fan['uid'])) {}AExW8: if (!empty($fan)) {}ie4Kq:lzXG3: checkauth();DiqeL: r1u5Q:I8cQo: Pon7q:W6Lo_: $fanid = $fan['fanid'];osh_v: global $_W;GtRRS: } public function get_fields($fid) { global $_GPC, $_W; return pdo_get($this->tb_field, array("refid" => $fid), array()); } public function get_comment($commentid) { global $_GPC, $_W; return pdo_get('dayu_comment', array("weid" => $_W['uniacid'], "id" => $commentid), array()); } public function get_linkage($id, $type) {dK3Db: if ($type == 1) {}el2vx:Q9yMv: bU3C9:l1wZT: return pdo_fetch('SELECT * FROM ' . tablename($this->tb_linkage) . ' WHERE id = :id LIMIT 1', array(":id" => $id));vj6Fv: RoT_Z:fpOHg: return pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename($this->tb_linkage) . ' WHERE reid = :reid', array(":reid" => $id));SO0xh: } public function get_role($uid) { global $_GPC, $_W; return pdo_fetch('SELECT username, uid FROM ' . tablename('users') . ' WHERE uid = :uid LIMIT 1', array(":uid" => $uid)); } public function get_isrole($reid, $uid) { global $_GPC, $_W; return pdo_fetch('SELECT * FROM ' . tablename($this->tb_role) . ' WHERE weid = :weid AND reid = :reid AND roleid = :uid LIMIT 1', array(":weid" => $_W['uniacid'], ":reid" => $reid, ":uid" => $uid)); } public function doMobileUpthumb() {BAWSz: $result['msg'] = '更新头像失败';bRUDx: qvmqS:KIxhd: bmPL4:Xygwz: message($result, '', 'ajax');XhrDI: $result['msg'] = '更新头像成功';VCFN0: $result['status'] = 'error';sjT3g:h4HH3: if (!($mode == 'member')) {}koLQp: global $_W, $_GPC;vFbz1: $result['status'] = 'success';JW6iL: if (mc_update($_GPC['uid'], $data) === false) {}ofokQ: $mode = $_GPC['mode'];jAT_9: jcf36:Ptc2d: load()->model('mc');n3jX1: $data = array("avatar" => $_GPC['thumb']);bif2h: } public function doMobileUploadFiles() {g9Atr: global $_GPC, $_W;FcZ6C: message($result, '', 'ajax');Pn1dU: $result['msg'] = $pathname . '上传成功';TljGt: load()->func('file');A67hy: foreach ($_FILES as $key => $files) {R_7PA: $size = intval($files['size']);vAHgM: $result['status'] = '0';syM2u: if (!($files['error'] != 0)) {}yLUN1: message($result, '', 'ajax');idQvl: $result['msg'] = '上传失败, 请选择要上传的文件！';rNmC3: exit;goTOg: $ext = strtolower($ext);jvu4L: if (!is_error($file)) {}KcfQZ: kRJU2:ePSv8: $pathname = $file['path'];dinV8: $file = file_upload($files);ukA2R: message($result, '', 'ajax');k7qPI: $result['message'] = $file['message'];DWTQA: if (!empty($files['name'])) {}aEILk: $originname = $files['name'];KQdfC: exit;mxwxT: $result['msg'] = '上传失败, 请重试.';C4W6s: PY5Nj:l4PHp: Qv3PD:XEj58: $result['status'] = '0';aqxq9: die(json_encode($result));j0K5h: $ext = pathinfo($files['name'], PATHINFO_EXTENSION);ZMyXP: qMHzF:fDRe5: }EETdn: XgTlW:vklie: $result['status'] = '1';j3h0k: } }e_1fv: function tpl_form_field_images($name, $value = "", $default = "", $options = array()) {hZhD1: if (empty($value)) {}PbA1_: YzOSn:DVRGd: if (!empty($options['class_extra'])) {}SBgME: zAR1C:McTGD: exit('图片上传目录错误,只能指定最多两级目录,如: "store","store/d1"');VcE7k: $options['global'] = false;RIkrd:LhZjv: if (!(isset($options['dest_dir']) && !empty($options['dest_dir']))) {}RvBXM: l4Yct:of0fB: if (!empty($default)) {}QDRzE: $options['class_extra'] = '';Ki3cL: ACICD:Vy4Xz: $s .= '
<div class="input-group ' . $options['class_extra'] . '">
<input type="text" name="' . $name . '" value="' . $value . '"' . ($options['extras']['text'] ? $options['extras']['text'] : '') . ' id="re-image" class="form-control" autocomplete="off">
<span class="input-group-btn">
<button class="btn btn-default" type="button" onclick="showImageDialog(this);">选择图片</button>
</span>
</div>
<div class="col-xs-12 ' . $options['class_extra'] . '" style="margin-top:.5em;">
<em class="close" style="position:absolute; top: 0px; right: -14px;font-size:18px;color:#333;" title="删除这张图片" onclick="deleteImage(this)">× 删除</em>
</div>';uQh6K: $val = tomedia($value);LRj_i: $options['thumb'] = !empty($options['thumb']);pQtSa: return $s;OXMPz: $options['global'] = true;r08fA: $val = $default;YXQYd: $options['direct'] = true;ji97n: if (preg_match('/^\\w+([\\/]\\w+)?$/i', $options['dest_dir'])) {}YHi6Z: ovtem:DZlc9: define('TPL_INIT_IMAGE', true);qzz9K: ofmDI:nD8Dw: nHvo9:qgM85: $s = '
<script type=\"text/javascript\">
function showImageDialog(elm, opts, options) {
        require([\"util\"], function(util){
            var btn = $(elm);
            var ipt = btn.parent().prev();
            var val = ipt.val();
            var img = ipt.parent().next().children();
            options = ' . str_replace('"', '\'', json_encode($options)) . ';
            util.image(val, function(url){
                if(url.url){
                        if(img.length > 0){
                                img.get(0).src = url.url;
                            }
                            ipt.val(url.attachment);
                            ipt.attr(\"filename\",url.filename);
                            ipt.attr(\"url\",url.url);
                        }
                        if(url.media_id){
                                if(img.length > 0){
                                        img.get(0).src = \"\";
                                    }
                                    ipt.val(url.media_id);
                                }
                            }, null, options);
                        });
                    }
                    function deleteImage(elm){
                            require([\"jquery\"], function($){
                                $(elm).prev().attr(\"src\", \"./resource/images/nopic.jpg\");
                                $(elm).parent().prev().find(\"input\").val(\"\");
                            });
                        }
                        </script>';AV044: if (!empty($options['global'])) {}I7bdq: $default = './resource/images/nopic.jpg';xrYhp: $s = '';hsXA7: if (!isset($options['thumb'])) {}V9POD: global $_W;y5Ymi: $options['multiple'] = false;Ra9g3: hOdOu:eTvKr: tyVyI:qnWuE: if (defined('TPL_INIT_IMAGE')) {}kMfDD: } ?>